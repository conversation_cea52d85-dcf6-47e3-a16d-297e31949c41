{"version": 3, "targets": {"net9.0-windows7.0": {"FluentValidation/11.9.0": {"type": "package", "compile": {"lib/net8.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/FluentValidation.dll": {"related": ".xml"}}}, "MaterialDesignColors/5.2.1": {"type": "package", "compile": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/5.2.1": {"type": "package", "dependencies": {"MaterialDesignColors": "5.2.1", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.Data.Sqlite/9.0.6": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.6", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.Data.Sqlite.Core/9.0.6": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NLog/5.2.8": {"type": "package", "compile": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}}, "OxyPlot.Core/2.2.0": {"type": "package", "compile": {"lib/net8.0/OxyPlot.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OxyPlot.dll": {"related": ".xml"}}}, "OxyPlot.Wpf/2.2.0": {"type": "package", "dependencies": {"OxyPlot.Core": "2.2.0", "OxyPlot.Wpf.Shared": "2.2.0"}, "compile": {"lib/net8.0-windows7.0/OxyPlot.Wpf.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/OxyPlot.Wpf.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "OxyPlot.Wpf.Shared/2.2.0": {"type": "package", "dependencies": {"OxyPlot.Core": "2.2.0"}, "compile": {"lib/net8.0-windows7.0/OxyPlot.Wpf.Shared.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/OxyPlot.Wpf.Shared.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}, "runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"assetType": "native", "rid": "browser-wasm"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-s390x"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-ppc64le"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-s390x"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Drawing.Common/8.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "compile": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Json/8.0.5": {"type": "package", "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}}}, "libraries": {"FluentValidation/11.9.0": {"sha512": "VneVlTvwYDkfHV5av3QrQ0amALgrLX6LV94wlYyEsh0B/klJBW7C8y2eAtj5tOZ3jH6CAVpr4s1ZGgew/QWyig==", "type": "package", "path": "fluentvalidation/11.9.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.11.9.0.nupkg.sha512", "fluentvalidation.nuspec", "lib/net5.0/FluentValidation.dll", "lib/net5.0/FluentValidation.xml", "lib/net6.0/FluentValidation.dll", "lib/net6.0/FluentValidation.xml", "lib/net7.0/FluentValidation.dll", "lib/net7.0/FluentValidation.xml", "lib/net8.0/FluentValidation.dll", "lib/net8.0/FluentValidation.xml", "lib/netstandard2.0/FluentValidation.dll", "lib/netstandard2.0/FluentValidation.xml", "lib/netstandard2.1/FluentValidation.dll", "lib/netstandard2.1/FluentValidation.xml"]}, "MaterialDesignColors/5.2.1": {"sha512": "D0HW6E2/kzsnEWCh1KDG/K09Fpkvs9mR3n91Y8YSOsEAoQmGZbVAj58ssyAxGTiIPj2zB4ZVnwxkizwO35/v8A==", "type": "package", "path": "materialdesigncolors/5.2.1", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/net8.0/MaterialDesignColors.dll", "lib/net8.0/MaterialDesignColors.pdb", "materialdesigncolors.5.2.1.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/5.2.1": {"sha512": "x8JDqNHJcTLLxIoVts3w7AbSq5Zo0FXTw89XqPN7+n0EKqLXFwWsywiUn08HDyTGAmZVJqbQsWKxKWCI8qfWsQ==", "type": "package", "path": "materialdesignthemes/5.2.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "docs/README.md", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/net8.0/MaterialDesignThemes.Wpf.dll", "lib/net8.0/MaterialDesignThemes.Wpf.pdb", "lib/net8.0/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.5.2.1.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.Data.Sqlite/9.0.6": {"sha512": "JtLL15uSBqqkDs6mhRc4rUbNPaJJCgN+gGQfLs+paD600td6V6YGgOreXWWoIKdq2eyRipggSJnE/eM5PlxYVA==", "type": "package", "path": "microsoft.data.sqlite/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/netstandard2.0/_._", "microsoft.data.sqlite.9.0.6.nupkg.sha512", "microsoft.data.sqlite.nuspec"]}, "Microsoft.Data.Sqlite.Core/9.0.6": {"sha512": "3auiudiViGzj1TidUdjuDqtP3+f6PBk4xdw6r9sBaTtkYoGc3AZn0cP8LgYZaLRnJBqY5bXRLB+qhjoB+iATzA==", "type": "package", "path": "microsoft.data.sqlite.core/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Data.Sqlite.dll", "lib/net6.0/Microsoft.Data.Sqlite.xml", "lib/net8.0/Microsoft.Data.Sqlite.dll", "lib/net8.0/Microsoft.Data.Sqlite.xml", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.9.0.6.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.Identity.Client/4.61.3": {"sha512": "naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "type": "package", "path": "microsoft.identity.client/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.61.3.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"sha512": "xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "type": "package", "path": "microsoft.identitymodel.abstractions/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Abstractions.dll", "lib/net45/Microsoft.IdentityModel.Abstractions.xml", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.Win32.SystemEvents/8.0.0": {"sha512": "9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "type": "package", "path": "microsoft.win32.systemevents/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.8.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"sha512": "8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/net5.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.1/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NLog/5.2.8": {"sha512": "jAIELkWBs1CXFPp986KSGpDFQZHCFccO+LMbKBTTNm42KifaI1mYzFMFQQfuGmGMTrCx0TFPhDjHDE4cLAZWiQ==", "type": "package", "path": "nlog/5.2.8", "files": [".nupkg.metadata", ".signature.p7s", "N.png", "lib/net35/NLog.dll", "lib/net35/NLog.xml", "lib/net45/NLog.dll", "lib/net45/NLog.xml", "lib/net46/NLog.dll", "lib/net46/NLog.xml", "lib/netstandard1.3/NLog.dll", "lib/netstandard1.3/NLog.xml", "lib/netstandard1.5/NLog.dll", "lib/netstandard1.5/NLog.xml", "lib/netstandard2.0/NLog.dll", "lib/netstandard2.0/NLog.xml", "nlog.5.2.8.nupkg.sha512", "nlog.nuspec"]}, "OxyPlot.Core/2.2.0": {"sha512": "QhXNdXR5FPpro/VoLx3BOp6AhQo7YrbfmWEZ9cgY+pnYM7RYORZjnu+aDMA8ka9A1r8hLkX//NbCPZNUv+l8qA==", "type": "package", "path": "oxyplot.core/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "OxyPlot_128.png", "README.md", "lib/net462/OxyPlot.dll", "lib/net462/OxyPlot.xml", "lib/net6.0/OxyPlot.dll", "lib/net6.0/OxyPlot.xml", "lib/net8.0/OxyPlot.dll", "lib/net8.0/OxyPlot.xml", "lib/netstandard2.0/OxyPlot.dll", "lib/netstandard2.0/OxyPlot.xml", "oxyplot.core.2.2.0.nupkg.sha512", "oxyplot.core.nuspec"]}, "OxyPlot.Wpf/2.2.0": {"sha512": "69KzgsMWpJSZmeXZt07FwfV7B1D4CM1nl0MWMj4wjrCsb3USBMo32up1+fQIjxp5tHCPdGwX9VWhSxW8nsY7pQ==", "type": "package", "path": "oxyplot.wpf/2.2.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "OxyPlot_128.png", "README.md", "lib/net462/OxyPlot.Wpf.dll", "lib/net462/OxyPlot.Wpf.xml", "lib/net6.0-windows7.0/OxyPlot.Wpf.dll", "lib/net6.0-windows7.0/OxyPlot.Wpf.xml", "lib/net8.0-windows7.0/OxyPlot.Wpf.dll", "lib/net8.0-windows7.0/OxyPlot.Wpf.xml", "oxyplot.wpf.2.2.0.nupkg.sha512", "oxyplot.wpf.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "OxyPlot.Wpf.Shared/2.2.0": {"sha512": "JoOD/feTOlKFmgXqeTNrl4Ze0i0L6WizPapNw9pXwot+cnI0qhFgYv3tFjlSdu51hZw0EgmcgeRXMtm5bkueYA==", "type": "package", "path": "oxyplot.wpf.shared/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "OxyPlot_128.png", "README.md", "lib/net462/OxyPlot.Wpf.Shared.dll", "lib/net462/OxyPlot.Wpf.Shared.xml", "lib/net6.0-windows7.0/OxyPlot.Wpf.Shared.dll", "lib/net6.0-windows7.0/OxyPlot.Wpf.Shared.xml", "lib/net8.0-windows7.0/OxyPlot.Wpf.Shared.dll", "lib/net8.0-windows7.0/OxyPlot.Wpf.Shared.xml", "oxyplot.wpf.shared.2.2.0.nupkg.sha512", "oxyplot.wpf.shared.nuspec"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"sha512": "UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml", "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.1.10": {"sha512": "Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "type": "package", "path": "sqlitepclraw.core/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.1.10.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"sha512": "mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-arm/native/libe_sqlite3.so", "runtimes/linux-musl-arm64/native/libe_sqlite3.so", "runtimes/linux-musl-s390x/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-ppc64le/native/libe_sqlite3.so", "runtimes/linux-s390x/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib", "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib", "runtimes/osx-arm64/native/libe_sqlite3.dylib", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"sha512": "uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "type": "package", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll", "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.provider.e_sqlite3.nuspec"]}, "System.Diagnostics.DiagnosticSource/6.0.1": {"sha512": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/8.0.0": {"sha512": "JkbHJjtI/dWc5dfmEdJlbe3VwgZqCkZRtfuWFh5GOv0f+gGCfBtzMpIVkmdkj2AObO9y+oiOi81UGwH3aBYuqA==", "type": "package", "path": "system.drawing.common/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.pdb", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.pdb", "lib/net7.0/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.8.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["FluentValidation >= 11.9.0", "MaterialDesignThemes >= 5.2.1", "Microsoft.Data.Sqlite >= 9.0.6", "Microsoft.Identity.Client >= 4.61.3", "NLog >= 5.2.8", "Newtonsoft.Json >= 13.0.3", "OxyPlot.Wpf >= 2.2.0", "System.Drawing.Common >= 8.0.0", "System.Text.Json >= 8.0.5"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\aaee\\aaee\\DebtManagementApp.csproj", "projectName": "DebtManagementApp", "projectPath": "C:\\Users\\<USER>\\Downloads\\aaee\\aaee\\DebtManagementApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\aaee\\aaee\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.Data.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.61.3, )"}, "NLog": {"target": "Package", "version": "[5.2.8, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OxyPlot.Wpf": {"target": "Package", "version": "[2.2.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}