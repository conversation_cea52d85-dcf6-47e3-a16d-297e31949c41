<UserControl x:Class="DebtManagementApp.Views.OneDriveSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="{StaticResource AppBackground}"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="30">
        <StackPanel MaxWidth="800">
            
            <!-- العنوان الرئيسي -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,30">
                <Border Background="{StaticResource PrimaryBrush}" 
                        CornerRadius="20" Width="60" Height="60" 
                        Margin="0,0,20,0">
                    <TextBlock Text="☁️" FontSize="30" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="إعدادات OneDrive" 
                               Style="{StaticResource HeadingLarge}"
                               Foreground="{StaticResource TextPrimary}"/>
                    <TextBlock Text="مزامنة قاعدة البيانات مع السحابة" 
                               Style="{StaticResource BodyMedium}"
                               Foreground="{StaticResource TextSecondary}"
                               Margin="0,5,0,0"/>
                </StackPanel>
            </StackPanel>

            <!-- حالة الاتصال -->
            <Border Background="{StaticResource BackgroundSecondary}" 
                    CornerRadius="15" Padding="25" Margin="0,0,0,25">
                <StackPanel>
                    <TextBlock Text="حالة الاتصال" 
                               Style="{StaticResource HeadingSmall}"
                               Margin="0,0,0,15"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" x:Name="StatusIndicator"
                                Background="{StaticResource ErrorBrush}" 
                                CornerRadius="10" Width="20" Height="20" 
                                Margin="0,0,15,0"/>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock x:Name="ConnectionStatusText" 
                                       Text="غير متصل" 
                                       Style="{StaticResource BodyLarge}"
                                       FontWeight="Bold"/>
                            <TextBlock x:Name="UserInfoText" 
                                       Text="يرجى تسجيل الدخول" 
                                       Style="{StaticResource BodySmall}"
                                       Foreground="{StaticResource TextSecondary}"
                                       Margin="0,2,0,0"/>
                        </StackPanel>

                        <Button Grid.Column="2" x:Name="SignInButton"
                                Content="تسجيل الدخول" 
                                Style="{StaticResource PrimaryButton}"
                                Width="120" Height="35"
                                Click="SignInButton_Click"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- الإعدادات العامة -->
            <Border Background="{StaticResource BackgroundSecondary}" 
                    CornerRadius="15" Padding="25" Margin="0,0,0,25">
                <StackPanel>
                    <TextBlock Text="الإعدادات العامة" 
                               Style="{StaticResource HeadingSmall}"
                               Margin="0,0,0,20"/>

                    <!-- تفعيل المزامنة -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="تفعيل مزامنة OneDrive" 
                                       Style="{StaticResource BodyLarge}"
                                       FontWeight="Bold"/>
                            <TextBlock Text="تفعيل رفع وتحميل قاعدة البيانات تلقائياً" 
                                       Style="{StaticResource BodySmall}"
                                       Foreground="{StaticResource TextSecondary}"
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                        
                        <ToggleButton Grid.Column="1" x:Name="EnableSyncToggle"
                                      Style="{StaticResource ModernToggleButton}"
                                      Checked="EnableSyncToggle_Checked"
                                      Unchecked="EnableSyncToggle_Unchecked"/>
                    </Grid>

                    <!-- المزامنة التلقائية -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="المزامنة التلقائية" 
                                       Style="{StaticResource BodyLarge}"
                                       FontWeight="Bold"/>
                            <TextBlock Text="رفع التغييرات تلقائياً عند حفظها" 
                                       Style="{StaticResource BodySmall}"
                                       Foreground="{StaticResource TextSecondary}"
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                        
                        <ToggleButton Grid.Column="1" x:Name="AutoSyncToggle"
                                      Style="{StaticResource ModernToggleButton}"
                                      Checked="AutoSyncToggle_Checked"
                                      Unchecked="AutoSyncToggle_Unchecked"/>
                    </Grid>

                    <!-- فترة المزامنة -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="فترة المزامنة (بالدقائق)" 
                                       Style="{StaticResource BodyLarge}"
                                       FontWeight="Bold"/>
                            <TextBlock Text="كم دقيقة بين كل مزامنة تلقائية" 
                                       Style="{StaticResource BodySmall}"
                                       Foreground="{StaticResource TextSecondary}"
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                        
                        <TextBox Grid.Column="1" x:Name="SyncIntervalTextBox"
                                 Style="{StaticResource ModernTextBox}"
                                 Width="80" Height="35"
                                 Text="30"
                                 TextChanged="SyncIntervalTextBox_TextChanged"/>
                    </Grid>

                    <!-- مجلد OneDrive -->
                    <Grid Margin="0,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="200"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="مجلد OneDrive" 
                                       Style="{StaticResource BodyLarge}"
                                       FontWeight="Bold"/>
                            <TextBlock Text="اسم المجلد في OneDrive لحفظ النسخ الاحتياطية" 
                                       Style="{StaticResource BodySmall}"
                                       Foreground="{StaticResource TextSecondary}"
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                        
                        <TextBox Grid.Column="1" x:Name="FolderPathTextBox"
                                 Style="{StaticResource ModernTextBox}"
                                 Height="35"
                                 Text="DebtManagementApp"
                                 TextChanged="FolderPathTextBox_TextChanged"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- الإشعارات -->
            <Border Background="{StaticResource BackgroundSecondary}" 
                    CornerRadius="15" Padding="25" Margin="0,0,0,25">
                <StackPanel>
                    <TextBlock Text="الإشعارات" 
                               Style="{StaticResource HeadingSmall}"
                               Margin="0,0,0,20"/>

                    <!-- إشعارات المزامنة -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="إشعارات المزامنة" 
                                       Style="{StaticResource BodyLarge}"
                                       FontWeight="Bold"/>
                            <TextBlock Text="إظهار إشعارات عند نجاح أو فشل المزامنة" 
                                       Style="{StaticResource BodySmall}"
                                       Foreground="{StaticResource TextSecondary}"
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                        
                        <ToggleButton Grid.Column="1" x:Name="ShowNotificationsToggle"
                                      Style="{StaticResource ModernToggleButton}"
                                      IsChecked="True"
                                      Checked="ShowNotificationsToggle_Checked"
                                      Unchecked="ShowNotificationsToggle_Unchecked"/>
                    </Grid>

                    <!-- تحميل التحديثات تلقائياً -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="تحميل التحديثات تلقائياً" 
                                       Style="{StaticResource BodyLarge}"
                                       FontWeight="Bold"/>
                            <TextBlock Text="تحميل النسخة الأحدث تلقائياً بدون سؤال" 
                                       Style="{StaticResource BodySmall}"
                                       Foreground="{StaticResource TextSecondary}"
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                        
                        <ToggleButton Grid.Column="1" x:Name="AutoDownloadToggle"
                                      Style="{StaticResource ModernToggleButton}"
                                      Checked="AutoDownloadToggle_Checked"
                                      Unchecked="AutoDownloadToggle_Unchecked"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- العمليات -->
            <Border Background="{StaticResource BackgroundSecondary}" 
                    CornerRadius="15" Padding="25" Margin="0,0,0,25">
                <StackPanel>
                    <TextBlock Text="العمليات" 
                               Style="{StaticResource HeadingSmall}"
                               Margin="0,0,0,20"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" x:Name="ManualSyncButton"
                                Content="مزامنة يدوية" 
                                Style="{StaticResource PrimaryButton}"
                                Height="40" Margin="0,0,10,0"
                                Click="ManualSyncButton_Click"/>
                        
                        <Button Grid.Column="1" x:Name="CheckUpdatesButton"
                                Content="فحص التحديثات" 
                                Style="{StaticResource SecondaryButton}"
                                Height="40" Margin="5,0,5,0"
                                Click="CheckUpdatesButton_Click"/>
                        
                        <Button Grid.Column="2" x:Name="DownloadLatestButton"
                                Content="تحميل الأحدث" 
                                Style="{StaticResource AccentButton}"
                                Height="40" Margin="10,0,0,0"
                                Click="DownloadLatestButton_Click"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- معلومات آخر مزامنة -->
            <Border Background="{StaticResource BackgroundSecondary}" 
                    CornerRadius="15" Padding="25">
                <StackPanel>
                    <TextBlock Text="معلومات آخر مزامنة" 
                               Style="{StaticResource HeadingSmall}"
                               Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" 
                                   Text="آخر مزامنة:" 
                                   Style="{StaticResource BodyMedium}"
                                   Margin="0,0,15,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" 
                                   x:Name="LastSyncTimeText"
                                   Text="لم يتم المزامنة بعد" 
                                   Style="{StaticResource BodyMedium}"
                                   Foreground="{StaticResource TextSecondary}"
                                   Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" 
                                   Text="الحالة:" 
                                   Style="{StaticResource BodyMedium}"
                                   Margin="0,0,15,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" 
                                   x:Name="LastSyncStatusText"
                                   Text="لم يتم المزامنة بعد" 
                                   Style="{StaticResource BodyMedium}"
                                   Foreground="{StaticResource TextSecondary}"/>
                    </Grid>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</UserControl>
