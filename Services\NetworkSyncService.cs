using System;
using System.Text.Json;
using System.Threading.Tasks;
using DebtManagementApp.Models;

namespace DebtManagementApp.Services
{
    /// <summary>
    /// خدمة مزامنة الشبكة - لإرسال التحديثات لجميع الأجهزة المتصلة
    /// </summary>
    public static class NetworkSyncService
    {
        private static SimpleNetworkService? _networkService;

        /// <summary>
        /// تهيئة الخدمة مع خدمة الشبكة
        /// </summary>
        public static void Initialize(SimpleNetworkService networkService)
        {
            _networkService = networkService;
        }

        /// <summary>
        /// إرسال تحديث إضافة شخص
        /// </summary>
        public static async Task SendPersonAdded(Person person)
        {
            if (_networkService?.IsConnected == true)
            {
                try
                {
                    var message = new NetworkMessage
                    {
                        Type = "PERSON_ADDED",
                        Data = JsonSerializer.Serialize(person),
                        Timestamp = DateTime.Now
                    };
                    
                    var json = JsonSerializer.Serialize(message);
                    await _networkService.SendSimpleData("PERSON_ADDED", json);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في إرسال تحديث إضافة الشخص: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// إرسال تحديث تعديل شخص
        /// </summary>
        public static async Task SendPersonUpdated(Person person)
        {
            if (_networkService?.IsConnected == true)
            {
                try
                {
                    var message = new NetworkMessage
                    {
                        Type = "PERSON_UPDATED",
                        Data = JsonSerializer.Serialize(person),
                        Timestamp = DateTime.Now
                    };
                    
                    var json = JsonSerializer.Serialize(message);
                    await _networkService.SendSimpleData("PERSON_UPDATED", json);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في إرسال تحديث تعديل الشخص: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// إرسال تحديث حذف شخص
        /// </summary>
        public static async Task SendPersonDeleted(int personId)
        {
            if (_networkService?.IsConnected == true)
            {
                try
                {
                    var message = new NetworkMessage
                    {
                        Type = "PERSON_DELETED",
                        Data = JsonSerializer.Serialize(personId),
                        Timestamp = DateTime.Now
                    };
                    
                    var json = JsonSerializer.Serialize(message);
                    await _networkService.SendSimpleData("PERSON_DELETED", json);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في إرسال تحديث حذف الشخص: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// إرسال تحديث إضافة دين
        /// </summary>
        public static async Task SendDebtAdded(Debt debt)
        {
            if (_networkService?.IsConnected == true)
            {
                try
                {
                    var message = new NetworkMessage
                    {
                        Type = "DEBT_ADDED",
                        Data = JsonSerializer.Serialize(debt),
                        Timestamp = DateTime.Now
                    };
                    
                    var json = JsonSerializer.Serialize(message);
                    await _networkService.SendSimpleData("DEBT_ADDED", json);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في إرسال تحديث إضافة الدين: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// إرسال تحديث تعديل دين
        /// </summary>
        public static async Task SendDebtUpdated(Debt debt)
        {
            if (_networkService?.IsConnected == true)
            {
                try
                {
                    var message = new NetworkMessage
                    {
                        Type = "DEBT_UPDATED",
                        Data = JsonSerializer.Serialize(debt),
                        Timestamp = DateTime.Now
                    };
                    
                    var json = JsonSerializer.Serialize(message);
                    await _networkService.SendSimpleData("DEBT_UPDATED", json);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في إرسال تحديث تعديل الدين: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// إرسال تحديث حذف دين
        /// </summary>
        public static async Task SendDebtDeleted(int debtId)
        {
            if (_networkService?.IsConnected == true)
            {
                try
                {
                    var message = new NetworkMessage
                    {
                        Type = "DEBT_DELETED",
                        Data = JsonSerializer.Serialize(debtId),
                        Timestamp = DateTime.Now
                    };
                    
                    var json = JsonSerializer.Serialize(message);
                    await _networkService.SendSimpleData("DEBT_DELETED", json);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في إرسال تحديث حذف الدين: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// التحقق من حالة الاتصال
        /// </summary>
        public static bool IsConnected => _networkService?.IsConnected == true;
    }

    /// <summary>
    /// نموذج رسالة الشبكة
    /// </summary>
    public class NetworkMessage
    {
        public string Type { get; set; } = "";
        public string Data { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }
}
