﻿#pragma checksum "..\..\..\..\Views\SimpleNetworkView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2DF13D9FEE77DF2E7462464A926A1C52B5F0C6C8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// SimpleNetworkView
    /// </summary>
    public partial class SimpleNetworkView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 22 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectedDevicesCount;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocalIPTextBox;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NetworkStatusText;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartServerButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopServerButton;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ServerIPTextBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectButton;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DisconnectButton;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestButton;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox DevicesListBox;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ScanStatusText;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Views\SimpleNetworkView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LogTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/simplenetworkview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SimpleNetworkView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ConnectedDevicesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.LocalIPTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            
            #line 61 "..\..\..\..\Views\SimpleNetworkView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshIP_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.NetworkStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.StartServerButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\SimpleNetworkView.xaml"
            this.StartServerButton.Click += new System.Windows.RoutedEventHandler(this.StartServer_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.StopServerButton = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\Views\SimpleNetworkView.xaml"
            this.StopServerButton.Click += new System.Windows.RoutedEventHandler(this.StopServer_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ServerIPTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            
            #line 118 "..\..\..\..\Views\SimpleNetworkView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ScanNetwork_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ConnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 124 "..\..\..\..\Views\SimpleNetworkView.xaml"
            this.ConnectButton.Click += new System.Windows.RoutedEventHandler(this.ConnectToServer_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.DisconnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 128 "..\..\..\..\Views\SimpleNetworkView.xaml"
            this.DisconnectButton.Click += new System.Windows.RoutedEventHandler(this.Disconnect_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TestButton = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\..\Views\SimpleNetworkView.xaml"
            this.TestButton.Click += new System.Windows.RoutedEventHandler(this.TestConnection_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DevicesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 145 "..\..\..\..\Views\SimpleNetworkView.xaml"
            this.DevicesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DevicesListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ScanStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            
            #line 168 "..\..\..\..\Views\SimpleNetworkView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearLog_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.LogTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

