# ميزة مزامنة OneDrive

## نظرة عامة

تم إضافة ميزة مزامنة OneDrive إلى تطبيق إدارة الديون لتوفير نسخ احتياطية تلقائية وآمنة لقاعدة البيانات في السحابة.

## الميزات الرئيسية

### 1. المزامنة التلقائية
- رفع تلقائي لقاعدة البيانات عند حدوث تغييرات
- مزامنة دورية حسب الفترة المحددة (افتراضياً كل 30 دقيقة)
- مراقبة تغييرات الملفات في الوقت الفعلي

### 2. كشف النسخ الأحدث
- التحقق التلقائي من وجود نسخة أحدث في OneDrive
- عرض نافذة تأكيد للمستخدم عند العثور على نسخة أحدث
- خيار التحميل التلقائي أو اليدوي للنسخ الأحدث

### 3. إدارة التعارضات
- مقارنة تواريخ التعديل بين النسخة المحلية والسحابية
- عرض تفاصيل الفروق الزمنية
- خيارات للمستخدم: تحميل النسخة الأحدث أو تجاهل

### 4. الأمان والمصادقة
- استخدام Microsoft Identity Client للمصادقة الآمنة
- دعم تسجيل الدخول التفاعلي
- حفظ حالة تسجيل الدخول

## الملفات المضافة

### النماذج (Models)
- `Models/OneDriveSettings.cs` - إعدادات OneDrive

### الخدمات (Services)
- `Services/OneDriveService.cs` - خدمة OneDrive الأساسية
- `Services/OneDriveSyncManager.cs` - مدير المزامنة التلقائية

### الواجهات (Views)
- `Views/OneDriveSettingsView.xaml` - صفحة إعدادات OneDrive
- `Views/OneDriveSettingsView.xaml.cs` - الكود الخلفي للإعدادات
- `Views/OneDriveUpdateDialog.xaml` - نافذة تنبيه النسخة الأحدث
- `Views/OneDriveUpdateDialog.xaml.cs` - الكود الخلفي للتنبيه

## التكامل مع التطبيق

### 1. MainWindow
- إضافة زر "☁️ OneDrive" في القائمة الجانبية
- تهيئة خدمة OneDrive عند بدء التطبيق
- معالجة أحداث المزامنة والتحديثات

### 2. DatabaseHelper
- إضافة تحفيز مزامنة OneDrive عند تغيير البيانات
- تكامل مع عمليات إضافة/تعديل/حذف البيانات

### 3. AppSettings
- إضافة إعدادات OneDrive إلى إعدادات التطبيق
- حفظ واسترداد تفضيلات المستخدم

## الإعدادات المتاحة

### الإعدادات العامة
- **تفعيل المزامنة**: تشغيل/إيقاف خدمة OneDrive
- **المزامنة التلقائية**: رفع التغييرات تلقائياً
- **فترة المزامنة**: المدة بين كل مزامنة تلقائية (بالدقائق)
- **مجلد OneDrive**: اسم المجلد لحفظ النسخ الاحتياطية

### إعدادات الإشعارات
- **إشعارات المزامنة**: عرض إشعارات نجاح/فشل المزامنة
- **تحميل التحديثات تلقائياً**: تحميل النسخة الأحدث بدون سؤال

## العمليات المتاحة

### 1. تسجيل الدخول/الخروج
- تسجيل دخول آمن باستخدام حساب Microsoft
- عرض معلومات المستخدم المتصل
- إمكانية تسجيل الخروج

### 2. المزامنة اليدوية
- رفع النسخة المحلية فوراً
- فحص وجود تحديثات في OneDrive
- تحميل النسخة الأحدث يدوياً

### 3. معلومات المزامنة
- عرض تاريخ ووقت آخر مزامنة
- حالة آخر عملية مزامنة (نجح/فشل)
- معلومات المستخدم المتصل

## متطلبات النظام

### الحزم المطلوبة
```xml
<PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
<PackageReference Include="System.Text.Json" Version="8.0.5" />
```

### الأذونات المطلوبة
- `Files.ReadWrite.All` - قراءة وكتابة الملفات في OneDrive
- `User.Read` - قراءة معلومات المستخدم الأساسية

## الاستخدام

### 1. الإعداد الأولي
1. انتقل إلى صفحة "☁️ OneDrive" من القائمة الجانبية
2. اضغط على "تسجيل الدخول"
3. أدخل بيانات حساب Microsoft الخاص بك
4. فعّل المزامنة من الإعدادات

### 2. المزامنة التلقائية
- بمجرد تفعيل المزامنة، سيتم رفع قاعدة البيانات تلقائياً
- عند حدوث تغييرات، سيتم رفعها خلال ثوانٍ
- المزامنة الدورية تحدث حسب الفترة المحددة

### 3. التعامل مع التحديثات
- عند العثور على نسخة أحدث، ستظهر نافذة تأكيد
- اختر "تحميل النسخة الأحدث" للحصول على آخر البيانات
- أو اختر "تجاهل" للاستمرار بالنسخة المحلية

## الأمان

### حماية البيانات
- جميع الاتصالات مشفرة باستخدام HTTPS
- استخدام OAuth 2.0 للمصادقة الآمنة
- عدم حفظ كلمات المرور محلياً

### الخصوصية
- البيانات تُحفظ في حساب OneDrive الشخصي للمستخدم
- لا يتم مشاركة البيانات مع أطراف ثالثة
- المستخدم يتحكم بالكامل في بياناته

## استكشاف الأخطاء

### مشاكل شائعة
1. **فشل تسجيل الدخول**: تأكد من اتصال الإنترنت وصحة بيانات الحساب
2. **فشل الرفع**: تحقق من مساحة التخزين المتاحة في OneDrive
3. **عدم ظهور التحديثات**: تأكد من تفعيل المزامنة التلقائية

### رسائل الخطأ
- يتم عرض رسائل خطأ واضحة باللغة العربية
- تسجيل تفصيلي للأخطاء في وحدة التحكم للمطورين
- إشعارات فورية عند حدوث مشاكل

## التطوير المستقبلي

### ميزات مخططة
- دعم مزودي سحابة إضافيين (Google Drive, Dropbox)
- ضغط قاعدة البيانات قبل الرفع
- تشفير إضافي للبيانات الحساسة
- مزامنة انتقائية للجداول
- نسخ احتياطية متعددة بتواريخ مختلفة

### تحسينات الأداء
- تحسين سرعة الرفع والتحميل
- ضغط البيانات لتوفير عرض النطاق
- مزامنة تدريجية للتغييرات فقط

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع قسم "معلومات آخر مزامنة" في صفحة الإعدادات
- تحقق من رسائل الخطأ في الإشعارات
- تأكد من تحديث التطبيق لآخر إصدار
