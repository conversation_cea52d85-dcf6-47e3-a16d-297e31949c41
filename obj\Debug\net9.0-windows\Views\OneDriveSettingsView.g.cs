﻿#pragma checksum "..\..\..\..\Views\OneDriveSettingsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DAD81501586B0D1FCED9F680C450865FDEC5A8A8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// OneDriveSettingsView
    /// </summary>
    public partial class OneDriveSettingsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 46 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserInfoText;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SignInButton;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton EnableSyncToggle;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AutoSyncToggle;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SyncIntervalTextBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FolderPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ShowNotificationsToggle;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AutoDownloadToggle;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManualSyncButton;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CheckUpdatesButton;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadLatestButton;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastSyncTimeText;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastSyncStatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/onedrivesettingsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatusIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.UserInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SignInButton = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.SignInButton.Click += new System.Windows.RoutedEventHandler(this.SignInButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.EnableSyncToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 99 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.EnableSyncToggle.Checked += new System.Windows.RoutedEventHandler(this.EnableSyncToggle_Checked);
            
            #line default
            #line hidden
            
            #line 100 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.EnableSyncToggle.Unchecked += new System.Windows.RoutedEventHandler(this.EnableSyncToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AutoSyncToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 122 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.AutoSyncToggle.Checked += new System.Windows.RoutedEventHandler(this.AutoSyncToggle_Checked);
            
            #line default
            #line hidden
            
            #line 123 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.AutoSyncToggle.Unchecked += new System.Windows.RoutedEventHandler(this.AutoSyncToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SyncIntervalTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 147 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.SyncIntervalTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SyncIntervalTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.FolderPathTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 171 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.FolderPathTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.FolderPathTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ShowNotificationsToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 204 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.ShowNotificationsToggle.Checked += new System.Windows.RoutedEventHandler(this.ShowNotificationsToggle_Checked);
            
            #line default
            #line hidden
            
            #line 205 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.ShowNotificationsToggle.Unchecked += new System.Windows.RoutedEventHandler(this.ShowNotificationsToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AutoDownloadToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 227 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.AutoDownloadToggle.Checked += new System.Windows.RoutedEventHandler(this.AutoDownloadToggle_Checked);
            
            #line default
            #line hidden
            
            #line 228 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.AutoDownloadToggle.Unchecked += new System.Windows.RoutedEventHandler(this.AutoDownloadToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ManualSyncButton = ((System.Windows.Controls.Button)(target));
            
            #line 252 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.ManualSyncButton.Click += new System.Windows.RoutedEventHandler(this.ManualSyncButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CheckUpdatesButton = ((System.Windows.Controls.Button)(target));
            
            #line 258 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.CheckUpdatesButton.Click += new System.Windows.RoutedEventHandler(this.CheckUpdatesButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DownloadLatestButton = ((System.Windows.Controls.Button)(target));
            
            #line 264 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.DownloadLatestButton.Click += new System.Windows.RoutedEventHandler(this.DownloadLatestButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.LastSyncTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.LastSyncStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

