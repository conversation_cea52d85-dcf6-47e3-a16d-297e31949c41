﻿#pragma checksum "..\..\..\..\Views\OneDriveSettingsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BD0FE3AD9E0AA561C2390CD0EDB2655FB52A07C5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// OneDriveSettingsView
    /// </summary>
    public partial class OneDriveSettingsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 54 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserInfoText;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SignInButton;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton EnableSyncToggle;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AutoSyncToggle;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SyncIntervalTextBox;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FolderPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ShowNotificationsToggle;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AutoDownloadToggle;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RememberSignInToggle;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CompressionToggle;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton VersioningToggle;
        
        #line default
        #line hidden
        
        
        #line 325 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManualSyncButton;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CheckUpdatesButton;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadLatestButton;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ProgressContainer;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressText;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar SyncProgressBar;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastSyncTimeText;
        
        #line default
        #line hidden
        
        
        #line 401 "..\..\..\..\Views\OneDriveSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastSyncStatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/onedrivesettingsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatusIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.UserInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SignInButton = ((System.Windows.Controls.Button)(target));
            
            #line 75 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.SignInButton.Click += new System.Windows.RoutedEventHandler(this.SignInButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.EnableSyncToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 107 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.EnableSyncToggle.Checked += new System.Windows.RoutedEventHandler(this.EnableSyncToggle_Checked);
            
            #line default
            #line hidden
            
            #line 108 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.EnableSyncToggle.Unchecked += new System.Windows.RoutedEventHandler(this.EnableSyncToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AutoSyncToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 130 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.AutoSyncToggle.Checked += new System.Windows.RoutedEventHandler(this.AutoSyncToggle_Checked);
            
            #line default
            #line hidden
            
            #line 131 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.AutoSyncToggle.Unchecked += new System.Windows.RoutedEventHandler(this.AutoSyncToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SyncIntervalTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 155 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.SyncIntervalTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SyncIntervalTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.FolderPathTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 179 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.FolderPathTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.FolderPathTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ShowNotificationsToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 212 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.ShowNotificationsToggle.Checked += new System.Windows.RoutedEventHandler(this.ShowNotificationsToggle_Checked);
            
            #line default
            #line hidden
            
            #line 213 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.ShowNotificationsToggle.Unchecked += new System.Windows.RoutedEventHandler(this.ShowNotificationsToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AutoDownloadToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 235 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.AutoDownloadToggle.Checked += new System.Windows.RoutedEventHandler(this.AutoDownloadToggle_Checked);
            
            #line default
            #line hidden
            
            #line 236 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.AutoDownloadToggle.Unchecked += new System.Windows.RoutedEventHandler(this.AutoDownloadToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 11:
            this.RememberSignInToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 258 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.RememberSignInToggle.Checked += new System.Windows.RoutedEventHandler(this.RememberSignInToggle_Checked);
            
            #line default
            #line hidden
            
            #line 259 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.RememberSignInToggle.Unchecked += new System.Windows.RoutedEventHandler(this.RememberSignInToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CompressionToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 281 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.CompressionToggle.Checked += new System.Windows.RoutedEventHandler(this.CompressionToggle_Checked);
            
            #line default
            #line hidden
            
            #line 282 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.CompressionToggle.Unchecked += new System.Windows.RoutedEventHandler(this.CompressionToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 13:
            this.VersioningToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 304 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.VersioningToggle.Checked += new System.Windows.RoutedEventHandler(this.VersioningToggle_Checked);
            
            #line default
            #line hidden
            
            #line 305 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.VersioningToggle.Unchecked += new System.Windows.RoutedEventHandler(this.VersioningToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ManualSyncButton = ((System.Windows.Controls.Button)(target));
            
            #line 329 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.ManualSyncButton.Click += new System.Windows.RoutedEventHandler(this.ManualSyncButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.CheckUpdatesButton = ((System.Windows.Controls.Button)(target));
            
            #line 335 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.CheckUpdatesButton.Click += new System.Windows.RoutedEventHandler(this.CheckUpdatesButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.DownloadLatestButton = ((System.Windows.Controls.Button)(target));
            
            #line 341 "..\..\..\..\Views\OneDriveSettingsView.xaml"
            this.DownloadLatestButton.Click += new System.Windows.RoutedEventHandler(this.DownloadLatestButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ProgressContainer = ((System.Windows.Controls.Border)(target));
            return;
            case 18:
            this.ProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.SyncProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 20:
            this.LastSyncTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.LastSyncStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

