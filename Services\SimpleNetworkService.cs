using System;
using System.Collections.Generic;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace DebtManagementApp.Services
{
    public class SimpleNetworkService
    {
        private TcpListener? _server;
        private TcpClient? _client;
        private NetworkStream? _stream;
        private bool _isServer;
        private bool _isConnected;
        private readonly int _port = 8888;

        public event Action<string>? OnMessageReceived;
        public event Action<bool>? OnConnectionStatusChanged;
        public event Action<string>? OnLog;

        public bool IsConnected => _isConnected;
        public bool IsServer => _isServer;

        // بدء الخادم
        public async Task<bool> StartServer()
        {
            try
            {
                _isServer = true;
                var localIP = GetLocalIPAddress();
                _server = new TcpListener(IPAddress.Parse(localIP), _port);
                _server.Start();
                
                OnLog?.Invoke($"🟢 الخادم يعمل على {localIP}:{_port}");
                OnLog?.Invoke("⏳ في انتظار اتصال العميل...");

                // انتظار اتصال العميل
                var tcpClient = await _server.AcceptTcpClientAsync();
                _stream = tcpClient.GetStream();
                _isConnected = true;
                
                OnConnectionStatusChanged?.Invoke(true);
                OnLog?.Invoke("✅ تم الاتصال بالعميل بنجاح!");

                // بدء استقبال الرسائل
                _ = Task.Run(ListenForMessages);
                
                return true;
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"❌ خطأ في بدء الخادم: {ex.Message}");
                return false;
            }
        }

        // الاتصال كعميل
        public async Task<bool> ConnectToServer(string serverIP)
        {
            try
            {
                _isServer = false;
                _client = new TcpClient();
                
                OnLog?.Invoke($"⏳ محاولة الاتصال بالخادم {serverIP}:{_port}...");
                
                await _client.ConnectAsync(IPAddress.Parse(serverIP), _port);
                _stream = _client.GetStream();
                _isConnected = true;
                
                OnConnectionStatusChanged?.Invoke(true);
                OnLog?.Invoke("✅ تم الاتصال بالخادم بنجاح!");

                // بدء استقبال الرسائل
                _ = Task.Run(ListenForMessages);
                
                return true;
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"❌ خطأ في الاتصال بالخادم: {ex.Message}");
                return false;
            }
        }

        // إرسال رسالة
        public async Task<bool> SendMessage(string message)
        {
            if (!_isConnected || _stream == null)
                return false;

            try
            {
                byte[] data = Encoding.UTF8.GetBytes(message);
                byte[] lengthPrefix = BitConverter.GetBytes(data.Length);
                
                await _stream.WriteAsync(lengthPrefix, 0, 4);
                await _stream.WriteAsync(data, 0, data.Length);
                await _stream.FlushAsync();
                
                return true;
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"❌ خطأ في إرسال الرسالة: {ex.Message}");
                return false;
            }
        }

        // استقبال الرسائل
        private async Task ListenForMessages()
        {
            if (_stream == null) return;

            try
            {
                byte[] lengthBuffer = new byte[4];
                
                while (_isConnected && _stream.CanRead)
                {
                    // قراءة طول الرسالة
                    int bytesRead = await _stream.ReadAsync(lengthBuffer, 0, 4);
                    if (bytesRead == 0) break;
                    
                    int messageLength = BitConverter.ToInt32(lengthBuffer, 0);
                    
                    // قراءة الرسالة
                    byte[] messageBuffer = new byte[messageLength];
                    int totalBytesRead = 0;
                    
                    while (totalBytesRead < messageLength)
                    {
                        bytesRead = await _stream.ReadAsync(messageBuffer, totalBytesRead, 
                            messageLength - totalBytesRead);
                        if (bytesRead == 0) break;
                        totalBytesRead += bytesRead;
                    }
                    
                    string message = Encoding.UTF8.GetString(messageBuffer);
                    OnMessageReceived?.Invoke(message);
                }
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"❌ خطأ في استقبال الرسائل: {ex.Message}");
            }
            finally
            {
                Disconnect();
            }
        }

        // قطع الاتصال
        public void Disconnect()
        {
            _isConnected = false;
            
            try
            {
                _stream?.Close();
                _client?.Close();
                _server?.Stop();
                
                OnConnectionStatusChanged?.Invoke(false);
                OnLog?.Invoke("🔴 تم قطع الاتصال");
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"❌ خطأ في قطع الاتصال: {ex.Message}");
            }
        }

        // الحصول على IP المحلي
        public static string GetLocalIPAddress()
        {
            try
            {
                foreach (NetworkInterface ni in NetworkInterface.GetAllNetworkInterfaces())
                {
                    if (ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211 ||
                        ni.NetworkInterfaceType == NetworkInterfaceType.Ethernet)
                    {
                        if (ni.OperationalStatus == OperationalStatus.Up)
                        {
                            foreach (UnicastIPAddressInformation ip in ni.GetIPProperties().UnicastAddresses)
                            {
                                if (ip.Address.AddressFamily == AddressFamily.InterNetwork &&
                                    !IPAddress.IsLoopback(ip.Address))
                                {
                                    return ip.Address.ToString();
                                }
                            }
                        }
                    }
                }
            }
            catch { }
            
            return "127.0.0.1";
        }

        // فحص الاتصال
        public static async Task<bool> PingHost(string hostIP)
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(hostIP, 3000);
                return reply.Status == IPStatus.Success;
            }
            catch
            {
                return false;
            }
        }

        // البحث عن الأجهزة في الشبكة
        public static async Task<List<string>> ScanNetwork()
        {
            var devices = new List<string>();
            var localIP = GetLocalIPAddress();
            var subnet = localIP.Substring(0, localIP.LastIndexOf('.'));

            var tasks = new List<Task>();
            
            for (int i = 1; i <= 254; i++)
            {
                string ip = $"{subnet}.{i}";
                if (ip != localIP)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        if (await PingHost(ip))
                        {
                            lock (devices)
                            {
                                devices.Add(ip);
                            }
                        }
                    }));
                }
            }

            await Task.WhenAll(tasks);
            return devices;
        }

        // إرسال بيانات مبسطة
        public async Task SendSimpleData(string action, string data)
        {
            var message = new
            {
                Action = action,
                Data = data,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };

            string jsonMessage = JsonSerializer.Serialize(message);
            await SendMessage(jsonMessage);
            OnLog?.Invoke($"📤 إرسال: {action}");
        }
    }
}
