using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;
using DebtManagementApp.Services;

namespace DebtManagementApp.Views
{
    public partial class DebtsManagementView : UserControl, INotifyPropertyChanged
    {
        private readonly object _databaseService;
        private ObservableCollection<Debt> _allDebts;
        private ObservableCollection<Debt> _filteredDebts;
        private ObservableCollection<Person> _allPersons;
        private ObservableCollection<PersonSummary> _allPersonSummaries;
        private ObservableCollection<PersonSummary> _filteredPersons;
        private PersonSummary _selectedPersonSummary;
        private bool _isEditingDebt = false;
        private Debt _selectedDebtForEdit;
        private Debt _selectedDebt;
        private string _searchText = "";

        public DebtsManagementView()
        {
            InitializeComponent();
            _databaseService = null; // سيتم إصلاحه لاحقاً
            _allDebts = new ObservableCollection<Debt>();
            _filteredDebts = new ObservableCollection<Debt>();
            _allPersons = new ObservableCollection<Person>();
            _allPersonSummaries = new ObservableCollection<PersonSummary>();
            _filteredPersons = new ObservableCollection<PersonSummary>();
            DataContext = this;
            Loaded += DebtsManagementView_Loaded;

            // تسجيل أحداث حفظ إعدادات الأعمدة
            Loaded += (s, e) => ColumnSettingsHelper.RegisterColumnEvents(PersonsDataGrid, "DebtsManagementGrid");
        }

        private void DebtsManagementView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadSampleData();
                UpdatePersonSummaries();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public ObservableCollection<Debt> FilteredDebts
        {
            get => _filteredDebts;
            set
            {
                _filteredDebts = value;
                OnPropertyChanged(nameof(FilteredDebts));
            }
        }

        public ObservableCollection<PersonSummary> FilteredPersons
        {
            get => _filteredPersons;
            set
            {
                _filteredPersons = value;
                OnPropertyChanged(nameof(FilteredPersons));
            }
        }

        public PersonSummary SelectedPersonSummary
        {
            get => _selectedPersonSummary;
            set
            {
                _selectedPersonSummary = value;
                OnPropertyChanged(nameof(SelectedPersonSummary));
            }
        }

        public Debt SelectedDebt
        {
            get => _selectedDebt;
            set
            {
                _selectedDebt = value;
                OnPropertyChanged(nameof(SelectedDebt));
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged(nameof(SearchText));
                FilterPersons();
            }
        }

        private void LoadSampleData()
        {
            try
            {
                // تحميل الأشخاص من قاعدة البيانات
                _allPersons.Clear();

                try
                {
                    var personsFromDb = DatabaseHelper.GetAllPersons();
                    foreach (var person in personsFromDb)
                    {
                        // تحويل Location إلى Address إذا لزم الأمر
                        if (string.IsNullOrEmpty(person.Address) && !string.IsNullOrEmpty(person.Location))
                        {
                            person.Address = person.Location;
                        }
                        if (string.IsNullOrEmpty(person.Notes) && !string.IsNullOrEmpty(person.AdditionalInfo))
                        {
                            person.Notes = person.AdditionalInfo;
                        }
                        _allPersons.Add(person);
                    }
                }
                catch (Exception dbEx)
                {
                    // إذا فشل تحميل البيانات من قاعدة البيانات، استخدم البيانات التجريبية
                    MessageBox.Show($"فشل تحميل الأشخاص من قاعدة البيانات، سيتم استخدام البيانات التجريبية: {dbEx.Message}",
                        "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);

                    var samplePersons = new List<Person>
                    {
                        new Person { Id = 1, Name = "أحمد محمد علي" },
                        new Person { Id = 2, Name = "فاطمة عبدالله" },
                        new Person { Id = 3, Name = "محمد سعد الغامدي" },
                        new Person { Id = 4, Name = "نورا خالد" },
                        new Person { Id = 5, Name = "عبدالرحمن الشهري" }
                    };

                    foreach (var person in samplePersons)
                    {
                        _allPersons.Add(person);
                    }
                }

                // تحميل الديون التجريبية
                LoadSampleDebts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSampleDebts()
        {
            try
            {
                _allDebts.Clear();
                FilteredDebts.Clear();

                // تحميل الديون من قاعدة البيانات
                try
                {
                    var debtsFromDb = DatabaseHelper.GetAllDebts();
                    foreach (var debt in debtsFromDb)
                    {
                        _allDebts.Add(debt);
                    }

                    // إذا لم توجد ديون في قاعدة البيانات، أضف البيانات التجريبية
                    if (debtsFromDb.Count == 0)
                    {
                        LoadSampleDebtsData();
                    }
                }
                catch (Exception dbEx)
                {
                    // إذا فشل تحميل البيانات من قاعدة البيانات، استخدم البيانات التجريبية
                    MessageBox.Show($"فشل تحميل الديون من قاعدة البيانات، سيتم استخدام البيانات التجريبية: {dbEx.Message}",
                        "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);

                    LoadSampleDebtsData();
                }

                // تحديث قائمة الديون المفلترة
                foreach (var debt in _allDebts)
                {
                    FilteredDebts.Add(debt);
                }

                UpdatePersonSummaries();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الديون: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSampleDebtsData()
        {
            // ديون تجريبية مبسطة
            var sampleDebts = new List<Debt>();

                // دين 1
                var debt1 = new Debt();
                debt1.Id = 1;
                debt1.PersonId = 1;
                debt1.PersonName = "أحمد محمد علي";
                debt1.Amount = 750000;
                debt1.Date = DateTime.Now.AddDays(-30);
                debt1.DueDate = DateTime.Now.AddDays(-5);
                debt1.Description = "حديد تسليح 16 ملم - 5 طن";
                debt1.IsSettled = false;
                debt1.PaymentDate = null;
                sampleDebts.Add(debt1);

                // دين 2
                var debt2 = new Debt();
                debt2.Id = 2;
                debt2.PersonId = 1;
                debt2.PersonName = "أحمد محمد علي";
                debt2.Amount = 450000;
                debt2.Date = DateTime.Now.AddDays(-20);
                debt2.DueDate = DateTime.Now.AddDays(10);
                debt2.Description = "قطع وثني حديد للأعمدة";
                debt2.IsSettled = true;
                debt2.PaymentDate = DateTime.Now.AddDays(-5);
                sampleDebts.Add(debt2);

                // دين 3
                var debt3 = new Debt();
                debt3.Id = 3;
                debt3.PersonId = 2;
                debt3.PersonName = "فاطمة عبدالله";
                debt3.Amount = 320000;
                debt3.Date = DateTime.Now.AddDays(-15);
                debt3.DueDate = DateTime.Now.AddDays(15);
                debt3.Description = "حديد تسليح 12 ملم - 2 طن";
                debt3.IsSettled = false;
                debt3.PaymentDate = null;
                sampleDebts.Add(debt3);

                // دين 4
                var debt4 = new Debt();
                debt4.Id = 4;
                debt4.PersonId = 3;
                debt4.PersonName = "محمد سعد الغامدي";
                debt4.Amount = 890000;
                debt4.Date = DateTime.Now.AddDays(-25);
                debt4.DueDate = DateTime.Now.AddDays(-2);
                debt4.Description = "لحام وتركيب هيكل حديدي";
                debt4.IsSettled = false;
                debt4.PaymentDate = null;
                sampleDebts.Add(debt4);

                // دين 5
                var debt5 = new Debt();
                debt5.Id = 5;
                debt5.PersonId = 3;
                debt5.PersonName = "محمد سعد الغامدي";
                debt5.Amount = 150000;
                debt5.Date = DateTime.Now.AddDays(-10);
                debt5.DueDate = DateTime.Now.AddDays(20);
                debt5.Description = "حديد زاوية وقطع صغيرة";
                debt5.IsSettled = true;
                debt5.PaymentDate = DateTime.Now.AddDays(-3);
                sampleDebts.Add(debt5);

            foreach (var debt in sampleDebts)
            {
                _allDebts.Add(debt);
            }
        }

        private void FilterPersons()
        {
            try
            {
                FilteredPersons.Clear();

                var filtered = _allPersonSummaries.AsEnumerable();

                // فلترة بالنص
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    filtered = filtered.Where(p =>
                        p.PersonName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        p.Phone.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        p.Address.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
                }

                // فلترة بالحالة
                if (PaymentStatusFilterComboBox?.SelectedItem is ComboBoxItem statusItem)
                {
                    var status = statusItem.Content?.ToString();
                    switch (status)
                    {
                        case "مدفوع بالكامل":
                            filtered = filtered.Where(p => p.PaymentStatus == "مدفوع بالكامل");
                            break;
                        case "متأخر":
                            filtered = filtered.Where(p => p.PaymentStatus == "متأخر");
                            break;
                        case "جزئي":
                            filtered = filtered.Where(p => p.PaymentStatus == "جزئي");
                            break;
                    }
                }

                foreach (var person in filtered)
                {
                    FilteredPersons.Add(person);
                }
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في التحديث
            }
        }

        private void FilterDebts()
        {
            try
            {
                FilteredDebts.Clear();

                var filtered = _allDebts.AsEnumerable();

                // فلترة النص
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    filtered = filtered.Where(d =>
                        (d.PersonName?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                        (d.Description?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                        d.Amount.ToString().Contains(SearchText));
                }

            // فلترة الشخص - تم إزالتها لأننا نعرض الأشخاص الآن

            // فلترة الحالة
            if (StatusFilterComboBox?.SelectedItem is ComboBoxItem statusItem)
            {
                var status = statusItem.Content?.ToString();
                switch (status)
                {
                    case "مدفوع":
                        filtered = filtered.Where(d => d.IsSettled);
                        break;
                    case "غير مدفوع":
                        filtered = filtered.Where(d => !d.IsSettled && d.DueDate >= DateTime.Now);
                        break;
                    case "متأخر":
                        filtered = filtered.Where(d => !d.IsSettled && d.DueDate < DateTime.Now);
                        break;
                }
            }

                foreach (var debt in filtered)
                {
                    FilteredDebts.Add(debt);
                }

                UpdatePersonSummaries();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الفلترة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdatePersonSummaries()
        {
            try
            {
                _allPersonSummaries.Clear();

                var personGroups = _allDebts.GroupBy(d => d.PersonName);

                foreach (var group in personGroups)
                {
                    var personName = group.Key;
                    var personDebts = group.ToList();
                    var person = _allPersons.FirstOrDefault(p => p.Name == personName);

                    var totalAmount = personDebts.Sum(d => d.Amount);
                    var paidAmount = personDebts.Where(d => d.IsSettled).Sum(d => d.Amount);
                    var remainingAmount = personDebts.Where(d => !d.IsSettled).Sum(d => d.Amount);
                    var hasOverdueDebts = personDebts.Any(d => !d.IsSettled && d.IsOverdue);

                    // تحديد حالة الدفع
                    string paymentStatus;
                    if (remainingAmount == 0)
                    {
                        paymentStatus = "مدفوع بالكامل";
                    }
                    else if (hasOverdueDebts)
                    {
                        paymentStatus = "متأخر";
                    }
                    else if (paidAmount > 0)
                    {
                        paymentStatus = "جزئي";
                    }
                    else
                    {
                        paymentStatus = "غير مدفوع";
                    }

                    var summary = new PersonSummary
                    {
                        PersonId = person?.Id ?? 0,
                        PersonName = personName,
                        Phone = person?.Phone ?? "",
                        Address = person?.Address ?? "",
                        TotalDebts = personDebts.Count,
                        TotalAmount = totalAmount,
                        PaidAmount = paidAmount,
                        RemainingAmount = remainingAmount,
                        PaymentStatus = paymentStatus,
                        LastPaymentDate = personDebts.Where(d => d.IsSettled && d.SettlementDate.HasValue)
                                                   .OrderByDescending(d => d.SettlementDate)
                                                   .FirstOrDefault()?.SettlementDate
                    };

                    _allPersonSummaries.Add(summary);
                }

                FilteredPersons = new ObservableCollection<PersonSummary>(_allPersonSummaries);
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في التحديث
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                if (TotalDebtsText == null || PaidDebtsText == null ||
                    UnpaidDebtsText == null || OverdueDebtsText == null)
                    return;

                var visibleDebts = FilteredDebts;
                TotalDebtsText.Text = visibleDebts.Count.ToString();
                PaidDebtsText.Text = visibleDebts.Count(d => d.IsSettled).ToString();
                UnpaidDebtsText.Text = visibleDebts.Count(d => !d.IsSettled && d.DueDate >= DateTime.Now).ToString();
                OverdueDebtsText.Text = visibleDebts.Count(d => !d.IsSettled && d.DueDate < DateTime.Now).ToString();
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في التحديث
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                SearchText = textBox.Text;
                FilterPersons();
            }
        }

        private void PersonFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterPersons();
        }

        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterPersons();
        }

        private void AddDebt_Click(object sender, RoutedEventArgs e)
        {
            ShowDebtForm(false);
        }

        private void EditDebt_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Debt debt)
            {
                _selectedDebtForEdit = debt;
                ShowDebtForm(true);
            }
        }

        private void DeleteDebt_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Debt debt)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الدين؟\nالشخص: {debt.PersonName}\nالمبلغ: {debt.Amount:N0} دينار",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _allDebts.Remove(debt);
                        FilteredDebts.Remove(debt);
                        UpdateStatistics();

                        MessageBox.Show("تم حذف الدين بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        // تسجيل النشاط
                        ActivityService.LogDebtDeleted(debt.PersonName, debt.Amount);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الدين: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void TogglePayment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Debt debt)
            {
                try
                {
                    // تغيير حالة الدفع
                    debt.IsSettled = !debt.IsSettled;
                    debt.PaymentDate = debt.IsSettled ? DateTime.Now : (DateTime?)null;

                    // تحديث الإحصائيات
                    UpdateStatistics();

                    var message = debt.IsSettled ? "تم تسجيل الدفع بنجاح" : "تم إلغاء تسجيل الدفع";
                    MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                    // تسجيل النشاط
                    if (debt.IsSettled)
                    {
                        ActivityService.LogDebtPaid(debt.PersonName, debt.Amount);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث حالة الدفع: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void PersonsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (SelectedPersonSummary != null)
            {
                ViewPersonDebts_Click(sender, new RoutedEventArgs());
            }
        }

        private void ViewPersonDebts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الشخص المحدد من DataGrid
                var selectedPersonSummary = PersonsDataGrid.SelectedItem as PersonSummary;
                if (selectedPersonSummary == null)
                {
                    MessageBox.Show("يرجى تحديد شخص لعرض ديونه", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // البحث عن الشخص في قائمة الأشخاص
                var person = _allPersons.FirstOrDefault(p => p.Name == selectedPersonSummary.PersonName);
                if (person != null)
                {
                    ShowPersonDebts(person.Id, person.Name);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على بيانات الشخص", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض ديون الشخص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddDebtForPerson_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الشخص المحدد من DataGrid
                var selectedPersonSummary = PersonsDataGrid.SelectedItem as PersonSummary;
                if (selectedPersonSummary == null)
                {
                    MessageBox.Show("يرجى تحديد شخص لإضافة دين له", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // البحث عن الشخص في قائمة الأشخاص
                var person = _allPersons.FirstOrDefault(p => p.Name == selectedPersonSummary.PersonName);
                if (person != null)
                {
                    ShowDebtForm(false, person);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على بيانات الشخص", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة دين جديد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowPersonDebts(int personId, string personName)
        {
            try
            {
                // البحث عن الشخص في القائمة
                var person = _allPersons.FirstOrDefault(p => p.Id == personId);
                if (person == null)
                {
                    // إنشاء شخص مؤقت إذا لم يوجد
                    person = new Person { Id = personId, Name = personName };
                }

                // البحث عن MainWindow
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    // إنشاء واجهة ديون الشخص
                    var personDebtsView = new PersonDebtsView();
                    personDebtsView.SetPerson(person, () => {
                        // العودة لواجهة إدارة الديون
                        var debtsView = new DebtsManagementView();
                        mainWindow.MainContent.Content = debtsView;
                        mainWindow.ContentTitle.Text = "💰 ديون الأشخاص";
                    });

                    // تحديث المحتوى في MainWindow
                    mainWindow.MainContent.Content = personDebtsView;
                    mainWindow.ContentTitle.Text = $"💰 ديون {person.Name}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح ديون الشخص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
        }

        // دوال إدارة الديون
        private void ShowDebtForm(bool isEditing, Person selectedPerson = null)
        {
            try
            {
                _isEditingDebt = isEditing;

                if (DebtFormTitleText == null || DebtPersonComboBox == null ||
                    DebtAmountTextBox == null || DebtDatePicker == null ||
                    DueDatePicker == null || DebtDescriptionTextBox == null ||
                    IsSettledCheckBox == null || PaymentDatePicker == null ||
                    SaveDebtButton == null || DebtFormOverlay == null)
                {
                    return;
                }

                // تحديث قائمة الأشخاص من قاعدة البيانات
                try
                {
                    _allPersons.Clear();
                    var personsFromDb = DatabaseHelper.GetAllPersons();
                    foreach (var person in personsFromDb)
                    {
                        // تحويل Location إلى Address إذا لزم الأمر
                        if (string.IsNullOrEmpty(person.Address) && !string.IsNullOrEmpty(person.Location))
                        {
                            person.Address = person.Location;
                        }
                        if (string.IsNullOrEmpty(person.Notes) && !string.IsNullOrEmpty(person.AdditionalInfo))
                        {
                            person.Notes = person.AdditionalInfo;
                        }
                        _allPersons.Add(person);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل قائمة الأشخاص: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }

                // تحميل قائمة الأشخاص
                DebtPersonComboBox.ItemsSource = null; // إعادة تعيين أولاً
                DebtPersonComboBox.ItemsSource = _allPersons;
                DebtPersonComboBox.DisplayMemberPath = "Name";
                DebtPersonComboBox.SelectedValuePath = "Id";

                if (_allPersons.Count == 0)
                {
                    MessageBox.Show("لا توجد أشخاص في قاعدة البيانات. يرجى إضافة أشخاص أولاً من قائمة إدارة الأشخاص.",
                        "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }

                if (isEditing && _selectedDebtForEdit != null)
                {
                    DebtFormTitleText.Text = "✏️ تعديل الدين";
                    DebtPersonComboBox.SelectedValue = _selectedDebtForEdit.PersonId;
                    DebtAmountTextBox.Text = _selectedDebtForEdit.Amount.ToString();
                    DebtDatePicker.SelectedDate = _selectedDebtForEdit.Date;
                    DueDatePicker.SelectedDate = _selectedDebtForEdit.DueDate;
                    DebtDescriptionTextBox.Text = _selectedDebtForEdit.Description ?? "";
                    IsSettledCheckBox.IsChecked = _selectedDebtForEdit.IsSettled;
                    PaymentDatePicker.SelectedDate = _selectedDebtForEdit.PaymentDate;
                    SaveDebtButton.Content = "💾 تحديث";
                }
                else
                {
                    DebtFormTitleText.Text = "➕ إضافة دين جديد";

                    // إذا تم تحديد شخص معين، اختره في القائمة
                    if (selectedPerson != null)
                    {
                        DebtPersonComboBox.SelectedValue = selectedPerson.Id;
                    }
                    else
                    {
                        DebtPersonComboBox.SelectedIndex = -1;
                    }

                    DebtAmountTextBox.Text = "";
                    DebtDatePicker.SelectedDate = DateTime.Now;
                    DueDatePicker.SelectedDate = DateTime.Now.AddDays(30);
                    DebtDescriptionTextBox.Text = "";
                    IsSettledCheckBox.IsChecked = false;
                    PaymentDatePicker.SelectedDate = null;
                    SaveDebtButton.Content = "💾 حفظ";
                }

                // تحديث رؤية تاريخ الدفع
                UpdatePaymentDateVisibility();

                DebtFormOverlay.Visibility = Visibility.Visible;
                DebtAmountTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseDebtForm_Click(object sender, RoutedEventArgs e)
        {
            if (DebtFormOverlay != null)
                DebtFormOverlay.Visibility = Visibility.Collapsed;
        }

        private void IsSettled_Changed(object sender, RoutedEventArgs e)
        {
            UpdatePaymentDateVisibility();
        }

        private void UpdatePaymentDateVisibility()
        {
            if (PaymentDatePanel != null && IsSettledCheckBox != null)
            {
                PaymentDatePanel.Visibility = IsSettledCheckBox.IsChecked == true ?
                    Visibility.Visible : Visibility.Collapsed;

                if (IsSettledCheckBox.IsChecked == true && PaymentDatePicker?.SelectedDate == null)
                {
                    PaymentDatePicker.SelectedDate = DateTime.Now;
                }
            }
        }

        private void SaveDebt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (DebtPersonComboBox?.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار الشخص", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(DebtAmountTextBox?.Text) ||
                    !double.TryParse(DebtAmountTextBox.Text, out double amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    DebtAmountTextBox?.Focus();
                    return;
                }

                if (DebtDatePicker?.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ الدين", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (DueDatePicker?.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ الاستحقاق", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء أو تحديث الدين
                Debt debt;
                if (_isEditingDebt && _selectedDebtForEdit != null)
                {
                    debt = _selectedDebtForEdit;
                }
                else
                {
                    debt = new Debt();
                    debt.Id = _allDebts.Count > 0 ? _allDebts.Max(d => d.Id) + 1 : 1;
                }

                // تعبئة البيانات
                debt.PersonId = (int)DebtPersonComboBox.SelectedValue;
                debt.PersonName = ((Person)DebtPersonComboBox.SelectedItem)?.Name ?? "";
                debt.Amount = (decimal)amount;
                debt.Date = DebtDatePicker.SelectedDate.Value;
                debt.DueDate = DueDatePicker.SelectedDate.Value;
                debt.Description = DebtDescriptionTextBox?.Text?.Trim() ?? "";
                debt.IsSettled = IsSettledCheckBox?.IsChecked == true;
                debt.PaymentDate = debt.IsSettled ? PaymentDatePicker?.SelectedDate : null;

                // حفظ في قاعدة البيانات
                try
                {
                    if (_isEditingDebt)
                    {
                        DatabaseHelper.UpdateDebt(debt);
                    }
                    else
                    {
                        DatabaseHelper.AddDebt(debt);
                        _allDebts.Add(debt);
                    }
                }
                catch (Exception dbEx)
                {
                    MessageBox.Show($"تم حفظ الدين في الذاكرة ولكن فشل حفظه في قاعدة البيانات: {dbEx.Message}",
                        "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);

                    // إضافة في الذاكرة على الأقل
                    if (!_isEditingDebt)
                    {
                        _allDebts.Add(debt);
                    }
                }

                // إعادة تطبيق الفلترة وتحديث القوائم
                UpdatePersonSummaries();
                FilterPersons();
                UpdateStatistics();

                // تحديث الصفحة الرئيسية
                RefreshMainDashboard();

                // إغلاق النموذج
                CloseDebtForm_Click(sender, e);

                var message = _isEditingDebt ? "تم تحديث الدين بنجاح" : "تم إضافة الدين بنجاح";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                // تسجيل النشاط
                if (_isEditingDebt)
                {
                    ActivityService.LogDebtUpdated(debt.PersonName, debt.Amount);
                }
                else
                {
                    ActivityService.LogDebtAdded(debt.PersonName, debt.Amount);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void OpenAddDebtFormForPerson(Person person)
        {
            try
            {
                // فتح نموذج إضافة دين
                ShowDebtForm(false, person);

                // تحديد الشخص مسبقاً في القائمة المنسدلة
                if (DebtPersonComboBox != null && _allPersons.Contains(person))
                {
                    DebtPersonComboBox.SelectedItem = person;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث الصفحة الرئيسية
        /// </summary>
        private void RefreshMainDashboard()
        {
            try
            {
                // تأخير صغير للتأكد من حفظ البيانات
                System.Threading.Tasks.Task.Delay(100).ContinueWith(_ =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        // البحث عن النافذة الرئيسية وتحديث الصفحة الرئيسية
                        var mainWindow = Application.Current.MainWindow as MainWindow;
                        if (mainWindow != null)
                        {
                            mainWindow.RefreshDashboard();
                            mainWindow.ForceRefreshActivities(); // إجبار تحديث الأنشطة

                            // تحديث صفحة التقارير إذا كانت مفتوحة
                            RefreshReportsPage(mainWindow);

                            System.Diagnostics.Debug.WriteLine("تم تحديث الصفحة الرئيسية والأنشطة والتقارير من صفحة إدارة الديون");
                        }
                    });
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الصفحة الرئيسية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث صفحة التقارير إذا كانت مفتوحة
        /// </summary>
        private void RefreshReportsPage(MainWindow mainWindow)
        {
            try
            {
                // البحث عن صفحة التقارير في المحتوى الحالي
                if (mainWindow.MainContent?.Content is ReportsView reportsView)
                {
                    reportsView.RefreshData();
                    System.Diagnostics.Debug.WriteLine("تم تحديث صفحة التقارير");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث صفحة التقارير: {ex.Message}");
            }
        }

        // تحديث البيانات (للمزامنة الشبكية)
        public async void RefreshData()
        {
            try
            {
                await LoadPersonSummaries();
                await LoadRecentDebts();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث بيانات الديون: {ex.Message}");
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
