<UserControl x:Class="DebtManagementApp.Views.NetworkSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/ModernDesignSystem.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  Style="{StaticResource ModernScrollViewer}"
                  PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
        <StackPanel Margin="24">
            
            <!-- عنوان الصفحة -->
            <Border Style="{StaticResource ModernCard}" Margin="0,0,0,24">
                <StackPanel>
                    <TextBlock Text="🌐 إعدادات الشبكة" Style="{StaticResource PageTitle}"/>
                    <TextBlock Text="ربط قاعدة البيانات بين حاسوبين على نفس الشبكة" 
                               Style="{StaticResource BodyText}" Margin="0,8,0,0"/>
                </StackPanel>
            </Border>

            <!-- معلومات الشبكة -->
            <Border Style="{StaticResource ModernCard}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="📡 معلومات الشبكة" Style="{StaticResource SectionTitle}"/>
                    
                    <Grid Margin="0,16,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="عنوان IP المحلي:" 
                                   VerticalAlignment="Center" Margin="0,0,16,8"
                                   FontWeight="SemiBold" Foreground="#495057"/>
                        <TextBox x:Name="LocalIPTextBox" Grid.Row="0" Grid.Column="1" 
                                 IsReadOnly="True" Margin="0,0,8,8"
                                 Style="{StaticResource ModernTextBox}"/>
                        <Button Grid.Row="0" Grid.Column="2" Content="🔄 تحديث" 
                                Click="RefreshIP_Click" Margin="0,0,0,8"
                                Style="{StaticResource SecondaryButton}"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="حالة الاتصال:" 
                                   VerticalAlignment="Center" Margin="0,0,16,0"
                                   FontWeight="SemiBold" Foreground="#495057"/>
                        <TextBlock x:Name="ConnectionStatusText" Grid.Row="1" Grid.Column="1" 
                                   Text="🔴 غير متصل" VerticalAlignment="Center"
                                   FontWeight="Bold" Foreground="#DC3545"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- إعدادات الخادم -->
            <Border Style="{StaticResource ModernCard}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="🖥️ إعدادات الخادم (Server)" Style="{StaticResource SectionTitle}"/>
                    <TextBlock Text="اجعل هذا الجهاز خادماً لمشاركة قاعدة البيانات" 
                               Style="{StaticResource BodyText}" Margin="0,8,0,16"/>
                    
                    <Button x:Name="StartServerButton" Content="🚀 بدء الخادم" 
                            Click="StartServer_Click" Margin="0,0,0,8"
                            Style="{StaticResource PrimaryButton}"/>
                    
                    <Button x:Name="StopServerButton" Content="⏹️ إيقاف الخادم" 
                            Click="StopServer_Click" Margin="0,0,0,0"
                            Style="{StaticResource DangerButton}" IsEnabled="False"/>
                </StackPanel>
            </Border>

            <!-- إعدادات العميل -->
            <Border Style="{StaticResource ModernCard}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="💻 إعدادات العميل (Client)" Style="{StaticResource SectionTitle}"/>
                    <TextBlock Text="اتصل بخادم آخر لمشاركة قاعدة البيانات" 
                               Style="{StaticResource BodyText}" Margin="0,8,0,16"/>
                    
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="عنوان IP الخادم:" 
                                   VerticalAlignment="Center" Margin="0,0,16,8"
                                   FontWeight="SemiBold" Foreground="#495057"/>
                        <TextBox x:Name="ServerIPTextBox" Grid.Row="0" Grid.Column="1" 
                                 Margin="0,0,8,8" Style="{StaticResource ModernTextBox}"
                                 Text="192.168.1."/>
                        <Button Grid.Row="0" Grid.Column="2" Content="🔍 بحث" 
                                Click="ScanNetwork_Click" Margin="0,0,0,8"
                                Style="{StaticResource SecondaryButton}"/>

                        <Button x:Name="ConnectButton" Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                                Content="🔗 اتصال بالخادم" Click="ConnectToServer_Click" 
                                Margin="0,0,8,8" Style="{StaticResource PrimaryButton}"/>
                        
                        <Button x:Name="DisconnectButton" Grid.Row="1" Grid.Column="2"
                                Content="❌ قطع الاتصال" Click="Disconnect_Click" 
                                Margin="0,0,0,8" Style="{StaticResource DangerButton}" IsEnabled="False"/>

                        <Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3"
                                Content="🔄 طلب مزامنة كاملة" Click="RequestSync_Click" 
                                Margin="0,0,0,0" Style="{StaticResource SecondaryButton}"
                                x:Name="SyncButton" IsEnabled="False"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- الأجهزة المكتشفة -->
            <Border Style="{StaticResource ModernCard}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="📱 الأجهزة المكتشفة في الشبكة" Style="{StaticResource SectionTitle}"/>

                    <ListBox x:Name="DevicesListBox" Height="120" Margin="0,16,0,8"
                             SelectionChanged="DevicesListBox_SelectionChanged"
                             Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" Margin="8">
                                    <TextBlock Text="🖥️" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"
                                               FontFamily="Consolas" FontSize="13"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <TextBlock x:Name="ScanStatusText" Text="اضغط 'بحث' للعثور على الأجهزة المتاحة"
                               Style="{StaticResource BodySmall}" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- سجل الأحداث -->
            <Border Style="{StaticResource ModernCard}">
                <StackPanel>
                    <Grid>
                        <TextBlock Text="📋 سجل الأحداث" Style="{StaticResource SectionTitle}"/>
                        <Button Content="🗑️ مسح" Click="ClearLog_Click"
                                HorizontalAlignment="Right" VerticalAlignment="Top"
                                Style="{StaticResource SecondaryButton}" Padding="8,4"/>
                    </Grid>

                    <ScrollViewer Height="200" Margin="0,16,0,0"
                                  VerticalScrollBarVisibility="Auto"
                                  Style="{StaticResource ModernScrollViewer}">
                        <TextBlock x:Name="LogTextBlock" Background="#F8F9FA"
                                   Padding="12" FontFamily="Consolas" FontSize="12"
                                   TextWrapping="Wrap" Text="جاهز للاتصال..."/>
                    </ScrollViewer>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</UserControl>
