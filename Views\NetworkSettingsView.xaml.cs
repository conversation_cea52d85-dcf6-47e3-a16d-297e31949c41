using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using DebtManagementApp.Services;

namespace DebtManagementApp.Views
{
    public partial class NetworkSettingsView : UserControl
    {
        private readonly DataSyncService _dataSyncService;
        private bool _isScanning;

        public NetworkSettingsView()
        {
            InitializeComponent();
            _dataSyncService = new DataSyncService();
            
            InitializeEvents();
            RefreshLocalIP();
        }

        private void InitializeEvents()
        {
            _dataSyncService.NetworkService.OnConnectionStatusChanged += OnConnectionStatusChanged;
            _dataSyncService.OnSyncLog += OnSyncLog;
            _dataSyncService.OnDataUpdated += OnDataUpdated;
        }

        private void RefreshLocalIP()
        {
            LocalIPTextBox.Text = NetworkService.GetLocalIPAddress();
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            Dispatcher.Invoke(() =>
            {
                if (isConnected)
                {
                    ConnectionStatusText.Text = "🟢 متصل";
                    ConnectionStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0x28, 0xA7, 0x45));
                    
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = _dataSyncService.NetworkService.IsServer;
                    ConnectButton.IsEnabled = false;
                    DisconnectButton.IsEnabled = true;
                    SyncButton.IsEnabled = true;
                }
                else
                {
                    ConnectionStatusText.Text = "🔴 غير متصل";
                    ConnectionStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0xDC, 0x35, 0x45));
                    
                    StartServerButton.IsEnabled = true;
                    StopServerButton.IsEnabled = false;
                    ConnectButton.IsEnabled = true;
                    DisconnectButton.IsEnabled = false;
                    SyncButton.IsEnabled = false;
                }
            });
        }

        private void OnSyncLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                LogTextBlock.Text += $"\n[{DateTime.Now:HH:mm:ss}] {message}";
                
                // التمرير لأسفل
                if (LogTextBlock.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToEnd();
                }
            });
        }

        private void OnDataUpdated()
        {
            // يمكن إضافة تحديث للواجهات الأخرى هنا
            OnSyncLog("🔄 تم تحديث البيانات المحلية");
        }

        private async void StartServer_Click(object sender, RoutedEventArgs e)
        {
            StartServerButton.IsEnabled = false;
            OnSyncLog("🚀 بدء تشغيل الخادم...");
            
            bool success = await _dataSyncService.NetworkService.StartServer();
            
            if (!success)
            {
                StartServerButton.IsEnabled = true;
            }
        }

        private void StopServer_Click(object sender, RoutedEventArgs e)
        {
            _dataSyncService.NetworkService.Disconnect();
            OnSyncLog("⏹️ تم إيقاف الخادم");
        }

        private async void ConnectToServer_Click(object sender, RoutedEventArgs e)
        {
            string serverIP = ServerIPTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(serverIP))
            {
                MessageBox.Show("يرجى إدخال عنوان IP الخادم", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            ConnectButton.IsEnabled = false;
            OnSyncLog($"🔗 محاولة الاتصال بـ {serverIP}...");
            
            bool success = await _dataSyncService.NetworkService.ConnectToServer(serverIP);
            
            if (!success)
            {
                ConnectButton.IsEnabled = true;
            }
            else
            {
                // طلب مزامنة تلقائية عند الاتصال
                await Task.Delay(1000);
                await _dataSyncService.RequestFullSync();
            }
        }

        private void Disconnect_Click(object sender, RoutedEventArgs e)
        {
            _dataSyncService.NetworkService.Disconnect();
        }

        private async void ScanNetwork_Click(object sender, RoutedEventArgs e)
        {
            if (_isScanning) return;

            _isScanning = true;
            ScanStatusText.Text = "🔍 جاري البحث عن الأجهزة...";
            DevicesListBox.Items.Clear();

            try
            {
                var devices = await NetworkService.ScanNetwork();
                
                foreach (var device in devices)
                {
                    DevicesListBox.Items.Add(device);
                }

                ScanStatusText.Text = $"تم العثور على {devices.Count} جهاز";
            }
            catch (Exception ex)
            {
                ScanStatusText.Text = $"خطأ في البحث: {ex.Message}";
            }
            finally
            {
                _isScanning = false;
            }
        }

        private void DevicesListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DevicesListBox.SelectedItem is string selectedIP)
            {
                ServerIPTextBox.Text = selectedIP;
            }
        }

        private async void RequestSync_Click(object sender, RoutedEventArgs e)
        {
            await _dataSyncService.RequestFullSync();
        }

        private void RefreshIP_Click(object sender, RoutedEventArgs e)
        {
            RefreshLocalIP();
            OnSyncLog("🔄 تم تحديث عنوان IP المحلي");
        }

        private void ClearLog_Click(object sender, RoutedEventArgs e)
        {
            LogTextBlock.Text = "جاهز للاتصال...";
        }

        // دالة تحسين التمرير بالماوس
        private void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (sender is ScrollViewer scrollViewer)
            {
                // تحسين سرعة التمرير مع السلاسة
                double scrollSpeed = 2.0;
                double targetOffset = scrollViewer.VerticalOffset - (e.Delta * scrollSpeed);
                
                // التأكد من أن القيمة ضمن الحدود المسموحة
                targetOffset = Math.Max(0, Math.Min(scrollViewer.ScrollableHeight, targetOffset));
                
                // تطبيق التمرير السلس
                SmoothScrollToOffset(scrollViewer, targetOffset);
                
                // منع التمرير الافتراضي
                e.Handled = true;
            }
        }

        // دالة التمرير السلس
        private void SmoothScrollToOffset(ScrollViewer scrollViewer, double targetOffset)
        {
            double startOffset = scrollViewer.VerticalOffset;
            double distance = targetOffset - startOffset;
            
            if (Math.Abs(distance) < 1) return;
            
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromMilliseconds(16); // 60 FPS
            
            DateTime startTime = DateTime.Now;
            double duration = 150; // مدة الحركة بالميلي ثانية
            
            timer.Tick += (s, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);
                
                // استخدام دالة Ease Out للسلاسة
                double easedProgress = 1 - Math.Pow(1 - progress, 3);
                
                double currentOffset = startOffset + (distance * easedProgress);
                scrollViewer.ScrollToVerticalOffset(currentOffset);
                
                if (progress >= 1.0)
                {
                    timer.Stop();
                }
            };
            
            timer.Start();
        }

        // تنظيف الموارد
        public void Cleanup()
        {
            _dataSyncService?.NetworkService?.Disconnect();
        }
    }
}
