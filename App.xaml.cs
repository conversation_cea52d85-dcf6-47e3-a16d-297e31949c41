﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace DebtManagementApp
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // إعداد معالج الأخطاء العامة
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                DispatcherUnhandledException += OnDispatcherUnhandledException;

                // تهيئة قاعدة البيانات باستخدام DatabaseHelper
                try
                {
                    DatabaseHelper.InitializeDatabase();
                }
                catch (Exception dbEx)
                {
                    MessageBox.Show($"خطأ في قاعدة البيانات: {dbEx.Message}", "خطأ قاعدة البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في تهيئة التطبيق:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", "خطأ فادح",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            MessageBox.Show($"خطأ في التطبيق:\n{e.Exception.Message}\n\nStack Trace:\n{e.Exception.StackTrace}",
                "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            e.Handled = true;
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            MessageBox.Show($"خطأ فادح:\n{exception?.Message}\n\nStack Trace:\n{exception?.StackTrace}",
                "خطأ فادح", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            base.OnExit(e);
        }

        // دالة تحسين التمرير بالماوس
        public static void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (sender is ScrollViewer scrollViewer)
            {
                // تحسين سرعة التمرير - ضرب في 3 لجعل التمرير أسرع
                double scrollSpeed = 3.0;
                double newOffset = scrollViewer.VerticalOffset - (e.Delta * scrollSpeed);

                // التأكد من أن القيمة ضمن الحدود المسموحة
                newOffset = Math.Max(0, Math.Min(scrollViewer.ScrollableHeight, newOffset));

                // تطبيق التمرير الجديد
                scrollViewer.ScrollToVerticalOffset(newOffset);

                // منع التمرير الافتراضي
                e.Handled = true;
            }
        }
    }
}

