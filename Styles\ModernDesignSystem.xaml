<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 🎨 نظام الألوان العصري -->
    
    <!-- الألوان الأساسية -->
    <SolidColorBrush x:Key="PrimaryColor" Color="#6366F1"/>
    <SolidColorBrush x:Key="PrimaryLight" Color="#A5B4FC"/>
    <SolidColorBrush x:Key="PrimaryDark" Color="#4338CA"/>
    
    <SolidColorBrush x:Key="SecondaryColor" Color="#10B981"/>
    <SolidColorBrush x:Key="SecondaryLight" Color="#6EE7B7"/>
    <SolidColorBrush x:Key="SecondaryDark" Color="#047857"/>
    
    <SolidColorBrush x:Key="AccentColor" Color="#F59E0B"/>
    <SolidColorBrush x:Key="AccentLight" Color="#FCD34D"/>
    <SolidColorBrush x:Key="AccentDark" Color="#D97706"/>
    
    <SolidColorBrush x:Key="ErrorColor" Color="#EF4444"/>
    <SolidColorBrush x:Key="ErrorLight" Color="#FCA5A5"/>
    <SolidColorBrush x:Key="ErrorDark" Color="#DC2626"/>

    <!-- ألوان إضافية -->
    
    <!-- ألوان النص -->
    <SolidColorBrush x:Key="TextPrimary" Color="#1F2937"/>
    <SolidColorBrush x:Key="TextSecondary" Color="#6B7280"/>
    <SolidColorBrush x:Key="TextSecondaryColor" Color="#6B7280"/>
    <SolidColorBrush x:Key="TextMuted" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="TextInverse" Color="#FFFFFF"/>
    
    <!-- ألوان الخلفية -->
    <SolidColorBrush x:Key="BackgroundColor" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="BackgroundPrimary" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="BackgroundSecondary" Color="#F9FAFB"/>
    <SolidColorBrush x:Key="BackgroundTertiary" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="BackgroundDark" Color="#111827"/>
    
    <!-- ألوان الحدود -->
    <SolidColorBrush x:Key="BorderLight" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="BorderMedium" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="BorderDark" Color="#9CA3AF"/>
    
    <!-- التدرجات الحديثة -->
    <LinearGradientBrush x:Key="PrimaryGradient" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#8B5CF6" Offset="0"/>
        <GradientStop Color="#6366F1" Offset="0.5"/>
        <GradientStop Color="#3B82F6" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="SecondaryGradient" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#34D399" Offset="0"/>
        <GradientStop Color="#10B981" Offset="0.5"/>
        <GradientStop Color="#059669" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="AccentGradient" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#FBBF24" Offset="0"/>
        <GradientStop Color="#F59E0B" Offset="0.5"/>
        <GradientStop Color="#D97706" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="ErrorGradient" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#F87171" Offset="0"/>
        <GradientStop Color="#EF4444" Offset="0.5"/>
        <GradientStop Color="#DC2626" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- خلفية التطبيق -->
    <LinearGradientBrush x:Key="AppBackground" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#F8FAFC" Offset="0"/>
        <GradientStop Color="#F1F5F9" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- 🎭 الظلال الحديثة -->
    <DropShadowEffect x:Key="SoftShadow" Color="#10000000" Direction="270" ShadowDepth="4" BlurRadius="16" Opacity="0.1"/>
    <DropShadowEffect x:Key="MediumShadow" Color="#20000000" Direction="270" ShadowDepth="8" BlurRadius="24" Opacity="0.15"/>
    <DropShadowEffect x:Key="StrongShadow" Color="#30000000" Direction="270" ShadowDepth="12" BlurRadius="32" Opacity="0.2"/>
    
    <!-- 🎨 أنماط البطاقات الحديثة -->
    <Style x:Key="ModernCard" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource BackgroundPrimary}"/>
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderLight}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Effect" Value="{StaticResource SoftShadow}"/>
    </Style>
    
    <Style x:Key="ElevatedCard" TargetType="Border" BasedOn="{StaticResource ModernCard}">
        <Setter Property="Effect" Value="{StaticResource MediumShadow}"/>
    </Style>
    
    <Style x:Key="HighlightCard" TargetType="Border" BasedOn="{StaticResource ModernCard}">
        <Setter Property="Effect" Value="{StaticResource StrongShadow}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryLight}"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>
    
    <!-- 🎯 أنماط الأزرار الحديثة -->
    <Style x:Key="PrimaryButton" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryGradient}"/>
        <Setter Property="Foreground" Value="{StaticResource TextInverse}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="24,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}" 
                            CornerRadius="12" 
                            Padding="{TemplateBinding Padding}"
                            Effect="{StaticResource SoftShadow}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform Y="-2"/>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="border" Property="Effect" Value="{StaticResource MediumShadow}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <TranslateTransform Y="1"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
        <Setter Property="Background" Value="{StaticResource SecondaryGradient}"/>
    </Style>
    
    <Style x:Key="AccentButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
        <Setter Property="Background" Value="{StaticResource AccentGradient}"/>
    </Style>

    <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#DC3545" Offset="0"/>
                    <GradientStop Color="#C82333" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="OutlineButton" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="24,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}" 
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12" 
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryLight}"/>
                            <Setter Property="Foreground" Value="{StaticResource TextInverse}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 📝 أنماط النصوص الحديثة -->

    <Style x:Key="HeadingXLarge" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="32"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
        <Setter Property="LineHeight" Value="40"/>
    </Style>

    <Style x:Key="HeadingLarge" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="28"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
        <Setter Property="LineHeight" Value="36"/>
    </Style>

    <Style x:Key="HeadingMedium" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
        <Setter Property="LineHeight" Value="32"/>
    </Style>

    <Style x:Key="HeadingSmall" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
        <Setter Property="LineHeight" Value="28"/>
    </Style>

    <Style x:Key="BodyLarge" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
        <Setter Property="LineHeight" Value="26"/>
    </Style>

    <Style x:Key="BodyMedium" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
        <Setter Property="LineHeight" Value="24"/>
    </Style>

    <Style x:Key="BodySmall" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondary}"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>

</ResourceDictionary>
