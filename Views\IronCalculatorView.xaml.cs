using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace DebtManagementApp.Views
{
    public partial class IronCalculatorView : UserControl
    {
        public IronCalculatorView()
        {
            InitializeComponent();
        }

        private void Reset_Click(object sender, RoutedEventArgs e)
        {
            ResetResults();
        }

        private void SaveResult_Click(object sender, RoutedEventArgs e)
        {
            // حفظ النتائج في الحافظة أو أي منطق مطلوب
            MessageBox.Show("تم حفظ النتيجة بنجاح!", "حفظ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void IronCalculatorView_Loaded(object sender, RoutedEventArgs e)
        {
            ResetResults();
        }

        // الحسابات الجديدة المبسطة
        private void Input_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateIronCost();
        }

        private void ResetResults()
        {
            // إعادة تعيين النتائج لعناصر XAML مباشرة
            var tonPriceUsd = this.FindName("TonPriceUsdTextBox") as TextBox;
            var usdToIqd = this.FindName("UsdToIqdRateTextBox") as TextBox;
            var length = this.FindName("LengthTextBox") as TextBox;
            var width = this.FindName("WidthTextBox") as TextBox;
            var thickness = this.FindName("ThicknessTextBox") as TextBox;
            var transport = this.FindName("TransportCostTextBox") as TextBox;
            var area = this.FindName("AreaText") as TextBlock;
            var volume = this.FindName("VolumeText") as TextBlock;
            var weight = this.FindName("WeightText") as TextBlock;
            var priceUsd = this.FindName("PriceUsdText") as TextBlock;
            var priceIqd = this.FindName("PriceIqdText") as TextBlock;
            var finalPrice = this.FindName("FinalPriceText") as TextBlock;

            if (tonPriceUsd != null) tonPriceUsd.Text = "";
            if (usdToIqd != null) usdToIqd.Text = "";
            if (length != null) length.Text = "";
            if (width != null) width.Text = "";
            if (thickness != null) thickness.Text = "";
            if (transport != null) transport.Text = "";
            if (area != null) area.Text = "0";
            if (volume != null) volume.Text = "0";
            if (weight != null) weight.Text = "0";
            if (priceUsd != null) priceUsd.Text = "0";
            if (priceIqd != null) priceIqd.Text = "0";
            if (finalPrice != null) finalPrice.Text = "0";
        }

        private void CalculateIronCost()
        {
            try
            {
                // قراءة القيم من عناصر XAML مباشرة
                var tonPriceUsdBox = this.FindName("TonPriceUsdTextBox") as TextBox;
                var usdToIqdBox = this.FindName("UsdToIqdRateTextBox") as TextBox;
                var lengthBox = this.FindName("LengthTextBox") as TextBox;
                var widthBox = this.FindName("WidthTextBox") as TextBox;
                var thicknessBox = this.FindName("ThicknessTextBox") as TextBox;
            // عناصر النتائج
            var basicCostText = this.FindName("BasicCostText") as TextBlock;
            var additionalFeesText = this.FindName("AdditionalFeesText") as TextBlock;
            var totalCostText = this.FindName("TotalCostText") as TextBlock;
            var taxCostText = this.FindName("TaxCostText") as TextBlock;
            var transportCostText = this.FindName("TransportCostText") as TextBlock;

            double tonPriceUsd = tonPriceUsdBox != null ? ParseDouble(tonPriceUsdBox.Text) : 0;
            double usdToIqd = usdToIqdBox != null ? ParseDouble(usdToIqdBox.Text) : 0;
            double length = lengthBox != null ? ParseDouble(lengthBox.Text) : 0;
            double width = widthBox != null ? ParseDouble(widthBox.Text) : 0;
            double thicknessMm = thicknessBox != null ? ParseDouble(thicknessBox.Text) : 0;
            // رسوم إضافية (مدخلة يدويًا)
            var transportCostBox = this.FindName("TransportCostTextBox") as TextBox;
            double additionalFees = transportCostBox != null ? ParseDouble(transportCostBox.Text) : 0;

            // التحقق من صحة القيم
            if (tonPriceUsd <= 0 || usdToIqd <= 0 || length <= 0 || width <= 0 || thicknessMm <= 0)
            {
                ResetResults();
                return;
            }

            // الحسابات
            double area = length * width;
            double volume = area * (thicknessMm / 1000.0); // m³
            double weight = volume * 7850; // كغم (كثافة الحديد الصحيحة)
            double priceUsd = (weight / 1000.0) * tonPriceUsd;
            double priceIqd = priceUsd * usdToIqd;
            double basicCost = priceIqd; // سعر الحديد بدون النقل
            double transportCost = basicCost * 0.075; // تكلفة النقل = 7.5% من سعر الحديد بدون النقل
            double totalCost = basicCost + transportCost + additionalFees;

            // تحديث النتائج
            if (basicCostText != null) basicCostText.Text = basicCost.ToString("N0") + " دينار";
            if (transportCostText != null) transportCostText.Text = transportCost.ToString("N0") + " دينار";
            if (additionalFeesText != null) additionalFeesText.Text = additionalFees.ToString("N0") + " دينار";
            if (totalCostText != null) totalCostText.Text = totalCost.ToString("N0") + " دينار";
            if (taxCostText != null) taxCostText.Text = weight.ToString("N0") + " كغم";
        }

        private double ParseDouble(string? text)
        {
            if (double.TryParse(text, out double v))
                return v;
            return 0;
        }
    }
}
