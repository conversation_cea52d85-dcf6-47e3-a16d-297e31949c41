using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using System.Windows.Controls;
using System;
using System.Collections.Generic;
using System.Linq;
using DebtManagementApp.ViewModels;
using DebtManagementApp.Helpers;
using DebtManagementApp.Services;
using DebtManagementApp.Models;
using Microsoft.Data.Sqlite;

namespace DebtManagementApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private DispatcherTimer _timeTimer;
        private DateTime _lastActivitiesUpdate = DateTime.MinValue;

        public MainWindow()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🏗️ بدء إنشاء MainWindow...");
                InitializeComponent();
                System.Diagnostics.Debug.WriteLine("✅ تم تحميل XAML بنجاح");

                // تأجيل إنشاء ViewModel لتجنب مشاكل قاعدة البيانات
                System.Diagnostics.Debug.WriteLine("📊 إنشاء MainViewModel...");
                DataContext = new MainViewModel();
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء MainViewModel بنجاح");

                // إعداد مؤقت الوقت
                _timeTimer = new DispatcherTimer();
                _timeTimer.Interval = TimeSpan.FromSeconds(1);
                _timeTimer.Tick += UpdateTime;
                _timeTimer.Start();

                // تحديث الوقت فوراً
                UpdateTime(null, null);

                // تسجيل حدث إغلاق النافذة لحفظ إعدادات الأعمدة
                this.Closing += MainWindow_Closing;

                // الاشتراك في خدمة الأنشطة
                ActivityService.ActivityAdded += OnActivityAdded;

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء MainWindow بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء MainWindow: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء النافذة الرئيسية:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                    "خطأ فادح", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // التأكد من أن النافذة تظهر في المقدمة
            this.WindowState = WindowState.Normal;
            this.Activate();
            this.Topmost = true;
            this.Topmost = false;
            this.Focus();

            // تحديث البيانات عند تحميل النافذة
            UpdateDashboardStatistics();

            if (DataContext is MainViewModel viewModel)
            {
                viewModel.RefreshDataCommand?.Execute(null);
            }
        }

        private void UpdateDashboardStatistics()
        {
            try
            {
                // تحديث الإحصائيات بناءً على البيانات الفعلية من قاعدة البيانات
                UpdatePersonsStatistics();
                UpdateDebtsStatistics();
                UpdateRecentActivities();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث إحصائيات الصفحة الرئيسية: {ex.Message}");
            }
        }

        private void UpdatePersonsStatistics()
        {
            try
            {
                var persons = DatabaseHelper.GetAllPersons();
                if (TotalPersonsCount != null)
                {
                    TotalPersonsCount.Text = persons.Count.ToString();
                    System.Diagnostics.Debug.WriteLine($"تم تحديث عدد الأشخاص: {persons.Count}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث إحصائيات الأشخاص: {ex.Message}");
                if (TotalPersonsCount != null)
                    TotalPersonsCount.Text = "0";
            }
        }

        private void UpdateDebtsStatistics()
        {
            try
            {
                var debts = DatabaseHelper.GetAllDebts();
                var overdueDebts = DatabaseHelper.GetOverdueDebts();

                if (TotalDebtsCount != null)
                {
                    TotalDebtsCount.Text = debts.Count.ToString();
                    System.Diagnostics.Debug.WriteLine($"تم تحديث عدد الديون: {debts.Count}");
                }

                if (OverdueDebtsCount != null)
                {
                    OverdueDebtsCount.Text = overdueDebts.Count.ToString();
                    System.Diagnostics.Debug.WriteLine($"تم تحديث عدد الديون المتأخرة: {overdueDebts.Count}");
                }

                if (TotalAmountDisplay != null)
                {
                    var totalAmount = debts.Where(d => !d.IsSettled).Sum(d => d.Amount);
                    TotalAmountDisplay.Text = CalculationHelper.FormatIqdCurrency(totalAmount);
                    System.Diagnostics.Debug.WriteLine($"تم تحديث إجمالي المبلغ: {totalAmount}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث إحصائيات الديون: {ex.Message}");
                if (TotalDebtsCount != null)
                    TotalDebtsCount.Text = "0";
                if (OverdueDebtsCount != null)
                    OverdueDebtsCount.Text = "0";
                if (TotalAmountDisplay != null)
                    TotalAmountDisplay.Text = "0 دينار";
            }
        }

        private void UpdateRecentActivities()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تحديث الأنشطة الأخيرة...");

                if (RecentActivitiesList != null)
                {
                    var activities = new List<object>();

                    // قراءة الأنشطة من قاعدة البيانات
                    var recentActivities = DatabaseHelper.GetRecentActivities(10);

                    System.Diagnostics.Debug.WriteLine($"تم العثور على {recentActivities.Count} أنشطة حديثة");

                    foreach (var activity in recentActivities)
                    {
                        var timeAgo = GetTimeAgo(activity.Date);

                        activities.Add(new ActivityItem
                        {
                            Id = activity.Id,
                            Description = activity.Description,
                            Time = timeAgo,
                            Icon = GetActivityIcon(activity.Type),
                            Type = activity.Type,
                            Date = activity.Date
                        });

                        System.Diagnostics.Debug.WriteLine($"نشاط مضاف: {activity.Description} - {activity.Date}");
                    }

                    // إذا لم توجد أنشطة، أضف رسالة
                    if (activities.Count == 0)
                    {
                        activities.Add(new ActivityItem
                        {
                            Id = 0,
                            Description = "لا توجد أنشطة حديثة",
                            Time = "",
                            Icon = "📝",
                            Type = "Empty"
                        });
                        System.Diagnostics.Debug.WriteLine("لا توجد أنشطة حديثة");
                    }

                    // تحديث قائمة الأنشطة
                    var finalActivities = activities.Take(5).ToList();
                    RecentActivitiesList.ItemsSource = null; // إعادة تعيين أولاً
                    RecentActivitiesList.ItemsSource = finalActivities;

                    // تحديث وقت آخر تحديث
                    _lastActivitiesUpdate = DateTime.Now;

                    System.Diagnostics.Debug.WriteLine($"تم تحديث قائمة الأنشطة بـ {finalActivities.Count} نشاط في {_lastActivitiesUpdate}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("RecentActivitiesList is null!");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الأنشطة الأخيرة: {ex.Message}");
                if (RecentActivitiesList != null)
                {
                    RecentActivitiesList.ItemsSource = new[] {
                        new { Description = "خطأ في تحميل الأنشطة", Time = "" }
                    };
                }
            }
        }

        private string GetTimeAgo(DateTime date)
        {
            var timeSpan = DateTime.Now - date;

            if (timeSpan.Days > 0)
                return $"منذ {timeSpan.Days} يوم";
            else if (timeSpan.Hours > 0)
                return $"منذ {timeSpan.Hours} ساعة";
            else if (timeSpan.Minutes > 0)
                return $"منذ {timeSpan.Minutes} دقيقة";
            else
                return "الآن";
        }

        /// <summary>
        /// دالة عامة لتحديث الصفحة الرئيسية - يمكن استدعاؤها من الصفحات الأخرى
        /// </summary>
        public void RefreshDashboard()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تحديث الصفحة الرئيسية...");
                UpdateDashboardStatistics();
                System.Diagnostics.Debug.WriteLine("تم تحديث الصفحة الرئيسية بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الصفحة الرئيسية: {ex.Message}");
            }
        }

        private void OnActivityAdded(object? sender, EventArgs e)
        {
            try
            {
                // تحديث الأنشطة في الخيط الرئيسي
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    ForceRefreshActivities();
                }));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة حدث النشاط: {ex.Message}");
            }
        }

        private string GetActivityIcon(string activityType)
        {
            return activityType switch
            {
                "Person" => "👤",
                "Debt" => "💰",
                "FactoryDebt" => "🏭",
                "Salary" => "💵",
                "Worker" => "👷",
                "Reminder" => "⏰",
                "Backup" => "💾",
                _ => "📝"
            };
        }

        private void DeleteActivity_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedActivity = RecentActivitiesList.SelectedItem as ActivityItem;
                if (selectedActivity == null || selectedActivity.Id == 0)
                {
                    MessageBox.Show("يرجى اختيار نشاط صالح للحذف", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف هذا النشاط؟\n\n{selectedActivity.Description}",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    DatabaseHelper.DeleteActivity(selectedActivity.Id);
                    ForceRefreshActivities();

                    MessageBox.Show("تم حذف النشاط بنجاح", "تم الحذف",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف النشاط: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UndoActivity_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedActivity = RecentActivitiesList.SelectedItem as ActivityItem;
                if (selectedActivity == null || selectedActivity.Id == 0)
                {
                    MessageBox.Show("يرجى اختيار نشاط صالح للتراجع عنه", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من التراجع عن هذا النشاط؟\n\n{selectedActivity.Description}\n\nتحذير: هذا الإجراء قد يؤثر على البيانات المرتبطة.",
                    "تأكيد التراجع",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Debug.WriteLine($"بدء عملية التراجع للنشاط: {selectedActivity.Description}");
                    bool undoSuccess = UndoActivityAction(selectedActivity);
                    System.Diagnostics.Debug.WriteLine($"نتيجة التراجع: {undoSuccess}");

                    if (undoSuccess)
                    {
                        // حذف النشاط من قاعدة البيانات بعد التراجع الناجح
                        DatabaseHelper.DeleteActivity(selectedActivity.Id);
                        ForceRefreshActivities();

                        // تحديث جميع الواجهات المفتوحة
                        RefreshAllViews();

                        MessageBox.Show("تم التراجع عن النشاط بنجاح", "تم التراجع",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("فشل التراجع - عرض رسالة للمستخدم");
                        MessageBox.Show("لا يمكن التراجع عن هذا النشاط تلقائياً.\nيرجى التراجع يدوياً من القسم المناسب.",
                            "تعذر التراجع", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التراجع عن النشاط: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool UndoActivityAction(ActivityItem activity)
        {
            try
            {
                // تحليل وصف النشاط لاستخراج المعلومات
                var description = activity.Description;
                var activityType = activity.Type;

                System.Diagnostics.Debug.WriteLine($"محاولة التراجع عن النشاط: {description}");
                System.Diagnostics.Debug.WriteLine($"نوع النشاط: {activityType}");

                // التراجع حسب نوع النشاط
                switch (activityType)
                {
                    case "Person":
                        System.Diagnostics.Debug.WriteLine("معالجة نشاط الأشخاص");
                        return UndoPersonActivity(description);

                    case "Debt":
                        System.Diagnostics.Debug.WriteLine("معالجة نشاط الديون");
                        return UndoDebtActivity(description);

                    case "FactoryDebt":
                        System.Diagnostics.Debug.WriteLine("معالجة نشاط ديون المعمل");
                        return UndoFactoryDebtActivity(description);

                    case "Worker":
                        System.Diagnostics.Debug.WriteLine("معالجة نشاط العمال");
                        return UndoWorkerActivity(description);

                    case "Salary":
                        System.Diagnostics.Debug.WriteLine("معالجة نشاط الرواتب");
                        return UndoSalaryActivity(description);

                    default:
                        System.Diagnostics.Debug.WriteLine($"نوع نشاط غير مدعوم: {activityType}");
                        MessageBox.Show($"نوع النشاط '{activityType}' غير مدعوم للتراجع التلقائي.\nيرجى التراجع يدوياً من القسم المناسب.",
                            "نوع غير مدعوم", MessageBoxButton.OK, MessageBoxImage.Information);
                        return false; // لا يمكن التراجع عن هذا النوع
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التراجع عن النشاط: {ex.Message}");
                MessageBox.Show($"خطأ في التراجع عن النشاط: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private bool UndoPersonActivity(string description)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"تحليل وصف نشاط الشخص: {description}");

                // استخراج اسم الشخص من وصف النشاط
                if (description.Contains("تم إضافة شخص جديد:"))
                {
                    var personName = description.Replace("تم إضافة شخص جديد:", "").Trim();
                    System.Diagnostics.Debug.WriteLine($"اسم الشخص المستخرج: '{personName}'");
                    return UndoPersonAddition(personName);
                }
                else if (description.Contains("تم حذف الشخص:"))
                {
                    var personName = description.Replace("تم حذف الشخص:", "").Trim();
                    System.Diagnostics.Debug.WriteLine($"محاولة التراجع عن حذف الشخص: {personName}");
                    MessageBox.Show($"التراجع عن حذف الأشخاص قيد التطوير.\nيرجى إعادة إضافة الشخص '{personName}' يدوياً من قسم إدارة الأشخاص.",
                        "ميزة قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
                    return false;
                }
                else if (description.Contains("تم تحديث بيانات:"))
                {
                    System.Diagnostics.Debug.WriteLine("نشاط تحديث بيانات - لا يمكن التراجع");
                    MessageBox.Show("لا يمكن التراجع عن تحديث بيانات الأشخاص تلقائياً.\nيرجى التراجع يدوياً من قسم إدارة الأشخاص.",
                        "تعذر التراجع", MessageBoxButton.OK, MessageBoxImage.Information);
                    return false;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("نوع نشاط شخص غير معروف");
                    MessageBox.Show("نوع نشاط الشخص غير معروف للتراجع التلقائي.\nيرجى التراجع يدوياً من قسم إدارة الأشخاص.",
                        "نوع غير معروف", MessageBoxButton.OK, MessageBoxImage.Information);
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التراجع عن نشاط الشخص: {ex.Message}");
                MessageBox.Show($"خطأ في التراجع عن نشاط الشخص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private bool UndoPersonAddition(string personName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"البحث عن الشخص: '{personName}'");

                // البحث عن الشخص
                var persons = DatabaseHelper.GetAllPersons();
                System.Diagnostics.Debug.WriteLine($"تم العثور على {persons.Count} أشخاص في قاعدة البيانات");

                var person = persons.FirstOrDefault(p => p.Name == personName);

                if (person != null)
                {
                    System.Diagnostics.Debug.WriteLine($"تم العثور على الشخص: {person.Name} (ID: {person.Id})");

                    // التحقق من وجود ديون مرتبطة
                    var debts = DatabaseHelper.GetDebtsByPersonId(person.Id);
                    var factoryDebts = DatabaseHelper.GetFactoryDebtsByPersonId(person.Id);

                    System.Diagnostics.Debug.WriteLine($"عدد الديون العادية: {debts.Count}");
                    System.Diagnostics.Debug.WriteLine($"عدد ديون المعمل: {factoryDebts.Count}");

                    if (debts.Any() || factoryDebts.Any())
                    {
                        var result = MessageBox.Show($"تحذير: الشخص '{personName}' لديه ديون مرتبطة!\n\nالديون العادية: {debts.Count}\nديون المعمل: {factoryDebts.Count}\n\nهل تريد المتابعة؟ سيتم حذف الشخص مع جميع ديونه.",
                            "تحذير: ديون مرتبطة", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                        if (result == MessageBoxResult.No)
                        {
                            System.Diagnostics.Debug.WriteLine("المستخدم ألغى التراجع بسبب وجود ديون");
                            return false;
                        }

                        System.Diagnostics.Debug.WriteLine("المستخدم وافق على حذف الشخص مع ديونه");
                    }

                    // حذف الشخص إذا لم توجد ديون
                    System.Diagnostics.Debug.WriteLine($"محاولة حذف الشخص: {personName} (ID: {person.Id})");

                    try
                    {
                        // التحقق من وجود الشخص قبل الحذف
                        var personsBeforeDelete = DatabaseHelper.GetAllPersons();
                        var personExistsBeforeDelete = personsBeforeDelete.Any(p => p.Id == person.Id);
                        System.Diagnostics.Debug.WriteLine($"الشخص موجود قبل الحذف: {personExistsBeforeDelete}");

                        DatabaseHelper.DeletePerson(person.Id);
                        System.Diagnostics.Debug.WriteLine("تم استدعاء دالة حذف الشخص");

                        // التحقق من حذف الشخص فعلياً
                        var personsAfterDelete = DatabaseHelper.GetAllPersons();
                        var personExistsAfterDelete = personsAfterDelete.Any(p => p.Id == person.Id);
                        System.Diagnostics.Debug.WriteLine($"الشخص موجود بعد الحذف: {personExistsAfterDelete}");

                        if (!personExistsAfterDelete)
                        {
                            System.Diagnostics.Debug.WriteLine("تم حذف الشخص من قاعدة البيانات بنجاح");

                            ActivityService.LogActivity($"تم التراجع عن إضافة الشخص: {personName}", "UndoAction");
                            System.Diagnostics.Debug.WriteLine("تم تسجيل نشاط التراجع");

                            System.Diagnostics.Debug.WriteLine("التراجع مكتمل بنجاح");
                            return true;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("فشل في حذف الشخص من قاعدة البيانات");
                            MessageBox.Show("فشل في حذف الشخص من قاعدة البيانات", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                            return false;
                        }
                    }
                    catch (Exception deleteEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في حذف الشخص: {deleteEx.Message}");
                        MessageBox.Show($"خطأ في حذف الشخص من قاعدة البيانات: {deleteEx.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"لم يتم العثور على الشخص: '{personName}'");
                    MessageBox.Show($"لم يتم العثور على الشخص '{personName}' في قاعدة البيانات.\nقد يكون تم حذفه مسبقاً.",
                        "شخص غير موجود", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التراجع عن إضافة الشخص: {ex.Message}");
                MessageBox.Show($"خطأ في التراجع عن إضافة الشخص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }



        private bool UndoDebtActivity(string description)
        {
            try
            {
                if (description.Contains("تم إضافة دين لـ") && description.Contains("بمبلغ"))
                {
                    return UndoDebtAddition(description);
                }
                else if (description.Contains("تم سداد دين"))
                {
                    return UndoDebtPayment(description);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التراجع عن نشاط الدين: {ex.Message}");
            }
            return false;
        }

        private bool UndoDebtAddition(string description)
        {
            try
            {
                // استخراج اسم الشخص والمبلغ من الوصف
                // مثال: "تم إضافة دين لـ أحمد محمد بمبلغ 50,000 دينار"
                var parts = description.Split(new[] { "تم إضافة دين لـ ", " بمبلغ ", " دينار" }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 2)
                {
                    var personName = parts[0].Trim();
                    var amountStr = parts[1].Replace(",", "").Trim();

                    if (decimal.TryParse(amountStr, out decimal amount))
                    {
                        // البحث عن الدين الأحدث لهذا الشخص بنفس المبلغ
                        var persons = DatabaseHelper.GetAllPersons();
                        var person = persons.FirstOrDefault(p => p.Name == personName);

                        if (person != null)
                        {
                            var debts = DatabaseHelper.GetDebtsByPersonId(person.Id)
                                .Where(d => d.Amount == amount)
                                .OrderByDescending(d => d.Date)
                                .ToList();

                            if (debts.Any())
                            {
                                var latestDebt = debts.First();
                                System.Diagnostics.Debug.WriteLine($"محاولة حذف الدين ID: {latestDebt.Id} للشخص: {personName} بمبلغ: {amount}");

                                // التحقق من وجود الدين قبل الحذف
                                var debtsBeforeDelete = DatabaseHelper.GetDebtsByPersonId(person.Id);
                                var debtExistsBeforeDelete = debtsBeforeDelete.Any(d => d.Id == latestDebt.Id);
                                System.Diagnostics.Debug.WriteLine($"الدين موجود قبل الحذف: {debtExistsBeforeDelete}");

                                DatabaseHelper.DeleteDebt(latestDebt.Id);
                                System.Diagnostics.Debug.WriteLine("تم استدعاء دالة حذف الدين");

                                // التحقق من حذف الدين فعلياً
                                var debtsAfterDelete = DatabaseHelper.GetDebtsByPersonId(person.Id);
                                var debtExistsAfterDelete = debtsAfterDelete.Any(d => d.Id == latestDebt.Id);
                                System.Diagnostics.Debug.WriteLine($"الدين موجود بعد الحذف: {debtExistsAfterDelete}");

                                if (!debtExistsAfterDelete)
                                {
                                    System.Diagnostics.Debug.WriteLine("تم حذف الدين من قاعدة البيانات بنجاح");
                                    ActivityService.LogActivity($"تم التراجع عن إضافة دين {personName} بمبلغ {amount:N0} دينار", "UndoAction");
                                    return true;
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine("فشل في حذف الدين من قاعدة البيانات");
                                    MessageBox.Show("فشل في حذف الدين من قاعدة البيانات", "خطأ",
                                        MessageBoxButton.OK, MessageBoxImage.Error);
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التراجع عن إضافة الدين: {ex.Message}");
            }
            return false;
        }

        private bool UndoDebtPayment(string description)
        {
            // التراجع عن سداد الدين معقد ويحتاج تأكيد إضافي
            MessageBox.Show("التراجع عن سداد الديون يحتاج إجراءات محاسبية دقيقة.\nيرجى التراجع يدوياً من قسم إدارة الديون.",
                "تعذر التراجع التلقائي", MessageBoxButton.OK, MessageBoxImage.Information);
            return false;
        }

        private bool UndoFactoryDebtActivity(string description)
        {
            try
            {
                if (description.Contains("تم إضافة دين معمل") && description.Contains("بمبلغ"))
                {
                    return UndoFactoryDebtAddition(description);
                }
                else if (description.Contains("تم سداد دين معمل"))
                {
                    MessageBox.Show("التراجع عن سداد ديون المعمل يحتاج إجراءات محاسبية دقيقة.\nيرجى التراجع يدوياً من قسم ديون المعمل.",
                        "تعذر التراجع التلقائي", MessageBoxButton.OK, MessageBoxImage.Information);
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التراجع عن نشاط دين المعمل: {ex.Message}");
            }
            return false;
        }

        private bool UndoFactoryDebtAddition(string description)
        {
            try
            {
                // مثال: "تم إضافة دين معمل (مواد) لـ أحمد محمد بمبلغ 100,000 دينار"
                var parts = description.Split(new[] { "لـ ", " بمبلغ ", " دينار" }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 2)
                {
                    var personName = parts[1].Trim();
                    var amountStr = parts[2].Replace(",", "").Trim();

                    if (decimal.TryParse(amountStr, out decimal amount))
                    {
                        var persons = DatabaseHelper.GetAllPersons();
                        var person = persons.FirstOrDefault(p => p.Name == personName);

                        if (person != null)
                        {
                            var factoryDebts = DatabaseHelper.GetFactoryDebtsByPersonId(person.Id)
                                .Where(d => d.Amount == amount)
                                .OrderByDescending(d => d.CreatedDate)
                                .ToList();

                            if (factoryDebts.Any())
                            {
                                var latestDebt = factoryDebts.First();
                                DatabaseHelper.DeleteFactoryDebt(latestDebt.Id);
                                ActivityService.LogActivity($"تم التراجع عن إضافة دين معمل {personName} بمبلغ {amount:N0} دينار", "UndoAction");
                                return true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التراجع عن إضافة دين المعمل: {ex.Message}");
            }
            return false;
        }

        private bool UndoWorkerActivity(string description)
        {
            // إدارة العمال تستخدم قوائم محلية وليس قاعدة بيانات
            MessageBox.Show("إدارة العمال تستخدم نظام تخزين محلي.\nيرجى التراجع يدوياً من قسم إدارة العمال.",
                "تعذر التراجع التلقائي", MessageBoxButton.OK, MessageBoxImage.Information);
            return false;
        }

        private bool UndoSalaryActivity(string description)
        {
            MessageBox.Show("التراجع عن صرف الرواتب يحتاج إجراءات محاسبية ومالية دقيقة.\nيرجى التراجع يدوياً من قسم إدارة العمال.",
                "تعذر التراجع التلقائي", MessageBoxButton.OK, MessageBoxImage.Information);
            return false;
        }

        /// <summary>
        /// إضافة نشاط اختبار للتراجع
        /// </summary>
        public void AddTestActivity()
        {
            try
            {
                // إضافة نشاط اختبار يمكن التراجع عنه
                ActivityService.LogActivity("تم إضافة شخص جديد: شخص اختبار", "Person");
                ForceRefreshActivities();

                MessageBox.Show("تم إضافة نشاط اختبار.\nيمكنك الآن اختبار التراجع عنه بالكليك اليمين عليه.",
                    "نشاط اختبار", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة نشاط الاختبار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث جميع الواجهات المفتوحة
        /// </summary>
        private void RefreshAllViews()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تحديث جميع الواجهات");

                // تحديث الصفحة الرئيسية
                RefreshDashboard();

                System.Diagnostics.Debug.WriteLine("تم تحديث جميع الواجهات");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الواجهات: {ex.Message}");
            }
        }

        /// <summary>
        /// إجبار تحديث الأنشطة الأخيرة
        /// </summary>
        public void ForceRefreshActivities()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("إجبار تحديث الأنشطة الأخيرة...");
                _lastActivitiesUpdate = DateTime.MinValue; // إعادة تعيين وقت آخر تحديث
                UpdateRecentActivities();
                System.Diagnostics.Debug.WriteLine("تم إجبار تحديث الأنشطة الأخيرة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إجبار تحديث الأنشطة: {ex.Message}");
            }
        }

        private void UpdateTime(object sender, EventArgs e)
        {
            if (TimeDisplay != null)
            {
                TimeDisplay.Text = DateTime.Now.ToString("HH:mm:ss");
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            _timeTimer?.Stop();

            // تأكيد الإغلاق
            var result = MessageBox.Show(
                "هل أنت متأكد من إغلاق التطبيق؟",
                "تأكيد الإغلاق",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.No)
            {
                e.Cancel = true;
            }
        }

        // دوال التنقل
        private void NavigateToPersons(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "👥 إدارة الأشخاص";
            var personsView = new Views.PersonsManagementView();
            MainContent.Content = personsView;
        }

        private void NavigateToWorkers(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "👷 إدارة العمال";
            var workersView = new Views.WorkersManagementView();
            MainContent.Content = workersView;
        }

        private void NavigateToDebts(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "💰 ديون الأشخاص";
            var debtsView = new Views.DebtsManagementView();
            MainContent.Content = debtsView;
        }

        private void NavigateToFactoryDebts(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "🏭 ديون المعمل";
            var factoryDebtsView = new Views.FactoryDebtsView();
            MainContent.Content = factoryDebtsView;
        }

        private void NavigateToOverdue(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "⚠️ الديون المتأخرة";
            var overdueView = new Views.OverdueDebtsView();
            MainContent.Content = overdueView;
        }

        private void NavigateToReports(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveCurrentViewSettings();
                ContentTitle.Text = "📊 التقارير والإحصائيات";
                MainContent.Content = new Views.ReportsView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقارير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NavigateToBackup(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveCurrentViewSettings();
                ContentTitle.Text = "💾 النسخ الاحتياطي والاستعادة";
                MainContent.Content = new Views.BackupView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل النسخ الاحتياطي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NavigateToReminders(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveCurrentViewSettings();
                ContentTitle.Text = "🔔 نظام التذكيرات والإشعارات";
                MainContent.Content = new Views.RemindersView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التذكيرات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NavigateToIronCalc(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "⚙️ حاسبة الحديد المتقدمة";
            var ironCalcView = new Views.IronCalculatorView();
            MainContent.Content = ironCalcView;
        }

        private void NavigateToCuttingCalc(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "🔪 حاسبة التقطيع المتقدمة";
            var cuttingCalcView = new Views.CuttingCalculatorView();
            MainContent.Content = cuttingCalcView;
        }

        private void NavigateToSearch(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "🔍 البحث والفلترة";
            var searchView = new Views.SearchView();
            MainContent.Content = searchView;
        }

        private void NavigateToSettings(object sender, RoutedEventArgs e)
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "⚙️ الإعدادات";
            var settingsView = new Views.SettingsView();
            MainContent.Content = settingsView;
        }

        // دوال الأدوات
        private void RefreshContent(object sender, RoutedEventArgs e)
        {
            // تحديث الإحصائيات
            UpdateDashboardStatistics();

            if (DataContext is MainViewModel viewModel)
            {
                viewModel.RefreshDataCommand?.Execute(null);
            }
            MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportContent(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "تصدير البيانات",
                    Filter = "ملفات Excel (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv|ملفات JSON (*.json)|*.json",
                    FileName = $"تصدير_البيانات_{DateTime.Now:yyyy-MM-dd}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // محاكاة عملية التصدير
                    var exportData = GenerateExportData();

                    // حفظ البيانات (محاكاة)
                    System.IO.File.WriteAllText(saveFileDialog.FileName, exportData);

                    MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{saveFileDialog.FileName}",
                        "تصدير ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateExportData()
        {
            var currentView = ContentTitle.Text;
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            return $@"تقرير تصدير البيانات
الواجهة الحالية: {currentView}
تاريخ التصدير: {timestamp}
عدد الأشخاص: 25
عدد الديون: 48
إجمالي المبالغ: 125,000,000 د.ع
الديون المتأخرة: 8
المبلغ المتأخر: 15,500,000 د.ع

تم إنشاء هذا التقرير بواسطة نظام إدارة الديون المتقدم";
        }

        // الإجراءات السريعة
        private void QuickAddPerson(object sender, RoutedEventArgs e)
        {
            NavigateToPersons(sender, e);
        }

        private void QuickAddDebt(object sender, RoutedEventArgs e)
        {
            NavigateToDebts(sender, e);
        }

        private void QuickReport(object sender, RoutedEventArgs e)
        {
            NavigateToReports(sender, e);
        }

        private void TestUndo(object sender, RoutedEventArgs e)
        {
            AddTestActivity();
        }

        // العودة للوحة المعلومات
        private void ReturnToDashboard()
        {
            SaveCurrentViewSettings();
            ContentTitle.Text = "🏠 الصفحة الرئيسية";
            MainContent.Content = DashboardContent;

            // تحديث الإحصائيات
            UpdateDashboardStatistics();

            if (DataContext is MainViewModel viewModel)
            {
                viewModel.RefreshDataCommand?.Execute(null);
            }
        }

        /// <summary>
        /// حفظ إعدادات الصفحة الحالية قبل التنقل
        /// </summary>
        private void SaveCurrentViewSettings()
        {
            try
            {
                if (MainContent.Content is System.Windows.Controls.UserControl userControl)
                {
                    SaveDataGridSettings(userControl);
                    System.Diagnostics.Debug.WriteLine("💾 تم حفظ إعدادات الصفحة الحالية");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ إعدادات الصفحة الحالية: {ex.Message}");
            }
        }

        private void ReturnToDashboard_Click(object sender, RoutedEventArgs e)
        {
            ReturnToDashboard();
        }

        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // حفظ إعدادات الأعمدة لجميع الجداول المفتوحة
                System.Diagnostics.Debug.WriteLine("💾 حفظ إعدادات الأعمدة عند إغلاق التطبيق...");

                // البحث عن جميع DataGrid في المحتوى الحالي وحفظ إعداداتها
                if (MainContent.Content is System.Windows.Controls.UserControl userControl)
                {
                    SaveDataGridSettings(userControl);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ إعدادات الأعمدة: {ex.Message}");
            }
        }

        private void SaveDataGridSettings(System.Windows.Controls.UserControl userControl)
        {
            try
            {
                // البحث عن جميع DataGrid في UserControl
                var dataGrids = FindVisualChildren<System.Windows.Controls.DataGrid>(userControl);

                foreach (var dataGrid in dataGrids)
                {
                    string gridName = dataGrid.Name;
                    if (!string.IsNullOrEmpty(gridName))
                    {
                        ColumnSettingsHelper.SaveColumnSettings(dataGrid, gridName);
                        System.Diagnostics.Debug.WriteLine($"تم حفظ إعدادات {gridName}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ إعدادات DataGrid: {ex.Message}");
            }
        }

        private static IEnumerable<T> FindVisualChildren<T>(System.Windows.DependencyObject depObj) where T : System.Windows.DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    var child = System.Windows.Media.VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        // دالة تحسين التمرير بالماوس مع السلاسة
        private void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (sender is ScrollViewer scrollViewer)
            {
                // تحسين سرعة التمرير مع السلاسة
                double scrollSpeed = 2.0; // سرعة معتدلة للسلاسة
                double targetOffset = scrollViewer.VerticalOffset - (e.Delta * scrollSpeed);

                // التأكد من أن القيمة ضمن الحدود المسموحة
                targetOffset = Math.Max(0, Math.Min(scrollViewer.ScrollableHeight, targetOffset));

                // تطبيق التمرير السلس
                SmoothScrollToOffset(scrollViewer, targetOffset);

                // منع التمرير الافتراضي
                e.Handled = true;
            }
        }

        // دالة التمرير السلس
        private void SmoothScrollToOffset(ScrollViewer scrollViewer, double targetOffset)
        {
            double startOffset = scrollViewer.VerticalOffset;
            double distance = targetOffset - startOffset;

            if (Math.Abs(distance) < 1) return; // إذا كانت المسافة صغيرة جداً، لا حاجة للحركة

            var timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromMilliseconds(16); // 60 FPS

            DateTime startTime = DateTime.Now;
            double duration = 150; // مدة الحركة بالميلي ثانية

            timer.Tick += (s, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                // استخدام دالة Ease Out للسلاسة
                double easedProgress = 1 - Math.Pow(1 - progress, 3);

                double currentOffset = startOffset + (distance * easedProgress);
                scrollViewer.ScrollToVerticalOffset(currentOffset);

                if (progress >= 1.0)
                {
                    timer.Stop();
                }
            };

            timer.Start();
        }
    }
}
