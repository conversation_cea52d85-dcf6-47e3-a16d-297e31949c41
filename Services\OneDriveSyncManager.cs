using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;

namespace DebtManagementApp.Services
{
    /// <summary>
    /// مدير المزامنة التلقائية مع OneDrive
    /// </summary>
    public class OneDriveSyncManager : IDisposable
    {
        private readonly OneDriveService _oneDriveService;
        private readonly OneDriveSettings _settings;
        private readonly DispatcherTimer _syncTimer;
        private readonly FileSystemWatcher _fileWatcher;
        private readonly string _databasePath;
        private bool _isDisposed = false;
        private bool _isSyncing = false;

        public event Action<string>? StatusChanged;
        public event Action<string, bool>? SyncCompleted;
        public event Action<string>? ErrorOccurred;
        public event Action<DateTime, DateTime>? NewerVersionDetected;
        public event Action<int>? ProgressChanged;

        /// <summary>
        /// خدمة OneDrive للوصول إلى معلومات المستخدم
        /// </summary>
        public OneDriveService OneDriveService => _oneDriveService;

        public OneDriveSyncManager(OneDriveSettings settings, string databasePath)
        {
            _settings = settings;
            _databasePath = databasePath;
            _oneDriveService = new OneDriveService(settings);

            // ربط الأحداث
            _oneDriveService.StatusChanged += OnStatusChanged;
            _oneDriveService.SyncCompleted += OnSyncCompleted;
            _oneDriveService.ErrorOccurred += OnErrorOccurred;
            _oneDriveService.ProgressChanged += OnProgressChanged;

            // إعداد مؤقت المزامنة التلقائية
            _syncTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(_settings.SyncIntervalMinutes)
            };
            _syncTimer.Tick += async (s, e) => await PerformAutoSyncAsync();

            // إعداد مراقب الملفات
            var directory = Path.GetDirectoryName(_databasePath);
            if (!string.IsNullOrEmpty(directory) && Directory.Exists(directory))
            {
                _fileWatcher = new FileSystemWatcher(directory)
                {
                    Filter = Path.GetFileName(_databasePath),
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                    EnableRaisingEvents = false
                };
                _fileWatcher.Changed += OnDatabaseFileChanged;
            }
            else
            {
                _fileWatcher = new FileSystemWatcher();
            }

            // تحديث الإعدادات عند تغييرها
            _settings.PropertyChanged += OnSettingsChanged;
        }

        /// <summary>
        /// بدء المزامنة التلقائية
        /// </summary>
        public async Task<bool> StartAsync()
        {
            try
            {
                if (!_settings.IsEnabled)
                {
                    StatusChanged?.Invoke("مزامنة OneDrive معطلة");
                    return false;
                }

                StatusChanged?.Invoke("بدء خدمة مزامنة OneDrive...");

                // تسجيل الدخول
                var signInSuccess = await _oneDriveService.SignInAsync();
                if (!signInSuccess)
                {
                    ErrorOccurred?.Invoke("فشل في تسجيل الدخول إلى OneDrive");
                    return false;
                }

                // التحقق من وجود نسخة أحدث
                await CheckForNewerVersionAsync();

                // بدء المزامنة التلقائية
                if (_settings.AutoSync)
                {
                    _syncTimer.Start();
                    _fileWatcher.EnableRaisingEvents = true;
                    StatusChanged?.Invoke("تم بدء المزامنة التلقائية");
                }

                StatusChanged?.Invoke("خدمة OneDrive جاهزة");
                return true;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في بدء خدمة OneDrive: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إيقاف المزامنة التلقائية
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                _syncTimer.Stop();
                _fileWatcher.EnableRaisingEvents = false;

                await _oneDriveService.SignOutAsync();
                StatusChanged?.Invoke("تم إيقاف خدمة OneDrive");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في إيقاف خدمة OneDrive: {ex.Message}");
            }
        }

        /// <summary>
        /// مزامنة يدوية
        /// </summary>
        public async Task<bool> ManualSyncAsync()
        {
            if (_isSyncing)
            {
                StatusChanged?.Invoke("المزامنة قيد التنفيذ بالفعل");
                return false;
            }

            return await PerformSyncAsync(true);
        }

        /// <summary>
        /// تحميل النسخة الأحدث من OneDrive
        /// </summary>
        public async Task<bool> DownloadLatestAsync()
        {
            try
            {
                if (_isSyncing)
                {
                    StatusChanged?.Invoke("المزامنة قيد التنفيذ بالفعل");
                    return false;
                }

                _isSyncing = true;
                StatusChanged?.Invoke("جاري تحميل النسخة الأحدث...");

                var success = await _oneDriveService.DownloadDatabaseAsync(_databasePath);
                
                if (success && _settings.ShowSyncNotifications)
                {
                    SyncCompleted?.Invoke("تم تحميل النسخة الأحدث من OneDrive", true);
                }

                return success;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تحميل النسخة الأحدث: {ex.Message}");
                return false;
            }
            finally
            {
                _isSyncing = false;
            }
        }

        /// <summary>
        /// التحقق من وجود نسخة أحدث
        /// </summary>
        public async Task CheckForNewerVersionAsync()
        {
            try
            {
                var (hasNewer, remoteModified, localModified) = await _oneDriveService.CheckForNewerVersionAsync(_databasePath);
                
                if (hasNewer)
                {
                    StatusChanged?.Invoke("تم العثور على نسخة أحدث في OneDrive");
                    NewerVersionDetected?.Invoke(remoteModified, localModified);
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في التحقق من النسخة: {ex.Message}");
            }
        }

        /// <summary>
        /// تنفيذ المزامنة التلقائية
        /// </summary>
        private async Task PerformAutoSyncAsync()
        {
            if (!_settings.IsEnabled || !_settings.AutoSync)
                return;

            await PerformSyncAsync(false);
        }

        /// <summary>
        /// تنفيذ المزامنة
        /// </summary>
        private async Task<bool> PerformSyncAsync(bool isManual)
        {
            if (_isSyncing)
                return false;

            try
            {
                _isSyncing = true;

                if (!File.Exists(_databasePath))
                {
                    ErrorOccurred?.Invoke("ملف قاعدة البيانات غير موجود");
                    return false;
                }

                // التحقق من النسخة الأحدث أولاً
                var (hasNewer, remoteModified, localModified) = await _oneDriveService.CheckForNewerVersionAsync(_databasePath);
                
                if (hasNewer && !isManual)
                {
                    // إذا كانت هناك نسخة أحدث وهذه مزامنة تلقائية، لا نرفع
                    StatusChanged?.Invoke("تم تخطي الرفع - توجد نسخة أحدث في OneDrive");
                    return false;
                }

                // رفع النسخة المحلية
                var success = await _oneDriveService.UploadDatabaseAsync(_databasePath);
                
                if (success && _settings.ShowSyncNotifications && isManual)
                {
                    SyncCompleted?.Invoke("تم رفع قاعدة البيانات إلى OneDrive", true);
                }

                return success;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في المزامنة: {ex.Message}");
                return false;
            }
            finally
            {
                _isSyncing = false;
            }
        }

        /// <summary>
        /// معالج تغيير ملف قاعدة البيانات
        /// </summary>
        private async void OnDatabaseFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // انتظار قصير للتأكد من انتهاء الكتابة
                await Task.Delay(2000);
                
                if (_settings.IsEnabled && _settings.AutoSync)
                {
                    await PerformSyncAsync(false);
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في مزامنة التغييرات: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج تغيير الإعدادات
        /// </summary>
        private void OnSettingsChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(OneDriveSettings.SyncIntervalMinutes))
            {
                _syncTimer.Interval = TimeSpan.FromMinutes(_settings.SyncIntervalMinutes);
            }
            else if (e.PropertyName == nameof(OneDriveSettings.AutoSync))
            {
                if (_settings.AutoSync && _settings.IsEnabled)
                {
                    _syncTimer.Start();
                    _fileWatcher.EnableRaisingEvents = true;
                }
                else
                {
                    _syncTimer.Stop();
                    _fileWatcher.EnableRaisingEvents = false;
                }
            }
        }

        private void OnStatusChanged(string status) => StatusChanged?.Invoke(status);
        private void OnSyncCompleted(string message, bool success) => SyncCompleted?.Invoke(message, success);
        private void OnErrorOccurred(string error) => ErrorOccurred?.Invoke(error);
        private void OnProgressChanged(int progress) => ProgressChanged?.Invoke(progress);

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _syncTimer?.Stop();
                _fileWatcher?.Dispose();
                _oneDriveService?.Dispose();
                _isDisposed = true;
            }
        }
    }
}
