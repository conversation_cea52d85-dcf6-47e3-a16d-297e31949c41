<UserControl x:Class="DebtManagementApp.Views.FactoryDebtsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="#F8F9FA">

    <UserControl.Resources>
        <!-- ستايل البطاقات -->
        <Style x:Key="StatCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ستايل الأزرار -->
        <Style x:Key="ActionButton" TargetType="Button">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="8,0"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان الرئيسي -->
        <Border Grid.Row="0" Background="#007BFF" Padding="20,16" Margin="0,0,0,16">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🏭" FontSize="28" Margin="0,0,12,0"/>
                <TextBlock Text="ديون المعمل" FontSize="24" FontWeight="Bold" 
                           Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- البطاقات الإحصائية -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الديون غير المسددة -->
            <Border Grid.Column="0" Style="{StaticResource StatCard}">
                <StackPanel>
                    <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="TotalDebtsText" Text="0 دينار" FontSize="18" FontWeight="Bold"
                               Foreground="#DC3545" HorizontalAlignment="Center"/>
                    <TextBlock Text="الديون غير المسددة" FontSize="12" Foreground="#6C757D"
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                </StackPanel>
            </Border>

            <!-- الديون المسددة -->
            <Border Grid.Column="1" Style="{StaticResource StatCard}">
                <StackPanel>
                    <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="PaidDebtsText" Text="0 دينار" FontSize="18" FontWeight="Bold"
                               Foreground="#28A745" HorizontalAlignment="Center"/>
                    <TextBlock Text="الديون المسددة" FontSize="12" Foreground="#6C757D"
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                </StackPanel>
            </Border>

            <!-- عدد الفواتير -->
            <Border Grid.Column="2" Style="{StaticResource StatCard}">
                <StackPanel>
                    <TextBlock Text="📄" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="TotalInvoicesText" Text="0" FontSize="18" FontWeight="Bold"
                               Foreground="#007BFF" HorizontalAlignment="Center"/>
                    <TextBlock Text="عدد الفواتير" FontSize="12" Foreground="#6C757D"
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                </StackPanel>
            </Border>

            <!-- عدد الأشخاص -->
            <Border Grid.Column="3" Style="{StaticResource StatCard}">
                <StackPanel>
                    <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="TotalPersonsText" Text="0" FontSize="18" FontWeight="Bold"
                               Foreground="#6F42C1" HorizontalAlignment="Center"/>
                    <TextBlock Text="عدد الأشخاص" FontSize="12" Foreground="#6C757D"
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                </StackPanel>
            </Border>

            <!-- الفواتير غير المسددة -->
            <Border Grid.Column="4" Style="{StaticResource StatCard}">
                <StackPanel>
                    <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="UnpaidInvoicesText" Text="0" FontSize="18" FontWeight="Bold"
                               Foreground="#FD7E14" HorizontalAlignment="Center"/>
                    <TextBlock Text="فواتير غير مسددة" FontSize="12" Foreground="#6C757D"
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="2" x:Name="MainContentGrid">
            <!-- العرض الافتراضي -->
            <Grid x:Name="DefaultView">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="3*"/>
                </Grid.ColumnDefinitions>

                <!-- قائمة الأشخاص -->
                <Border Grid.Column="0" Background="White" BorderBrush="#E9ECEF" BorderThickness="1"
                        CornerRadius="8" Padding="16" Margin="0,0,8,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القائمة -->
                        <TextBlock Grid.Row="0" Text="👥 قائمة الأشخاص" FontSize="16" FontWeight="Bold"
                                   Foreground="#495057" Margin="0,0,0,16"/>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,16">
                            <Button x:Name="AddDebtButton" Content="➕ إضافة دين"
                                    Background="#28A745" Foreground="White"
                                    Style="{StaticResource ActionButton}"
                                    Click="AddDebtButton_Click"/>

                            <Button x:Name="ExportButton" Content="📊 تصدير"
                                    Background="#17A2B8" Foreground="White"
                                    Style="{StaticResource ActionButton}"
                                    Click="ExportButton_Click"/>
                        </StackPanel>

                        <!-- جدول الأشخاص -->
                        <DataGrid Grid.Row="2" x:Name="PersonsDataGrid"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="False"
                                  CanUserResizeRows="False"
                                  SelectionMode="Single"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  Background="White"
                                  BorderThickness="0"
                                  FontSize="13"
                                  RowHeight="40"
                                  MouseDoubleClick="PersonsDataGrid_MouseDoubleClick">

                            <DataGrid.ContextMenu>
                                <ContextMenu x:Name="PersonsContextMenu">
                                    <MenuItem Header="📄 عرض الفواتير" Click="ViewInvoicesMenuItem_Click"/>
                                    <MenuItem Header="➕ إضافة دين جديد" Click="AddDebtMenuItem_Click"/>
                                    <Separator/>
                                    <MenuItem Header="📊 تصدير بيانات الشخص" Click="ExportPersonMenuItem_Click"/>
                                </ContextMenu>
                            </DataGrid.ContextMenu>

                            <DataGrid.Columns>
                                <!-- اسم الشخص -->
                                <DataGridTextColumn Header="👤 الاسم" Binding="{Binding PersonName}" Width="*">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#495057"/>
                                            <Setter Property="Padding" Value="8,4"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- إجمالي الدين -->
                                <DataGridTextColumn Header="💰 إجمالي الدين" Binding="{Binding TotalDebtFormatted}" Width="120">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="Foreground" Value="#DC3545"/>
                                            <Setter Property="Padding" Value="8,4"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>


                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>

                <!-- منطقة المحتوى الافتراضي -->
                <Border Grid.Column="1" Background="White" BorderBrush="#E9ECEF" BorderThickness="1"
                        CornerRadius="8" Padding="16" Margin="8,0,0,0">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="🏭" FontSize="64" HorizontalAlignment="Center"
                                   Foreground="#E9ECEF" Margin="0,0,0,16"/>
                        <TextBlock Text="اختر شخصاً لعرض فواتيره" FontSize="18"
                                   Foreground="#6C757D" HorizontalAlignment="Center"/>
                        <TextBlock Text="أو اضغط 'إضافة دين' لإضافة فاتورة جديدة" FontSize="14"
                                   Foreground="#ADB5BD" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- منطقة المحتوى الديناميكي (كامل الشاشة) -->
            <ContentControl x:Name="DynamicContentArea" Visibility="Collapsed"/>
        </Grid>
    </Grid>
</UserControl>
