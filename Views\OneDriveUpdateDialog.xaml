<Window x:Class="DebtManagementApp.Views.OneDriveUpdateDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تحديث قاعدة البيانات - OneDrive" 
        Height="400" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{StaticResource AppBackground}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والأيقونة -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,30">
            <Border Background="{StaticResource WarningBrush}" 
                    CornerRadius="25" Width="50" Height="50" 
                    Margin="0,0,20,0">
                <TextBlock Text="⚠️" FontSize="24" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"/>
            </Border>
            <StackPanel VerticalAlignment="Center">
                <TextBlock Text="تم العثور على نسخة أحدث" 
                           Style="{StaticResource HeadingMedium}"
                           Foreground="{StaticResource TextPrimary}"/>
                <TextBlock Text="توجد نسخة أحدث من قاعدة البيانات في OneDrive" 
                           Style="{StaticResource BodyMedium}"
                           Foreground="{StaticResource TextSecondary}"
                           Margin="0,5,0,0"/>
            </StackPanel>
        </StackPanel>

        <!-- تفاصيل النسخ -->
        <Border Grid.Row="1" Background="{StaticResource BackgroundSecondary}" 
                CornerRadius="10" Padding="20" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="تفاصيل النسخ:" 
                           Style="{StaticResource BodyLarge}"
                           FontWeight="Bold"
                           Margin="0,0,0,15"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- النسخة المحلية -->
                    <TextBlock Grid.Row="0" Grid.Column="0" 
                               Text="النسخة المحلية:" 
                               Style="{StaticResource BodyMedium}"
                               Margin="0,0,15,10"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" 
                               x:Name="LocalVersionText"
                               Style="{StaticResource BodyMedium}"
                               Foreground="{StaticResource TextSecondary}"
                               Margin="0,0,0,10"/>

                    <!-- النسخة في OneDrive -->
                    <TextBlock Grid.Row="1" Grid.Column="0" 
                               Text="نسخة OneDrive:" 
                               Style="{StaticResource BodyMedium}"
                               Margin="0,0,15,10"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" 
                               x:Name="RemoteVersionText"
                               Style="{StaticResource BodyMedium}"
                               Foreground="{StaticResource SuccessBrush}"
                               FontWeight="Bold"
                               Margin="0,0,0,10"/>

                    <!-- الفرق الزمني -->
                    <TextBlock Grid.Row="2" Grid.Column="0" 
                               Text="الفرق:" 
                               Style="{StaticResource BodyMedium}"
                               Margin="0,0,15,0"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" 
                               x:Name="TimeDifferenceText"
                               Style="{StaticResource BodyMedium}"
                               Foreground="{StaticResource WarningBrush}"
                               FontWeight="Bold"/>
                </Grid>

                <Separator Margin="0,20,0,15" Background="{StaticResource BorderBrush}"/>

                <TextBlock Text="ماذا تريد أن تفعل؟" 
                           Style="{StaticResource BodyMedium}"
                           FontWeight="Bold"
                           Margin="0,0,0,10"/>

                <StackPanel>
                    <RadioButton x:Name="DownloadRadio" 
                                 Content="تحميل النسخة الأحدث من OneDrive (موصى به)"
                                 Style="{StaticResource ModernRadioButton}"
                                 IsChecked="True"
                                 Margin="0,0,0,10"/>
                    <RadioButton x:Name="IgnoreRadio" 
                                 Content="تجاهل والاستمرار بالنسخة المحلية"
                                 Style="{StaticResource ModernRadioButton}"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Left" Margin="0,10,0,0">
            <Button x:Name="ConfirmButton" 
                    Content="تأكيد" 
                    Style="{StaticResource PrimaryButton}"
                    Width="120" Height="40"
                    Margin="0,0,15,0"
                    Click="ConfirmButton_Click"/>
            <Button x:Name="CancelButton" 
                    Content="إلغاء" 
                    Style="{StaticResource SecondaryButton}"
                    Width="120" Height="40"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
