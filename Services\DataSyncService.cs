using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;

namespace DebtManagementApp.Services
{
    public class DataSyncService
    {
        private readonly NetworkService _networkService;
        private bool _isSyncing;

        public event Action<string>? OnSyncLog;
        public event Action? OnDataUpdated;

        public DataSyncService()
        {
            _networkService = new NetworkService();

            _networkService.OnMessageReceived += HandleReceivedMessage;
            _networkService.OnLog += (message) => OnSyncLog?.Invoke(message);
        }

        public NetworkService NetworkService => _networkService;

        // معالجة الرسائل المستقبلة
        private async void HandleReceivedMessage(string message)
        {
            if (_isSyncing) return;

            try
            {
                var syncData = JsonSerializer.Deserialize<SyncMessage>(message);
                if (syncData == null) return;

                OnSyncLog?.Invoke($"📥 استقبال: {syncData.Action} - {syncData.TableName}");

                _isSyncing = true;

                switch (syncData.Action.ToUpper())
                {
                    case "INSERT":
                        await HandleInsert(syncData);
                        break;
                    case "UPDATE":
                        await HandleUpdate(syncData);
                        break;
                    case "DELETE":
                        await HandleDelete(syncData);
                        break;
                    case "SYNC_REQUEST":
                        await HandleSyncRequest();
                        break;
                    case "SYNC_RESPONSE":
                        await HandleSyncResponse(syncData);
                        break;
                }

                OnDataUpdated?.Invoke();
            }
            catch (Exception ex)
            {
                OnSyncLog?.Invoke($"❌ خطأ في معالجة الرسالة: {ex.Message}");
            }
            finally
            {
                _isSyncing = false;
            }
        }

        // إرسال تحديث
        public async Task SendUpdate(string action, string tableName, object data, int? id = null)
        {
            if (_isSyncing || !_networkService.IsConnected) return;

            try
            {
                var syncMessage = new SyncMessage
                {
                    Action = action,
                    TableName = tableName,
                    Data = JsonSerializer.Serialize(data),
                    Id = id,
                    Timestamp = DateTime.Now
                };

                string message = JsonSerializer.Serialize(syncMessage);
                await _networkService.SendMessage(message);
                
                OnSyncLog?.Invoke($"📤 إرسال: {action} - {tableName}");
            }
            catch (Exception ex)
            {
                OnSyncLog?.Invoke($"❌ خطأ في إرسال التحديث: {ex.Message}");
            }
        }

        // معالجة الإدراج
        private async Task HandleInsert(SyncMessage syncData)
        {
            switch (syncData.TableName.ToLower())
            {
                case "persons":
                    var person = JsonSerializer.Deserialize<Person>(syncData.Data);
                    if (person != null)
                    {
                        DatabaseHelper.AddPerson(person);
                    }
                    break;

                case "debts":
                    var debt = JsonSerializer.Deserialize<Debt>(syncData.Data);
                    if (debt != null)
                    {
                        DatabaseHelper.AddDebt(debt);
                    }
                    break;

                case "workers":
                    var worker = JsonSerializer.Deserialize<Worker>(syncData.Data);
                    if (worker != null)
                    {
                        DatabaseHelper.AddWorker(worker);
                    }
                    break;

                case "salaries":
                    var salary = JsonSerializer.Deserialize<SalaryPayment>(syncData.Data);
                    if (salary != null)
                    {
                        DatabaseHelper.AddSalaryPayment(salary);
                    }
                    break;
            }
        }

        // معالجة التحديث
        private async Task HandleUpdate(SyncMessage syncData)
        {
            switch (syncData.TableName.ToLower())
            {
                case "persons":
                    var person = JsonSerializer.Deserialize<Person>(syncData.Data);
                    if (person != null)
                    {
                        DatabaseHelper.UpdatePerson(person);
                    }
                    break;

                case "debts":
                    var debt = JsonSerializer.Deserialize<Debt>(syncData.Data);
                    if (debt != null)
                    {
                        DatabaseHelper.UpdateDebt(debt);
                    }
                    break;

                case "workers":
                    var worker = JsonSerializer.Deserialize<Worker>(syncData.Data);
                    if (worker != null)
                    {
                        DatabaseHelper.UpdateWorker(worker);
                    }
                    break;
            }
        }

        // معالجة الحذف
        private async Task HandleDelete(SyncMessage syncData)
        {
            if (!syncData.Id.HasValue) return;

            switch (syncData.TableName.ToLower())
            {
                case "persons":
                    DatabaseHelper.DeletePerson(syncData.Id.Value);
                    break;

                case "debts":
                    DatabaseHelper.DeleteDebt(syncData.Id.Value);
                    break;

                case "workers":
                    DatabaseHelper.DeleteWorker(syncData.Id.Value);
                    break;
            }
        }

        // طلب مزامنة كاملة
        public async Task RequestFullSync()
        {
            var syncMessage = new SyncMessage
            {
                Action = "SYNC_REQUEST",
                TableName = "ALL",
                Timestamp = DateTime.Now
            };

            string message = JsonSerializer.Serialize(syncMessage);
            await _networkService.SendMessage(message);
            
            OnSyncLog?.Invoke("📋 طلب مزامنة كاملة");
        }

        // معالجة طلب المزامنة
        private async Task HandleSyncRequest()
        {
            try
            {
                var allData = new
                {
                    Persons = DatabaseHelper.GetAllPersons(),
                    Debts = DatabaseHelper.GetAllDebts(),
                    Workers = DatabaseHelper.GetAllWorkers(),
                    Salaries = DatabaseHelper.GetAllSalaryPayments()
                };

                var syncMessage = new SyncMessage
                {
                    Action = "SYNC_RESPONSE",
                    TableName = "ALL",
                    Data = JsonSerializer.Serialize(allData),
                    Timestamp = DateTime.Now
                };

                string message = JsonSerializer.Serialize(syncMessage);
                await _networkService.SendMessage(message);
                
                OnSyncLog?.Invoke("📋 إرسال بيانات المزامنة الكاملة");
            }
            catch (Exception ex)
            {
                OnSyncLog?.Invoke($"❌ خطأ في إرسال بيانات المزامنة: {ex.Message}");
            }
        }

        // معالجة استجابة المزامنة
        private async Task HandleSyncResponse(SyncMessage syncData)
        {
            try
            {
                var allData = JsonSerializer.Deserialize<AllDataModel>(syncData.Data);
                if (allData == null) return;

                // مزامنة الأشخاص
                foreach (var person in allData.Persons)
                {
                    var existing = DatabaseHelper.GetPersonById(person.Id);
                    if (existing == null)
                    {
                        DatabaseHelper.AddPerson(person);
                    }
                    else if (existing.LastUpdated < person.LastUpdated)
                    {
                        DatabaseHelper.UpdatePerson(person);
                    }
                }

                // مزامنة الديون
                foreach (var debt in allData.Debts)
                {
                    var existing = DatabaseHelper.GetDebtById(debt.Id);
                    if (existing == null)
                    {
                        DatabaseHelper.AddDebt(debt);
                    }
                    else if (existing.LastUpdated < debt.LastUpdated)
                    {
                        DatabaseHelper.UpdateDebt(debt);
                    }
                }

                // مزامنة العمال
                foreach (var worker in allData.Workers)
                {
                    var existing = DatabaseHelper.GetWorkerById(worker.Id);
                    if (existing == null)
                    {
                        DatabaseHelper.AddWorker(worker);
                    }
                    else if (existing.LastUpdated < worker.LastUpdated)
                    {
                        DatabaseHelper.UpdateWorker(worker);
                    }
                }

                OnSyncLog?.Invoke("✅ تمت المزامنة الكاملة بنجاح");
            }
            catch (Exception ex)
            {
                OnSyncLog?.Invoke($"❌ خطأ في معالجة بيانات المزامنة: {ex.Message}");
            }
        }
    }

    // نموذج رسالة المزامنة
    public class SyncMessage
    {
        public string Action { get; set; } = "";
        public string TableName { get; set; } = "";
        public string Data { get; set; } = "";
        public int? Id { get; set; }
        public DateTime Timestamp { get; set; }
    }

    // نموذج جميع البيانات
    public class AllDataModel
    {
        public List<Person> Persons { get; set; } = new();
        public List<Debt> Debts { get; set; } = new();
        public List<Worker> Workers { get; set; } = new();
        public List<SalaryPayment> Salaries { get; set; } = new();
    }
}
