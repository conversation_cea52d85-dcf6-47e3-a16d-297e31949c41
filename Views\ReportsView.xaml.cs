using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;

namespace DebtManagementApp.Views
{
    public partial class ReportsView : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<Person> _allPersons;
        private ObservableCollection<Debt> _allDebts;

        public ReportsView()
        {
            InitializeComponent();
            DataContext = this;

            // تهيئة المجموعات
            _allPersons = new ObservableCollection<Person>();
            _allDebts = new ObservableCollection<Debt>();

            LoadData();
            UpdateQuickStats();

            // تسجيل أحداث حفظ إعدادات الأعمدة
            Loaded += (s, e) => ColumnSettingsHelper.RegisterColumnEvents(ReportDataGrid, "ReportsGrid");
        }

        private void LoadData()
        {
            try
            {
                // تحميل البيانات الفعلية من قاعدة البيانات
                _allPersons.Clear();
                _allDebts.Clear();

                System.Diagnostics.Debug.WriteLine("بدء تحميل البيانات في صفحة التقارير...");

                // تحميل الأشخاص من قاعدة البيانات
                try
                {
                    var personsFromDb = DatabaseHelper.GetAllPersons();
                    System.Diagnostics.Debug.WriteLine($"تم تحميل {personsFromDb.Count} شخص من قاعدة البيانات");

                    foreach (var person in personsFromDb)
                    {
                        // تحويل Location إلى Address إذا لزم الأمر
                        if (string.IsNullOrEmpty(person.Address) && !string.IsNullOrEmpty(person.Location))
                        {
                            person.Address = person.Location;
                        }
                        if (string.IsNullOrEmpty(person.Notes) && !string.IsNullOrEmpty(person.AdditionalInfo))
                        {
                            person.Notes = person.AdditionalInfo;
                        }
                        _allPersons.Add(person);
                    }

                    // إذا لم توجد أشخاص في قاعدة البيانات، أضف البيانات التجريبية
                    if (personsFromDb.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("قاعدة البيانات فارغة، سيتم إضافة البيانات التجريبية للأشخاص");
                        LoadSamplePersons();
                    }
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"فشل تحميل الأشخاص من قاعدة البيانات: {dbEx.Message}");
                    LoadSamplePersons();
                }

                // تحميل الديون من قاعدة البيانات
                try
                {
                    var debtsFromDb = DatabaseHelper.GetAllDebts();
                    System.Diagnostics.Debug.WriteLine($"تم تحميل {debtsFromDb.Count} دين من قاعدة البيانات");

                    foreach (var debt in debtsFromDb)
                    {
                        _allDebts.Add(debt);
                    }

                    // إذا لم توجد ديون في قاعدة البيانات، أضف البيانات التجريبية
                    if (debtsFromDb.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("قاعدة البيانات فارغة، سيتم إضافة البيانات التجريبية للديون");
                        LoadSampleDebts();
                    }
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"فشل تحميل الديون من قاعدة البيانات: {dbEx.Message}");
                    LoadSampleDebts();
                }

                System.Diagnostics.Debug.WriteLine($"انتهى تحميل البيانات: {_allPersons.Count} أشخاص، {_allDebts.Count} ديون");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ عام في تحميل البيانات: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSamplePersons()
        {
            // بيانات تجريبية للأشخاص
            var samplePersons = new List<Person>
            {
                new Person { Id = 1, Name = "أحمد محمد علي", Phone = "07701234567", Address = "بغداد - الكرادة", Notes = "مقاول حديد تسليح", DebtsCount = 3, TotalAmount = 2180000 },
                new Person { Id = 2, Name = "فاطمة عبدالله", Phone = "07809876543", Address = "البصرة - الجمعيات", Notes = "أعمال لحام", DebtsCount = 2, TotalAmount = 540000 },
                new Person { Id = 3, Name = "محمد سعد الغامدي", Phone = "07511122334", Address = "أربيل - عنكاوا", Notes = "هياكل حديدية", DebtsCount = 2, TotalAmount = 1040000 },
                new Person { Id = 4, Name = "نورا خالد", Phone = "07667788990", Address = "الموصل - الجامعة", Notes = "عميل جديد", DebtsCount = 0, TotalAmount = 0 },
                new Person { Id = 5, Name = "عبدالرحمن الشهري", Phone = "07844556677", Address = "النجف - المركز", Notes = "تاجر حديد", DebtsCount = 4, TotalAmount = 3250000 }
            };

            foreach (var person in samplePersons)
            {
                _allPersons.Add(person);
            }
        }

        private void LoadSampleDebts()
        {
            // بيانات تجريبية للديون
            var sampleDebts = new List<Debt>
            {
                new Debt { Id = 1, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 750000, Description = "حديد تسليح 12 ملم", Date = DateTime.Now.AddDays(-10), DueDate = DateTime.Now.AddDays(-5), IsSettled = false },
                new Debt { Id = 2, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 890000, Description = "حديد تسليح 16 ملم", Date = DateTime.Now.AddDays(-15), DueDate = DateTime.Now.AddDays(10), IsSettled = true, PaymentDate = DateTime.Now.AddDays(-2) },
                new Debt { Id = 3, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 540000, Description = "أسياخ حديد 8 ملم", Date = DateTime.Now.AddDays(-5), DueDate = DateTime.Now.AddDays(15), IsSettled = false },
                new Debt { Id = 4, PersonId = 2, PersonName = "فاطمة عبدالله", Amount = 320000, Description = "لحام هياكل", Date = DateTime.Now.AddDays(-20), DueDate = DateTime.Now.AddDays(-10), IsSettled = false },
                new Debt { Id = 5, PersonId = 2, PersonName = "فاطمة عبدالله", Amount = 220000, Description = "أعمال تقطيع", Date = DateTime.Now.AddDays(-8), DueDate = DateTime.Now.AddDays(5), IsSettled = true, PaymentDate = DateTime.Now.AddDays(-1) },
                new Debt { Id = 6, PersonId = 3, PersonName = "محمد سعد الغامدي", Amount = 650000, Description = "هيكل حديدي مخصص", Date = DateTime.Now.AddDays(-12), DueDate = DateTime.Now.AddDays(-3), IsSettled = false },
                new Debt { Id = 7, PersonId = 3, PersonName = "محمد سعد الغامدي", Amount = 390000, Description = "حديد زاوية", Date = DateTime.Now.AddDays(-3), DueDate = DateTime.Now.AddDays(20), IsSettled = false },
                new Debt { Id = 8, PersonId = 5, PersonName = "عبدالرحمن الشهري", Amount = 1200000, Description = "حديد تسليح متنوع", Date = DateTime.Now.AddDays(-25), DueDate = DateTime.Now.AddDays(-15), IsSettled = false },
                new Debt { Id = 9, PersonId = 5, PersonName = "عبدالرحمن الشهري", Amount = 850000, Description = "أسلاك حديد", Date = DateTime.Now.AddDays(-12), DueDate = DateTime.Now.AddDays(8), IsSettled = true, PaymentDate = DateTime.Now.AddDays(-3) },
                new Debt { Id = 10, PersonId = 5, PersonName = "عبدالرحمن الشهري", Amount = 600000, Description = "حديد مجلفن", Date = DateTime.Now.AddDays(-7), DueDate = DateTime.Now.AddDays(12), IsSettled = false },
                new Debt { Id = 11, PersonId = 5, PersonName = "عبدالرحمن الشهري", Amount = 600000, Description = "قضبان حديد", Date = DateTime.Now.AddDays(-18), DueDate = DateTime.Now.AddDays(-8), IsSettled = false }
            };

            foreach (var debt in sampleDebts)
            {
                _allDebts.Add(debt);
            }
        }

        private void UpdateQuickStats()
        {
            try
            {
                TotalPersonsCount.Text = _allPersons.Count.ToString();
                TotalDebtsCount.Text = _allDebts.Count.ToString();
                TotalAmountValue.Text = $"{_allDebts.Sum(d => d.Amount):N0} دينار";
                OverdueDebtsCount.Text = _allDebts.Count(d => !d.IsSettled && d.DueDate < DateTime.Now).ToString();

                System.Diagnostics.Debug.WriteLine($"إحصائيات سريعة: {_allPersons.Count} أشخاص، {_allDebts.Count} ديون، {_allDebts.Sum(d => d.Amount):N0} دينار");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
                TotalPersonsCount.Text = "0";
                TotalDebtsCount.Text = "0";
                TotalAmountValue.Text = "0 دينار";
                OverdueDebtsCount.Text = "0";
            }
        }



        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                RefreshData();
                MessageBox.Show("تم إعادة تحميل البيانات بنجاح!", "تم التحديث",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GenerateComprehensiveReport_Click(object sender, RoutedEventArgs e)
        {
            ReportTitle.Text = "📈 التقرير الشامل - جميع الديون والأشخاص";
            EmptyMessage.Visibility = Visibility.Collapsed;
            ReportDataGrid.Visibility = Visibility.Visible;

            // إعداد أعمدة التقرير الشامل
            ReportDataGrid.Columns.Clear();
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "اسم الشخص", Binding = new System.Windows.Data.Binding("PersonName"), Width = 200 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "وصف الدين", Binding = new System.Windows.Data.Binding("Description"), Width = 250 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "المبلغ", Binding = new System.Windows.Data.Binding("Amount") { StringFormat = "{0:N0} دينار" }, Width = 150 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الاستحقاق", Binding = new System.Windows.Data.Binding("DueDate") { StringFormat = "yyyy/MM/dd" }, Width = 120 });
            ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("StatusText"), Width = 100 });

            // إعداد البيانات
            var reportData = _allDebts.Select(d => new
            {
                PersonName = d.PersonName,
                Description = d.Description,
                Amount = d.Amount,
                DueDate = d.DueDate,
                StatusText = d.IsSettled ? "مدفوع" : (d.DueDate < DateTime.Now ? "متأخر" : "مستحق")
            }).ToList();

            ReportDataGrid.ItemsSource = reportData;
        }

        private void GenerateAmountReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportTitle.Text = "💰 تقرير المبالغ - ملخص مالي حسب الأشخاص";
                EmptyMessage.Visibility = Visibility.Collapsed;
                ReportDataGrid.Visibility = Visibility.Visible;

                // إعداد أعمدة تقرير المبالغ
                ReportDataGrid.Columns.Clear();
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "اسم الشخص", Binding = new System.Windows.Data.Binding("PersonName"), Width = 200 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "عدد الديون", Binding = new System.Windows.Data.Binding("DebtsCount"), Width = 100 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "إجمالي المبلغ", Binding = new System.Windows.Data.Binding("TotalAmount") { StringFormat = "{0:N0} دينار" }, Width = 150 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "المبلغ المدفوع", Binding = new System.Windows.Data.Binding("PaidAmount") { StringFormat = "{0:N0} دينار" }, Width = 150 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "المبلغ المتبقي", Binding = new System.Windows.Data.Binding("RemainingAmount") { StringFormat = "{0:N0} دينار" }, Width = 150 });

                // إعداد البيانات - تجميع الديون حسب الأشخاص
                var reportData = _allDebts.GroupBy(d => new { d.PersonId, d.PersonName })
                    .Select(g => new
                    {
                        PersonName = g.Key.PersonName,
                        DebtsCount = g.Count(),
                        TotalAmount = g.Sum(d => d.Amount),
                        PaidAmount = g.Where(d => d.IsSettled).Sum(d => d.Amount),
                        RemainingAmount = g.Where(d => !d.IsSettled).Sum(d => d.Amount)
                    })
                    .OrderByDescending(r => r.TotalAmount)
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"تقرير المبالغ: تم إنشاء {reportData.Count} سجل");

                if (reportData.Count == 0)
                {
                    EmptyMessage.Visibility = Visibility.Visible;
                    ReportDataGrid.Visibility = Visibility.Collapsed;
                    ReportTitle.Text = "💰 تقرير المبالغ - لا توجد بيانات";
                }
                else
                {
                    ReportDataGrid.ItemsSource = reportData;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المبالغ: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"خطأ في تقرير المبالغ: {ex.Message}");
            }
        }

        private void GenerateMonthlyReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportTitle.Text = "📅 التقرير الشهري - ديون الشهر الحالي";
                EmptyMessage.Visibility = Visibility.Collapsed;
                ReportDataGrid.Visibility = Visibility.Visible;

                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;

                // إعداد أعمدة التقرير الشهري
                ReportDataGrid.Columns.Clear();
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "اسم الشخص", Binding = new System.Windows.Data.Binding("PersonName"), Width = 200 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "وصف الدين", Binding = new System.Windows.Data.Binding("Description"), Width = 250 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "المبلغ", Binding = new System.Windows.Data.Binding("Amount") { StringFormat = "{0:N0} دينار" }, Width = 150 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الإنشاء", Binding = new System.Windows.Data.Binding("Date") { StringFormat = "yyyy/MM/dd" }, Width = 120 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الاستحقاق", Binding = new System.Windows.Data.Binding("DueDate") { StringFormat = "yyyy/MM/dd" }, Width = 120 });
                ReportDataGrid.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("StatusText"), Width = 100 });

                // فلترة الديون للشهر الحالي (بناءً على تاريخ الإنشاء أو تاريخ الاستحقاق)
                var monthlyDebts = _allDebts.Where(d =>
                    (d.Date.Month == currentMonth && d.Date.Year == currentYear) ||
                    (d.DueDate.Month == currentMonth && d.DueDate.Year == currentYear))
                    .Select(d => new
                    {
                        PersonName = d.PersonName,
                        Description = d.Description,
                        Amount = d.Amount,
                        Date = d.Date,
                        DueDate = d.DueDate,
                        StatusText = d.IsSettled ? "مدفوع" : (d.DueDate < DateTime.Now ? "متأخر" : "مستحق")
                    })
                    .OrderBy(d => d.Date)
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"التقرير الشهري: تم العثور على {monthlyDebts.Count} دين للشهر {currentMonth}/{currentYear}");

                if (monthlyDebts.Count == 0)
                {
                    EmptyMessage.Visibility = Visibility.Visible;
                    ReportDataGrid.Visibility = Visibility.Collapsed;
                    ReportTitle.Text = $"📅 التقرير الشهري - لا توجد ديون للشهر {currentMonth}/{currentYear}";
                }
                else
                {
                    ReportDataGrid.ItemsSource = monthlyDebts;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير الشهري: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"خطأ في التقرير الشهري: {ex.Message}");
            }
        }

        private void GenerateCustomReport_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة التقرير المخصص
            var customReportWindow = new CustomReportWindow(_allPersons.ToList(), _allDebts.ToList());
            customReportWindow.ShowDialog();
        }

        private void ExportToPDF_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "تصدير التقرير إلى PDF",
                    Filter = "ملفات PDF (*.pdf)|*.pdf",
                    FileName = $"تقرير_الديون_{DateTime.Now:yyyy-MM-dd}.pdf"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // إنشاء محتوى PDF
                    var pdfContent = GeneratePDFContent();

                    // حفظ الملف (محاكاة)
                    System.IO.File.WriteAllText(saveFileDialog.FileName.Replace(".pdf", ".txt"), pdfContent);

                    MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{saveFileDialog.FileName.Replace(".pdf", ".txt")}\n\nملاحظة: تم حفظ الملف كـ TXT مؤقتاً",
                        "تصدير ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "تصدير التقرير إلى Excel",
                    Filter = "ملفات Excel (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv",
                    FileName = $"تقرير_الديون_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // إنشاء محتوى Excel/CSV
                    var csvContent = GenerateCSVContent();

                    // حفظ الملف
                    var fileName = saveFileDialog.FileName;
                    if (fileName.EndsWith(".xlsx"))
                        fileName = fileName.Replace(".xlsx", ".csv");

                    System.IO.File.WriteAllText(fileName, csvContent, System.Text.Encoding.UTF8);

                    MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{fileName}",
                        "تصدير ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء مستند للطباعة
                var printDocument = CreateDetailedPrintDocument();

                // إظهار معاينة الطباعة
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    IDocumentPaginatorSource idpSource = printDocument;
                    printDialog.PrintDocument(idpSource.DocumentPaginator, "تقرير الديون الشامل");
                    MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إعادة تحميل البيانات من قاعدة البيانات
        /// </summary>
        public void RefreshData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("إعادة تحميل البيانات في صفحة التقارير...");
                LoadData();
                UpdateQuickStats();
                System.Diagnostics.Debug.WriteLine("تم تحديث صفحة التقارير بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث صفحة التقارير: {ex.Message}");
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GeneratePDFContent()
        {
            var content = new System.Text.StringBuilder();
            content.AppendLine("تقرير الديون");
            content.AppendLine("=============");
            content.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}");
            content.AppendLine();

            content.AppendLine("ملخص الإحصائيات:");
            content.AppendLine($"إجمالي الديون: {_allDebts.Count}");
            content.AppendLine($"إجمالي المبلغ: {_allDebts.Sum(d => d.Amount):N0} د.ع");
            content.AppendLine($"الديون المسددة: {_allDebts.Count(d => d.IsSettled)}");
            content.AppendLine($"الديون غير المسددة: {_allDebts.Count(d => !d.IsSettled)}");
            content.AppendLine();

            content.AppendLine("تفاصيل الديون:");
            content.AppendLine("اسم الشخص\tالمبلغ\tنوع العملية\tتاريخ الاستحقاق\tالحالة");

            foreach (var debt in _allDebts.Take(50)) // أول 50 دين
            {
                content.AppendLine($"{debt.PersonName}\t{debt.Amount:N0}\t{debt.OperationType}\t{debt.DueDate:yyyy/MM/dd}\t{(debt.IsSettled ? "مسدد" : "غير مسدد")}");
            }

            return content.ToString();
        }

        private string GenerateCSVContent()
        {
            var content = new System.Text.StringBuilder();

            // رأس الجدول
            content.AppendLine("اسم الشخص,المبلغ,نوع العملية,تاريخ الاستحقاق,الحالة,الوصف");

            // البيانات
            foreach (var debt in _allDebts)
            {
                var status = debt.IsSettled ? "مسدد" : "غير مسدد";
                content.AppendLine($"\"{debt.PersonName}\",{debt.Amount},\"{debt.OperationType}\",{debt.DueDate:yyyy-MM-dd},\"{status}\",\"{debt.Description}\"");
            }

            return content.ToString();
        }

        private FlowDocument CreateDetailedPrintDocument()
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(30);
            document.FontFamily = new FontFamily("Arial");
            document.FontSize = 11;
            document.FlowDirection = FlowDirection.RightToLeft;
            document.PageWidth = 793.7; // A4 width in pixels (210mm)
            document.PageHeight = 1122.5; // A4 height in pixels (297mm)
            document.ColumnWidth = double.PositiveInfinity;

            // عنوان التقرير
            var title = new Paragraph();
            title.FontSize = 18;
            title.FontWeight = FontWeights.Bold;
            title.TextAlignment = TextAlignment.Center;
            title.Margin = new Thickness(0, 0, 0, 15);
            title.Inlines.Add(new Run("تقرير الديون الشامل"));
            document.Blocks.Add(title);

            // معلومات التقرير
            var headerInfo = new Paragraph();
            headerInfo.FontSize = 11;
            headerInfo.Margin = new Thickness(0, 0, 0, 15);
            headerInfo.TextAlignment = TextAlignment.Center;
            headerInfo.Inlines.Add(new Run($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm} | إجمالي الأشخاص: {_allPersons.Count} | إجمالي الديون: {_allDebts.Count}"));
            document.Blocks.Add(headerInfo);

            // الإحصائيات التفصيلية
            AddDetailedStatistics(document);

            // جدول الديون
            AddDebtsTable(document);

            // تذييل التقرير
            var footer = new Paragraph();
            footer.FontSize = 8;
            footer.TextAlignment = TextAlignment.Center;
            footer.Margin = new Thickness(0, 15, 0, 0);
            footer.Foreground = Brushes.Gray;
            footer.Inlines.Add(new Run($"نظام إدارة الديون - {DateTime.Now:yyyy/MM/dd HH:mm}"));
            document.Blocks.Add(footer);

            return document;
        }

        private void AddDetailedStatistics(FlowDocument document)
        {
            // حساب الإحصائيات
            var totalAmount = _allDebts.Sum(d => d.Amount);
            var settledAmount = _allDebts.Where(d => d.IsSettled).Sum(d => d.Amount);
            var pendingAmount = totalAmount - settledAmount;
            var settledCount = _allDebts.Count(d => d.IsSettled);
            var pendingCount = _allDebts.Count(d => !d.IsSettled);
            var overdueDebts = _allDebts.Where(d => !d.IsSettled && d.DueDate < DateTime.Now).ToList();
            var overdueAmount = overdueDebts.Sum(d => d.Amount);

            // إحصائيات عامة
            var generalStats = new Paragraph();
            generalStats.FontSize = 11;
            generalStats.Margin = new Thickness(0, 0, 0, 10);
            generalStats.TextAlignment = TextAlignment.Center;
            generalStats.Inlines.Add(new Run("الإحصائيات العامة:") { FontWeight = FontWeights.Bold });
            generalStats.Inlines.Add(new LineBreak());
            generalStats.Inlines.Add(new Run($"إجمالي: {_allDebts.Count} دين ({totalAmount:N0} د.ع) | مسدد: {settledCount} ({settledAmount:N0} د.ع) | متبقي: {pendingCount} ({pendingAmount:N0} د.ع)"));
            generalStats.Inlines.Add(new LineBreak());
            generalStats.Inlines.Add(new Run($"متأخر: {overdueDebts.Count} دين ({overdueAmount:N0} د.ع)") { Foreground = Brushes.Red, FontWeight = FontWeights.Bold });
            document.Blocks.Add(generalStats);

            // إحصائيات حسب الأشخاص (أعلى 10)
            var topDebtors = _allPersons
                .Select(p => new
                {
                    Person = p,
                    TotalDebt = _allDebts.Where(d => d.PersonId == p.Id).Sum(d => d.Amount),
                    PendingDebt = _allDebts.Where(d => d.PersonId == p.Id && !d.IsSettled).Sum(d => d.Amount),
                    DebtCount = _allDebts.Count(d => d.PersonId == p.Id)
                })
                .Where(x => x.TotalDebt > 0)
                .OrderByDescending(x => x.PendingDebt)
                .Take(10)
                .ToList();

            if (topDebtors.Any())
            {
                var topDebtorsStats = new Paragraph();
                topDebtorsStats.FontSize = 10;
                topDebtorsStats.Margin = new Thickness(0, 10, 0, 15);
                topDebtorsStats.Inlines.Add(new Run("أعلى 10 مدينين (حسب المبلغ المتبقي):") { FontWeight = FontWeights.Bold });
                topDebtorsStats.Inlines.Add(new LineBreak());

                foreach (var debtor in topDebtors)
                {
                    topDebtorsStats.Inlines.Add(new Run($"• {debtor.Person.Name}: {debtor.PendingDebt:N0} د.ع ({debtor.DebtCount} دين)"));
                    topDebtorsStats.Inlines.Add(new LineBreak());
                }

                document.Blocks.Add(topDebtorsStats);
            }
        }

        private void AddDebtsTable(FlowDocument document)
        {
            // عنوان الجدول
            var tableTitle = new Paragraph();
            tableTitle.FontSize = 12;
            tableTitle.FontWeight = FontWeights.Bold;
            tableTitle.Margin = new Thickness(0, 10, 0, 10);
            tableTitle.TextAlignment = TextAlignment.Center;
            tableTitle.Inlines.Add(new Run("تفاصيل الديون"));
            document.Blocks.Add(tableTitle);

            // إنشاء الجدول
            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);
            table.FontSize = 9;
            table.Margin = new Thickness(0, 5, 0, 0);

            // تعريف الأعمدة بنسب مئوية
            table.Columns.Add(new TableColumn() { Width = new GridLength(20, GridUnitType.Star) }); // اسم الشخص
            table.Columns.Add(new TableColumn() { Width = new GridLength(15, GridUnitType.Star) }); // المبلغ
            table.Columns.Add(new TableColumn() { Width = new GridLength(12, GridUnitType.Star) }); // تاريخ الدين
            table.Columns.Add(new TableColumn() { Width = new GridLength(12, GridUnitType.Star) }); // تاريخ الاستحقاق
            table.Columns.Add(new TableColumn() { Width = new GridLength(15, GridUnitType.Star) }); // نوع العملية
            table.Columns.Add(new TableColumn() { Width = new GridLength(20, GridUnitType.Star) }); // الوصف
            table.Columns.Add(new TableColumn() { Width = new GridLength(6, GridUnitType.Star) });  // الحالة

            // رأس الجدول
            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;

            AddTableCell(headerRow, "اسم الشخص", true);
            AddTableCell(headerRow, "المبلغ (د.ع)", true);
            AddTableCell(headerRow, "تاريخ الدين", true);
            AddTableCell(headerRow, "تاريخ الاستحقاق", true);
            AddTableCell(headerRow, "نوع العملية", true);
            AddTableCell(headerRow, "الوصف", true);
            AddTableCell(headerRow, "الحالة", true);

            headerRowGroup.Rows.Add(headerRow);
            table.RowGroups.Add(headerRowGroup);

            // بيانات الجدول - أحدث 50 دين
            var dataRowGroup = new TableRowGroup();
            var recentDebts = _allDebts.OrderByDescending(d => d.Date).Take(50);

            foreach (var debt in recentDebts)
            {
                var person = _allPersons.FirstOrDefault(p => p.Id == debt.PersonId);
                var row = new TableRow();

                // تلوين الصفوف المتأخرة
                if (!debt.IsSettled && debt.DueDate < DateTime.Now)
                {
                    row.Background = new SolidColorBrush(Color.FromRgb(255, 240, 240)); // خلفية حمراء فاتحة
                }

                AddTableCell(row, person?.Name ?? "غير معروف");
                AddTableCell(row, debt.Amount.ToString("N0"));
                AddTableCell(row, debt.Date.ToString("yyyy/MM/dd"));
                AddTableCell(row, debt.DueDate.ToString("yyyy/MM/dd"));
                AddTableCell(row, debt.OperationType ?? "غير محدد");
                AddTableCell(row, debt.Description ?? "");
                AddTableCell(row, debt.IsSettled ? "مسدد" : "غير مسدد");

                dataRowGroup.Rows.Add(row);
            }
            table.RowGroups.Add(dataRowGroup);

            document.Blocks.Add(table);

            // ملاحظة عن الجدول
            if (_allDebts.Count > 50)
            {
                var note = new Paragraph();
                note.FontSize = 8;
                note.Margin = new Thickness(0, 5, 0, 0);
                note.TextAlignment = TextAlignment.Center;
                note.Foreground = Brushes.Gray;
                note.Inlines.Add(new Run($"* يعرض الجدول أحدث 50 دين من إجمالي {_allDebts.Count} دين"));
                document.Blocks.Add(note);
            }
        }

        private void AddTableCell(TableRow row, string content, bool isHeader = false)
        {
            var cell = new TableCell();
            cell.BorderBrush = Brushes.Black;
            cell.BorderThickness = new Thickness(1);
            cell.Padding = new Thickness(3);

            var paragraph = new Paragraph();
            paragraph.TextAlignment = TextAlignment.Center;
            paragraph.Margin = new Thickness(0);
            paragraph.FontSize = isHeader ? 9 : 8;
            paragraph.LineHeight = 10;

            if (isHeader)
            {
                paragraph.FontWeight = FontWeights.Bold;
                cell.Background = Brushes.LightGray;
            }

            // تقصير النص الطويل
            var displayContent = content;
            if (!isHeader && content.Length > 30)
            {
                displayContent = content.Substring(0, 27) + "...";
            }

            paragraph.Inlines.Add(new Run(displayContent));
            cell.Blocks.Add(paragraph);
            row.Cells.Add(cell);
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
