using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DebtManagementApp.Models
{
    /// <summary>
    /// إعدادات مزامنة OneDrive
    /// </summary>
    public class OneDriveSettings : INotifyPropertyChanged
    {
        private bool _isEnabled = false;
        private bool _autoSync = true;
        private int _syncIntervalMinutes = 30;
        private string _folderPath = "DebtManagementApp";
        private bool _showSyncNotifications = true;
        private bool _autoDownloadUpdates = false;
        private DateTime _lastSyncTime = DateTime.MinValue;
        private string _lastSyncStatus = "لم يتم المزامنة بعد";

        /// <summary>
        /// تفعيل مزامنة OneDrive
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                _isEnabled = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// المزامنة التلقائية
        /// </summary>
        public bool AutoSync
        {
            get => _autoSync;
            set
            {
                _autoSync = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// فترة المزامنة بالدقائق
        /// </summary>
        public int SyncIntervalMinutes
        {
            get => _syncIntervalMinutes;
            set
            {
                _syncIntervalMinutes = Math.Max(5, value); // الحد الأدنى 5 دقائق
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// مسار المجلد في OneDrive
        /// </summary>
        public string FolderPath
        {
            get => _folderPath;
            set
            {
                _folderPath = value ?? "DebtManagementApp";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// إظهار إشعارات المزامنة
        /// </summary>
        public bool ShowSyncNotifications
        {
            get => _showSyncNotifications;
            set
            {
                _showSyncNotifications = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// تحميل التحديثات تلقائياً
        /// </summary>
        public bool AutoDownloadUpdates
        {
            get => _autoDownloadUpdates;
            set
            {
                _autoDownloadUpdates = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// وقت آخر مزامنة
        /// </summary>
        public DateTime LastSyncTime
        {
            get => _lastSyncTime;
            set
            {
                _lastSyncTime = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(LastSyncTimeText));
            }
        }

        /// <summary>
        /// حالة آخر مزامنة
        /// </summary>
        public string LastSyncStatus
        {
            get => _lastSyncStatus;
            set
            {
                _lastSyncStatus = value ?? "غير محدد";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// نص وقت آخر مزامنة
        /// </summary>
        public string LastSyncTimeText
        {
            get
            {
                if (_lastSyncTime == DateTime.MinValue)
                    return "لم يتم المزامنة بعد";

                var timeSpan = DateTime.Now - _lastSyncTime;
                if (timeSpan.TotalMinutes < 1)
                    return "منذ لحظات";
                else if (timeSpan.TotalHours < 1)
                    return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                else if (timeSpan.TotalDays < 1)
                    return $"منذ {(int)timeSpan.TotalHours} ساعة";
                else
                    return $"منذ {(int)timeSpan.TotalDays} يوم";
            }
        }

        /// <summary>
        /// معرف تطبيق Microsoft
        /// </summary>
        public string ClientId { get; set; } = "04b07795-8ddb-461a-bbee-02f9e1bf7b46";

        /// <summary>
        /// الأذونات المطلوبة
        /// </summary>
        public string[] Scopes { get; set; } = { "Files.ReadWrite", "User.Read" };

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
