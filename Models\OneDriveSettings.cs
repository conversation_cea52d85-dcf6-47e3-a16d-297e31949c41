using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DebtManagementApp.Models
{
    /// <summary>
    /// إعدادات مزامنة OneDrive
    /// </summary>
    public class OneDriveSettings : INotifyPropertyChanged
    {
        private bool _isEnabled = false;
        private bool _autoSync = true;
        private int _syncIntervalMinutes = 30;
        private string _folderPath = "DebtManagementApp";
        private bool _showSyncNotifications = true;
        private bool _autoDownloadUpdates = false;
        private bool _rememberSignIn = true;
        private string _savedSyncType = "";
        private string _savedUserInfo = "";
        private bool _enableCompression = true;
        private bool _enableVersioning = true;
        private int _maxBackupVersions = 5;
        private DateTime _lastSyncTime = DateTime.MinValue;
        private string _lastSyncStatus = "لم يتم المزامنة بعد";

        /// <summary>
        /// تفعيل مزامنة OneDrive
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                _isEnabled = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// المزامنة التلقائية
        /// </summary>
        public bool AutoSync
        {
            get => _autoSync;
            set
            {
                _autoSync = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// فترة المزامنة بالدقائق
        /// </summary>
        public int SyncIntervalMinutes
        {
            get => _syncIntervalMinutes;
            set
            {
                _syncIntervalMinutes = Math.Max(5, value); // الحد الأدنى 5 دقائق
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// مسار المجلد في OneDrive
        /// </summary>
        public string FolderPath
        {
            get => _folderPath;
            set
            {
                _folderPath = value ?? "DebtManagementApp";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// إظهار إشعارات المزامنة
        /// </summary>
        public bool ShowSyncNotifications
        {
            get => _showSyncNotifications;
            set
            {
                _showSyncNotifications = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// تحميل التحديثات تلقائياً
        /// </summary>
        public bool AutoDownloadUpdates
        {
            get => _autoDownloadUpdates;
            set
            {
                _autoDownloadUpdates = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// البقاء مسجل الدخول
        /// </summary>
        public bool RememberSignIn
        {
            get => _rememberSignIn;
            set
            {
                _rememberSignIn = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// نوع المزامنة المحفوظ
        /// </summary>
        public string SavedSyncType
        {
            get => _savedSyncType;
            set
            {
                _savedSyncType = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// معلومات المستخدم المحفوظة
        /// </summary>
        public string SavedUserInfo
        {
            get => _savedUserInfo;
            set
            {
                _savedUserInfo = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// تفعيل ضغط الملفات
        /// </summary>
        public bool EnableCompression
        {
            get => _enableCompression;
            set
            {
                _enableCompression = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// تفعيل نظام الإصدارات
        /// </summary>
        public bool EnableVersioning
        {
            get => _enableVersioning;
            set
            {
                _enableVersioning = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// عدد النسخ الاحتياطية القصوى
        /// </summary>
        public int MaxBackupVersions
        {
            get => _maxBackupVersions;
            set
            {
                _maxBackupVersions = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// وقت آخر مزامنة
        /// </summary>
        public DateTime LastSyncTime
        {
            get => _lastSyncTime;
            set
            {
                _lastSyncTime = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(LastSyncTimeText));
            }
        }

        /// <summary>
        /// حالة آخر مزامنة
        /// </summary>
        public string LastSyncStatus
        {
            get => _lastSyncStatus;
            set
            {
                _lastSyncStatus = value ?? "غير محدد";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// نص وقت آخر مزامنة
        /// </summary>
        public string LastSyncTimeText
        {
            get
            {
                if (_lastSyncTime == DateTime.MinValue)
                    return "لم يتم المزامنة بعد";

                var timeSpan = DateTime.Now - _lastSyncTime;
                if (timeSpan.TotalMinutes < 1)
                    return "منذ لحظات";
                else if (timeSpan.TotalHours < 1)
                    return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                else if (timeSpan.TotalDays < 1)
                    return $"منذ {(int)timeSpan.TotalHours} ساعة";
                else
                    return $"منذ {(int)timeSpan.TotalDays} يوم";
            }
        }

        /// <summary>
        /// معرف تطبيق Microsoft
        /// يجب إنشاء تطبيق جديد في Azure Portal للحصول على معرف صحيح
        /// </summary>
        public string ClientId { get; set; } = "d3590ed6-52b3-4102-aeff-aad2292ab01c";

        /// <summary>
        /// الأذونات المطلوبة
        /// </summary>
        public string[] Scopes { get; set; } = { "Files.ReadWrite", "User.Read" };

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
