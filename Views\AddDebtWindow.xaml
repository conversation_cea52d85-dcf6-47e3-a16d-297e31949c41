<Window x:Class="DebtManagementApp.Views.AddDebtWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة دين جديد" 
        Height="700" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F8F9FA"
        FontFamily="Segoe UI">

    <Window.Resources>
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SectionTitle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>

        <Style x:Key="PrimaryButton" TargetType="Button">
            <Setter Property="Background" Value="#28A745"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#218838"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button">
            <Setter Property="Background" Value="#6C757D"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#545B62"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="💰" FontSize="28" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <StackPanel>
                <TextBlock Text="إضافة دين جديد" 
                           FontSize="24" FontWeight="Bold" 
                           Foreground="#2C3E50"/>
                <TextBlock x:Name="PersonNameLabel" Text="للشخص: [اسم الشخص]" 
                           FontSize="14" Foreground="#6C757D"/>
            </StackPanel>
        </StackPanel>

        <!-- المحتوى -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- معلومات الدين الأساسية -->
                <Border Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <TextBlock Text="📋 معلومات الدين الأساسية" Style="{StaticResource SectionTitle}"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- نوع العملية -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع العملية:" 
                                       VerticalAlignment="Center" Margin="0,0,10,12"/>
                            <ComboBox x:Name="OperationTypeComboBox" Grid.Row="0" Grid.Column="1"
                                      Margin="0,0,0,12" Padding="8">
                                <ComboBoxItem Content="قطع" IsSelected="True"/>
                                <ComboBoxItem Content="لحام"/>
                                <ComboBoxItem Content="ثني"/>
                                <ComboBoxItem Content="تشكيل"/>
                                <ComboBoxItem Content="أخرى"/>
                            </ComboBox>
                            
                            <!-- المبلغ -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="المبلغ (د.ع):" 
                                       VerticalAlignment="Center" Margin="0,0,10,12"/>
                            <TextBox x:Name="AmountTextBox" Grid.Row="1" Grid.Column="1"
                                     Margin="0,0,0,12" Padding="8"
                                     Text="0"/>
                            
                            <!-- تاريخ الاستحقاق -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ الاستحقاق:" 
                                       VerticalAlignment="Center" Margin="0,0,10,12"/>
                            <DatePicker x:Name="DueDatePicker" Grid.Row="2" Grid.Column="1"
                                        Margin="0,0,0,12" Padding="8"/>
                            
                            <!-- الوصف -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="الوصف:" 
                                       VerticalAlignment="Top" Margin="0,5,10,0"/>
                            <TextBox x:Name="DescriptionTextBox" Grid.Row="3" Grid.Column="1"
                                     Height="80" TextWrapping="Wrap" 
                                     AcceptsReturn="True" Padding="8"
                                     VerticalScrollBarVisibility="Auto"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- تفاصيل إضافية -->
                <Border Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <TextBlock Text="📝 تفاصيل إضافية" Style="{StaticResource SectionTitle}"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- الأولوية -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الأولوية:" 
                                       VerticalAlignment="Center" Margin="0,0,10,12"/>
                            <ComboBox x:Name="PriorityComboBox" Grid.Row="0" Grid.Column="1"
                                      Margin="0,0,0,12" Padding="8" SelectedIndex="1">
                                <ComboBoxItem Content="منخفضة"/>
                                <ComboBoxItem Content="متوسطة"/>
                                <ComboBoxItem Content="عالية"/>
                                <ComboBoxItem Content="عاجلة"/>
                            </ComboBox>
                            
                            <!-- حالة الدين -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="حالة الدين:"
                                       VerticalAlignment="Center" Margin="0,0,10,12"/>
                            <ComboBox x:Name="StatusComboBox" Grid.Row="1" Grid.Column="1"
                                      Margin="0,0,0,12" Padding="8" SelectedIndex="0"
                                      SelectionChanged="StatusComboBox_SelectionChanged">
                                <ComboBoxItem Content="غير مسدد"/>
                                <ComboBoxItem Content="مسدد جزئياً"/>
                                <ComboBoxItem Content="مسدد"/>
                            </ComboBox>

                            <!-- المبلغ المسدد جزئياً -->
                            <TextBlock x:Name="PartialAmountLabel" Grid.Row="2" Grid.Column="0" Text="المبلغ المسدد:"
                                       VerticalAlignment="Center" Margin="0,0,10,12"
                                       Visibility="Collapsed"/>
                            <StackPanel x:Name="PartialAmountPanel" Grid.Row="2" Grid.Column="1"
                                        Orientation="Horizontal" Margin="0,0,0,12"
                                        Visibility="Collapsed">
                                <TextBox x:Name="PartialAmountTextBox" Width="150" Padding="8"
                                         TextChanged="PartialAmountTextBox_TextChanged"/>
                                <TextBlock Text="دينار" VerticalAlignment="Center" Margin="8,0,0,0"
                                           Foreground="#6C757D" FontWeight="SemiBold"/>
                                <TextBlock x:Name="RemainingAmountText" VerticalAlignment="Center"
                                           Margin="20,0,0,0" FontWeight="Bold" Foreground="#DC3545"/>
                            </StackPanel>

                            <!-- ملاحظات -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="ملاحظات:"
                                       VerticalAlignment="Top" Margin="0,5,10,0"/>
                            <TextBox x:Name="NotesTextBox" Grid.Row="3" Grid.Column="1"
                                     Height="60" TextWrapping="Wrap"
                                     AcceptsReturn="True" Padding="8"
                                     VerticalScrollBarVisibility="Auto"/>
                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Content="💾 حفظ الدين" 
                    Style="{StaticResource PrimaryButton}"
                    Margin="0,0,10,0"
                    Click="SaveDebt_Click"/>
            <Button Content="❌ إلغاء" 
                    Style="{StaticResource SecondaryButton}"
                    Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
