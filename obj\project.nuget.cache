{"version": 2, "dgSpecHash": "DNbexH56oC4=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\Downloads\\aaee\\aaee\\DebtManagementApp.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.39.0\\azure.core.1.39.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.9.0\\fluentvalidation.11.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesigncolors\\5.2.1\\materialdesigncolors.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesignthemes\\5.2.1\\materialdesignthemes.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite\\9.0.6\\microsoft.data.sqlite.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite.core\\9.0.6\\microsoft.data.sqlite.core.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.graph\\5.56.0\\microsoft.graph.5.56.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.graph.core\\3.1.12\\microsoft.graph.core.3.1.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.6.0\\microsoft.identitymodel.abstractions.7.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.6.0\\microsoft.identitymodel.jsonwebtokens.7.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.6.0\\microsoft.identitymodel.logging.7.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.6.0\\microsoft.identitymodel.protocols.7.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.6.0\\microsoft.identitymodel.protocols.openidconnect.7.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.6.0\\microsoft.identitymodel.tokens.7.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.abstractions\\1.9.1\\microsoft.kiota.abstractions.1.9.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.authentication.azure\\1.1.7\\microsoft.kiota.authentication.azure.1.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.http.httpclientlibrary\\1.4.3\\microsoft.kiota.http.httpclientlibrary.1.4.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.serialization.form\\1.2.4\\microsoft.kiota.serialization.form.1.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.serialization.json\\1.3.3\\microsoft.kiota.serialization.json.1.3.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.serialization.multipart\\1.1.5\\microsoft.kiota.serialization.multipart.1.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kiota.serialization.text\\1.2.2\\microsoft.kiota.serialization.text.1.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\8.0.0\\microsoft.win32.systemevents.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.39\\microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlog\\5.2.8\\nlog.5.2.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oxyplot.core\\2.2.0\\oxyplot.core.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oxyplot.wpf\\2.2.0\\oxyplot.wpf.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\oxyplot.wpf.shared\\2.2.0\\oxyplot.wpf.shared.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.bundle_e_sqlite3\\2.1.10\\sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.core\\2.1.10\\sqlitepclraw.core.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.lib.e_sqlite3\\2.1.10\\sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.provider.e_sqlite3\\2.1.10\\sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\std.uritemplate\\0.0.57\\std.uritemplate.0.0.57.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\8.0.0\\system.drawing.common.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.6.0\\system.identitymodel.tokens.jwt.7.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.3\\system.memory.4.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.7.2\\system.text.encodings.web.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512"], "logs": [{"code": "NU1101", "level": "Error", "message": "Unable to find package Microsoft.Graph.Authentication. No packages exist with this id in source(s): Microsoft Visual Studio Offline Packages, nuget.org", "projectPath": "C:\\Users\\<USER>\\Downloads\\aaee\\aaee\\DebtManagementApp.csproj", "filePath": "C:\\Users\\<USER>\\Downloads\\aaee\\aaee\\DebtManagementApp.csproj", "libraryId": "Microsoft.Graph.Authentication", "targetGraphs": ["net9.0-windows7.0"]}]}