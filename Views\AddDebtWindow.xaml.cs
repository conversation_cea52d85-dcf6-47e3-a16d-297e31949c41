using System;
using System.Windows;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;
using DebtManagementApp.Services;

namespace DebtManagementApp.Views
{
    public partial class AddDebtWindow : Window
    {
        private readonly Person _selectedPerson;

        public AddDebtWindow(Person selectedPerson)
        {
            InitializeComponent();
            _selectedPerson = selectedPerson ?? throw new ArgumentNullException(nameof(selectedPerson));
            
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            try
            {
                // عرض اسم الشخص
                PersonNameLabel.Text = $"للشخص: {_selectedPerson.Name}";
                
                // تعيين تاريخ الاستحقاق الافتراضي (بعد شهر من اليوم)
                DueDatePicker.SelectedDate = DateTime.Now.AddMonths(1);

                // التركيز على حقل المبلغ
                AmountTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveDebt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // إنشاء كائن الدين الجديد
                var newDebt = CreateDebtFromInput();
                
                // حفظ الدين في قاعدة البيانات
                DatabaseHelper.AddDebt(newDebt);
                
                // تسجيل النشاط
                ActivityService.LogActivity(
                    $"تم إضافة دين لـ {_selectedPerson.Name} بمبلغ {newDebt.Amount:N0} دينار", 
                    "Debt");

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من المبلغ
            if (!decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من الصفر", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountTextBox.Focus();
                return false;
            }

            // التحقق من تاريخ الاستحقاق
            if (!DueDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد تاريخ الاستحقاق", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DueDatePicker.Focus();
                return false;
            }

            // التحقق من أن تاريخ الاستحقاق ليس في الماضي
            if (DueDatePicker.SelectedDate.Value.Date < DateTime.Now.Date)
            {
                var result = MessageBox.Show("تاريخ الاستحقاق في الماضي. هل تريد المتابعة؟",
                    "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return false;
            }

            // التحقق من الوصف
            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال وصف للدين", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DescriptionTextBox.Focus();
                return false;
            }

            // التحقق من المبلغ المسدد جزئياً إذا كانت الحالة "مسدد جزئياً"
            if (StatusComboBox.SelectedIndex == 1) // مسدد جزئياً
            {
                if (!decimal.TryParse(PartialAmountTextBox.Text.Replace(",", ""), out decimal paidAmount) || paidAmount < 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح للمبلغ المسدد", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    PartialAmountTextBox.Focus();
                    return false;
                }

                if (paidAmount > amount)
                {
                    MessageBox.Show("المبلغ المسدد لا يمكن أن يكون أكبر من المبلغ الكلي", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    PartialAmountTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private Debt CreateDebtFromInput()
        {
            var operationType = ((System.Windows.Controls.ComboBoxItem)OperationTypeComboBox.SelectedItem)?.Content?.ToString() ?? "قطع";
            var priority = ((System.Windows.Controls.ComboBoxItem)PriorityComboBox.SelectedItem)?.Content?.ToString() ?? "متوسطة";
            var status = ((System.Windows.Controls.ComboBoxItem)StatusComboBox.SelectedItem)?.Content?.ToString() ?? "غير مسدد";

            // حساب المبلغ الفعلي بناءً على الحالة
            decimal originalAmount = decimal.Parse(AmountTextBox.Text.Replace(",", ""));
            decimal finalAmount = originalAmount;

            // إذا كان مسدد جزئياً، نقص المبلغ المسدد من المبلغ الكلي
            if (status == "مسدد جزئياً" && decimal.TryParse(PartialAmountTextBox.Text.Replace(",", ""), out decimal paidAmount))
            {
                finalAmount = originalAmount - paidAmount;
                // التأكد من أن المبلغ المتبقي لا يكون سالباً
                if (finalAmount < 0) finalAmount = 0;
            }

            return new Debt
            {
                PersonId = _selectedPerson.Id,
                PersonName = _selectedPerson.Name,
                Amount = finalAmount, // المبلغ المتبقي بعد خصم المسدد جزئياً
                DueDate = DueDatePicker.SelectedDate.Value,
                Description = DescriptionTextBox.Text.Trim(),
                OperationType = operationType,
                Notes = NotesTextBox.Text?.Trim() ?? "",
                IsSettled = status == "مسدد",
                Date = DateTime.Now,
                LastUpdated = DateTime.Now,

                // خصائص إضافية يمكن إضافتها لاحقاً
                CuttingCost = operationType == "قطع" ? finalAmount : 0,
                WeldingCost = operationType == "لحام" ? finalAmount : 0,
                BendingCost = operationType == "ثني" ? finalAmount : 0,
                IronCost = 0, // يمكن إضافة حقل منفصل لهذا
                TransportCost = 0 // يمكن إضافة حقل منفصل لهذا
            };
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void AmountTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            // تنسيق المبلغ أثناء الكتابة (اختياري)
            try
            {
                var textBox = sender as System.Windows.Controls.TextBox;
                if (textBox != null && !string.IsNullOrEmpty(textBox.Text))
                {
                    // إزالة التنسيق السابق
                    var text = textBox.Text.Replace(",", "").Replace(".", "");
                    
                    if (decimal.TryParse(text, out decimal value))
                    {
                        // حفظ موضع المؤشر
                        int selectionStart = textBox.SelectionStart;
                        
                        // تطبيق التنسيق
                        textBox.Text = value.ToString("N0");
                        
                        // استعادة موضع المؤشر
                        textBox.SelectionStart = Math.Min(selectionStart, textBox.Text.Length);
                    }
                }
            }
            catch
            {
                // تجاهل الأخطاء في التنسيق
            }
        }

        // معالج تغيير حالة الدين
        private void StatusComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            // التأكد من أن العناصر موجودة
            if (PartialAmountLabel == null || PartialAmountPanel == null)
                return;

            if (StatusComboBox.SelectedIndex == 1) // مسدد جزئياً
            {
                PartialAmountLabel.Visibility = Visibility.Visible;
                PartialAmountPanel.Visibility = Visibility.Visible;
                UpdateRemainingAmount();
            }
            else
            {
                PartialAmountLabel.Visibility = Visibility.Collapsed;
                PartialAmountPanel.Visibility = Visibility.Collapsed;
            }
        }

        // معالج تغيير المبلغ المسدد جزئياً
        private void PartialAmountTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateRemainingAmount();
        }

        // تحديث المبلغ المتبقي
        private void UpdateRemainingAmount()
        {
            try
            {
                // التأكد من أن العناصر موجودة
                if (AmountTextBox == null || PartialAmountTextBox == null || RemainingAmountText == null)
                    return;

                if (decimal.TryParse(AmountTextBox.Text.Replace(",", ""), out decimal totalAmount) &&
                    decimal.TryParse(PartialAmountTextBox.Text.Replace(",", ""), out decimal paidAmount))
                {
                    decimal remainingAmount = totalAmount - paidAmount;

                    if (remainingAmount >= 0)
                    {
                        RemainingAmountText.Text = $"المتبقي: {remainingAmount:N0} دينار";
                        RemainingAmountText.Foreground = new System.Windows.Media.SolidColorBrush(
                            System.Windows.Media.Color.FromRgb(0x28, 0xA7, 0x45)); // أخضر
                    }
                    else
                    {
                        RemainingAmountText.Text = $"زيادة في الدفع: {Math.Abs(remainingAmount):N0} دينار";
                        RemainingAmountText.Foreground = new System.Windows.Media.SolidColorBrush(
                            System.Windows.Media.Color.FromRgb(0xDC, 0x35, 0x45)); // أحمر
                    }
                }
                else
                {
                    RemainingAmountText.Text = "";
                }
            }
            catch
            {
                RemainingAmountText.Text = "";
            }
        }
    }
}
