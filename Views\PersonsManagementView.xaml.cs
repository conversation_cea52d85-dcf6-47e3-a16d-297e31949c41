using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;
using DebtManagementApp.Services;

namespace DebtManagementApp.Views
{
    public partial class PersonsManagementView : UserControl, INotifyPropertyChanged
    {
        // بيانات مشتركة عبر جميع instances
        private static ObservableCollection<Person> _staticAllPersons = new ObservableCollection<Person>();
        private static bool _staticDataLoaded = false;

        private readonly object _databaseService;
        private ObservableCollection<Person> _allPersons;
        private ObservableCollection<Person> _filteredPersons;
        private Person _selectedPerson;
        private string _searchText = "";
        private bool _isEditing = false;

        public PersonsManagementView()
        {
            InitializeComponent();
            _databaseService = null; // سيتم إصلاحه لاحقاً

            // تهيئة المجموعات
            _allPersons = new ObservableCollection<Person>();
            _filteredPersons = new ObservableCollection<Person>();

            DataContext = this;
            LoadSampleData(); // بيانات تجريبية

            // نسخ البيانات من المجموعة المشتركة إلى المجموعة المحلية
            RefreshPersonsList();

            FilterPersons(); // عرض البيانات الحالية
            UpdateStatistics(); // تحديث الإحصائيات

            // تسجيل أحداث حفظ إعدادات الأعمدة
            Loaded += (s, e) => ColumnSettingsHelper.RegisterColumnEvents(PersonsDataGrid, "PersonsManagementGrid");
        }

        public ObservableCollection<Person> FilteredPersons
        {
            get => _filteredPersons;
            set
            {
                _filteredPersons = value;
                OnPropertyChanged(nameof(FilteredPersons));
            }
        }

        public Person SelectedPerson
        {
            get => _selectedPerson;
            set
            {
                _selectedPerson = value;
                OnPropertyChanged(nameof(SelectedPerson));
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged(nameof(SearchText));
                FilterPersons();
            }
        }

        /// <summary>
        /// تحديث قائمة الأشخاص المحلية من البيانات المشتركة
        /// </summary>
        private void RefreshPersonsList()
        {
            try
            {
                _allPersons.Clear();

                if (_staticAllPersons != null)
                {
                    foreach (var person in _staticAllPersons)
                    {
                        _allPersons.Add(person);
                    }
                    System.Diagnostics.Debug.WriteLine($"RefreshPersonsList: تم نسخ {_allPersons.Count} شخص من البيانات المشتركة");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("RefreshPersonsList: البيانات المشتركة فارغة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث قائمة الأشخاص: {ex.Message}");
            }
        }

        private void LoadSampleData()
        {
            try
            {
                // تحميل البيانات مرة واحدة فقط
                if (_staticDataLoaded)
                    return;

                _staticAllPersons.Clear();

                // تحميل البيانات من قاعدة البيانات
                try
                {
                    var personsFromDb = DatabaseHelper.GetAllPersons();
                    System.Diagnostics.Debug.WriteLine($"تم تحميل {personsFromDb.Count} شخص من قاعدة البيانات");

                    foreach (var person in personsFromDb)
                    {
                        // تحويل Location إلى Address إذا لزم الأمر
                        if (string.IsNullOrEmpty(person.Address) && !string.IsNullOrEmpty(person.Location))
                        {
                            person.Address = person.Location;
                        }
                        if (string.IsNullOrEmpty(person.Notes) && !string.IsNullOrEmpty(person.AdditionalInfo))
                        {
                            person.Notes = person.AdditionalInfo;
                        }
                        _staticAllPersons.Add(person);
                    }

                    // إذا لم توجد بيانات في قاعدة البيانات، أضف البيانات التجريبية
                    if (personsFromDb.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("قاعدة البيانات فارغة، سيتم إضافة البيانات التجريبية");
                        LoadSamplePersons();
                    }
                }
                catch (Exception dbEx)
                {
                    // إذا فشل تحميل البيانات من قاعدة البيانات، استخدم البيانات التجريبية
                    System.Diagnostics.Debug.WriteLine($"فشل تحميل البيانات من قاعدة البيانات: {dbEx.Message}");
                    MessageBox.Show($"فشل تحميل البيانات من قاعدة البيانات، سيتم استخدام البيانات التجريبية: {dbEx.Message}",
                        "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    LoadSamplePersons();
                }

                _staticDataLoaded = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات التجريبية للأشخاص
        /// </summary>
        private void LoadSamplePersons()
        {
            try
            {
                // بيانات تجريبية - عملاء أعمال الحديد في العراق
                var samplePersons = new List<Person>
                {
                    new Person { Id = 1, Name = "أحمد محمد علي", Phone = "07701234567", Address = "بغداد - الكرادة", Notes = "مقاول حديد تسليح", DebtsCount = 3, TotalAmount = 2180000 },
                    new Person { Id = 2, Name = "فاطمة عبدالله", Phone = "07809876543", Address = "البصرة - الجمعيات", Notes = "أعمال لحام", DebtsCount = 2, TotalAmount = 540000 },
                    new Person { Id = 3, Name = "محمد سعد الغامدي", Phone = "07511122334", Address = "أربيل - عنكاوا", Notes = "هياكل حديدية", DebtsCount = 2, TotalAmount = 1040000 },
                    new Person { Id = 4, Name = "نورا خالد", Phone = "07667788990", Address = "الموصل - الجامعة", Notes = "عميل جديد", DebtsCount = 0, TotalAmount = 0 },
                    new Person { Id = 5, Name = "عبدالرحمن الشهري", Phone = "07844556677", Address = "النجف - المركز", Notes = "تاجر حديد", DebtsCount = 4, TotalAmount = 3250000 },
                    new Person { Id = 6, Name = "سارة أحمد", Phone = "", Address = "كربلاء - العباسية", Notes = "بدون هاتف", DebtsCount = 1, TotalAmount = 450000 },
                    new Person { Id = 7, Name = "يوسف العتيبي", Phone = "07533445566", Address = "السليمانية - المركز", Notes = "أعمال تقطيع", DebtsCount = 2, TotalAmount = 890000 },
                    new Person { Id = 8, Name = "هند محمد", Phone = "07722334455", Address = "ديالى - بعقوبة", Notes = "عميل قديم", DebtsCount = 1, TotalAmount = 675000 }
                };

                foreach (var person in samplePersons)
                {
                    _staticAllPersons.Add(person);
                }

                System.Diagnostics.Debug.WriteLine($"تم إضافة {samplePersons.Count} شخص تجريبي");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات التجريبية: {ex.Message}");
            }
        }

        private void FilterPersons()
        {
            try
            {
                if (FilteredPersons == null || _allPersons == null)
                    return;

                FilteredPersons.Clear();

                var filtered = string.IsNullOrWhiteSpace(SearchText)
                    ? _allPersons
                    : _allPersons.Where(p =>
                        (!string.IsNullOrEmpty(p.Name) && p.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase)) ||
                        (!string.IsNullOrEmpty(p.Phone) && p.Phone.Contains(SearchText)) ||
                        (!string.IsNullOrEmpty(p.Address) && p.Address.Contains(SearchText, StringComparison.OrdinalIgnoreCase)));

                foreach (var person in filtered)
                {
                    FilteredPersons.Add(person);
                }

                System.Diagnostics.Debug.WriteLine($"FilterPersons: عدد الأشخاص الكلي: {_allPersons.Count}, المفلترين: {FilteredPersons.Count}, نص البحث: '{SearchText}'");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فلترة الأشخاص: {ex.Message}");
                MessageBox.Show($"خطأ في فلترة الأشخاص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            // تحديث إحصائيات الديون من قاعدة البيانات
            UpdatePersonsDebtsFromDatabase();

            TotalPersonsText.Text = _allPersons.Count.ToString();
            PersonsWithDebtsText.Text = _allPersons.Count(p => p.DebtsCount > 0).ToString();
            PersonsWithPhonesText.Text = _allPersons.Count(p => !string.IsNullOrEmpty(p.Phone)).ToString();
        }

        private void UpdatePersonsDebtsFromDatabase()
        {
            try
            {
                // تحميل جميع الديون من قاعدة البيانات
                var allDebts = DatabaseHelper.GetAllDebts();

                // تجميع الديون حسب الشخص
                var debtsByPerson = allDebts.GroupBy(d => d.PersonId).ToDictionary(
                    g => g.Key,
                    g => new { Count = g.Count(), Total = g.Sum(d => d.Amount) }
                );

                // تحديث بيانات كل شخص
                foreach (var person in _allPersons)
                {
                    if (debtsByPerson.ContainsKey(person.Id))
                    {
                        person.DebtsCount = debtsByPerson[person.Id].Count;
                        person.TotalAmount = debtsByPerson[person.Id].Total;
                    }
                    else
                    {
                        person.DebtsCount = 0;
                        person.TotalAmount = 0;
                    }
                }

                // تحديث القائمة المفلترة
                FilterPersons();
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، لا نعرض رسالة لتجنب إزعاج المستخدم
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث إحصائيات الديون: {ex.Message}");
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // التصفية تتم تلقائياً عبر Binding
        }

        private void AddPerson_Click(object sender, RoutedEventArgs e)
        {
            ShowPersonForm(false);
        }

        private void EditPerson_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الشخص المحدد من DataGrid
            var selectedPerson = PersonsDataGrid.SelectedItem as Person;
            if (selectedPerson == null)
            {
                MessageBox.Show("يرجى تحديد شخص للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            SelectedPerson = selectedPerson;
            ShowPersonForm(true);
        }

        private void ViewPersonDebts_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الشخص المحدد من DataGrid
            var selectedPerson = PersonsDataGrid.SelectedItem as Person;
            if (selectedPerson == null)
            {
                MessageBox.Show("يرجى تحديد شخص لعرض ديونه", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    var personDebtsView = new PersonDebtsView();
                    personDebtsView.SetPerson(selectedPerson, () => {
                        // العودة لواجهة إدارة الأشخاص
                        var personsView = new PersonsManagementView();
                        mainWindow.MainContent.Content = personsView;
                        mainWindow.ContentTitle.Text = "👥 إدارة الأشخاص";
                    });

                    mainWindow.MainContent.Content = personDebtsView;
                    mainWindow.ContentTitle.Text = $"💰 ديون {selectedPerson.Name}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض ديون الشخص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeletePerson_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الشخص المحدد من DataGrid
            var selectedPerson = PersonsDataGrid.SelectedItem as Person;
            if (selectedPerson == null)
            {
                MessageBox.Show("يرجى تحديد شخص للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الشخص '{selectedPerson.Name}'؟\nسيتم حذف جميع الديون المرتبطة به أيضاً.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // حذف من قاعدة البيانات
                    try
                    {
                        DatabaseHelper.DeletePerson(selectedPerson.Id);
                    }
                    catch (Exception dbEx)
                    {
                        MessageBox.Show($"تم حذف الشخص من الذاكرة ولكن فشل حذفه من قاعدة البيانات: {dbEx.Message}",
                            "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }

                    // حذف من البيانات المحلية
                    _allPersons.Remove(selectedPerson);
                    FilteredPersons.Remove(selectedPerson);
                    UpdateStatistics();

                    MessageBox.Show("تم حذف الشخص بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // تسجيل النشاط
                    ActivityService.LogPersonDeleted(selectedPerson.Name);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الشخص: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void AddDebtForPerson_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الشخص المحدد من DataGrid
            var selectedPerson = PersonsDataGrid.SelectedItem as Person;
            if (selectedPerson == null)
            {
                MessageBox.Show("يرجى تحديد شخص لإضافة دين له", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // الانتقال إلى واجهة ديون الأشخاص مع فتح نموذج إضافة دين للشخص المحدد
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    var debtsView = new DebtsManagementView();
                    mainWindow.MainContent.Content = debtsView;
                    mainWindow.ContentTitle.Text = "💰 ديون الأشخاص";

                    // فتح نموذج إضافة دين مع تحديد الشخص مسبقاً
                    debtsView.OpenAddDebtFormForPerson(selectedPerson);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PersonsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (SelectedPerson != null)
            {
                ShowPersonForm(true);
            }
        }

        private void ShowPersonForm(bool isEditing)
        {
            try
            {
                _isEditing = isEditing;

                if (FormTitleText == null || NameTextBox == null || PhoneTextBox == null ||
                    AddressTextBox == null || NotesTextBox == null || SaveButton == null ||
                    PersonFormOverlay == null)
                {
                    return;
                }

                if (isEditing && SelectedPerson != null)
                {
                    FormTitleText.Text = "✏️ تعديل بيانات الشخص";
                    NameTextBox.Text = SelectedPerson.Name ?? "";
                    PhoneTextBox.Text = SelectedPerson.Phone ?? "";
                    AddressTextBox.Text = SelectedPerson.Address ?? "";
                    NotesTextBox.Text = SelectedPerson.Notes ?? "";
                    SaveButton.Content = "💾 تحديث";
                }
                else
                {
                    FormTitleText.Text = "➕ إضافة شخص جديد";
                    NameTextBox.Text = "";
                    PhoneTextBox.Text = "";
                    AddressTextBox.Text = "";
                    NotesTextBox.Text = "";
                    SaveButton.Content = "💾 حفظ";
                }

                PersonFormOverlay.Visibility = Visibility.Visible;
                NameTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseForm_Click(object sender, RoutedEventArgs e)
        {
            PersonFormOverlay.Visibility = Visibility.Collapsed;
        }

        private void SavePerson_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الشخص", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return;
            }

            // التحقق من عدم تكرار الاسم
            var trimmedName = NameTextBox.Text.Trim();
            var existingPerson = _allPersons.FirstOrDefault(p =>
                p.Name.Equals(trimmedName, StringComparison.OrdinalIgnoreCase) &&
                (!_isEditing || p.Id != SelectedPerson?.Id));

            if (existingPerson != null)
            {
                MessageBox.Show($"يوجد شخص بنفس الاسم '{trimmedName}' مسجل مسبقاً.\nيرجى استخدام اسم مختلف أو إضافة تفاصيل إضافية للتمييز.",
                    "اسم مكرر", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                NameTextBox.SelectAll();
                return;
            }

            try
            {
                var person = new Person
                {
                    Name = NameTextBox.Text.Trim(),
                    Phone = PhoneTextBox.Text.Trim(),
                    Address = AddressTextBox.Text.Trim(),
                    Notes = NotesTextBox.Text.Trim()
                };

                if (_isEditing && SelectedPerson != null)
                {
                    // تحديث البيانات الموجودة
                    person.Id = SelectedPerson.Id;
                    person.DebtsCount = SelectedPerson.DebtsCount;
                    person.TotalAmount = SelectedPerson.TotalAmount;

                    // تحديث في قاعدة البيانات
                    try
                    {
                        DatabaseHelper.UpdatePerson(person);

                        // تحديث في المجموعة المحلية
                        var localIndex = _allPersons.IndexOf(SelectedPerson);
                        if (localIndex >= 0)
                        {
                            _allPersons[localIndex] = person;
                        }

                        // تحديث في المجموعة المشتركة
                        var staticIndex = _staticAllPersons.IndexOf(SelectedPerson);
                        if (staticIndex >= 0)
                        {
                            _staticAllPersons[staticIndex] = person;
                        }

                        MessageBox.Show("تم تحديث بيانات الشخص بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        // تسجيل النشاط
                        ActivityService.LogPersonUpdated(person.Name);
                    }
                    catch (Exception dbEx)
                    {
                        MessageBox.Show($"تم تحديث البيانات في الذاكرة ولكن فشل حفظها في قاعدة البيانات: {dbEx.Message}",
                            "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);

                        // تحديث في المجموعة المحلية
                        var localIndex = _allPersons.IndexOf(SelectedPerson);
                        if (localIndex >= 0)
                        {
                            _allPersons[localIndex] = person;
                        }

                        // تحديث في المجموعة المشتركة
                        var staticIndex = _staticAllPersons.IndexOf(SelectedPerson);
                        if (staticIndex >= 0)
                        {
                            _staticAllPersons[staticIndex] = person;
                        }
                    }
                }
                else
                {
                    // إضافة شخص جديد
                    person.Id = _allPersons.Count > 0 ? _allPersons.Max(p => p.Id) + 1 : 1;
                    person.DebtsCount = 0;
                    person.TotalAmount = 0;

                    // حفظ في قاعدة البيانات
                    try
                    {
                        DatabaseHelper.AddPerson(person);
                        _allPersons.Add(person);
                        _staticAllPersons.Add(person); // إضافة للمجموعة المشتركة أيضاً

                        MessageBox.Show("تم إضافة الشخص بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        // تسجيل النشاط
                        ActivityService.LogPersonAdded(person.Name);
                    }
                    catch (Exception dbEx)
                    {
                        MessageBox.Show($"تم إضافة الشخص في الذاكرة ولكن فشل حفظه في قاعدة البيانات: {dbEx.Message}",
                            "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                        _allPersons.Add(person);
                        _staticAllPersons.Add(person); // إضافة للمجموعة المشتركة أيضاً
                    }
                }

                CloseForm_Click(sender, e);
                FilterPersons(); // إعادة تطبيق الفلترة
                UpdateStatistics();

                // تحديث الصفحة الرئيسية
                RefreshMainDashboard();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إعادة تعيين حالة التحميل لإجبار إعادة التحميل
                _staticDataLoaded = false;

                // إعادة تحميل البيانات
                LoadSampleData();
                RefreshPersonsList();
                FilterPersons();
                UpdateStatistics();

                MessageBox.Show($"تم تحديث البيانات بنجاح\n\nعدد الأشخاص: {_allPersons.Count}",
                    "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إعادة تحميل البيانات من قاعدة البيانات فقط
        /// </summary>
        public void ReloadFromDatabase()
        {
            try
            {
                _staticAllPersons.Clear();

                var personsFromDb = DatabaseHelper.GetAllPersons();
                System.Diagnostics.Debug.WriteLine($"ReloadFromDatabase: تم تحميل {personsFromDb.Count} شخص من قاعدة البيانات");

                foreach (var person in personsFromDb)
                {
                    // تحويل Location إلى Address إذا لزم الأمر
                    if (string.IsNullOrEmpty(person.Address) && !string.IsNullOrEmpty(person.Location))
                    {
                        person.Address = person.Location;
                    }
                    if (string.IsNullOrEmpty(person.Notes) && !string.IsNullOrEmpty(person.AdditionalInfo))
                    {
                        person.Notes = person.AdditionalInfo;
                    }
                    _staticAllPersons.Add(person);
                }

                RefreshPersonsList();
                FilterPersons();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تحميل البيانات من قاعدة البيانات: {ex.Message}");
                MessageBox.Show($"خطأ في إعادة تحميل البيانات من قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث الصفحة الرئيسية
        /// </summary>
        private void RefreshMainDashboard()
        {
            try
            {
                // تأخير صغير للتأكد من حفظ البيانات
                System.Threading.Tasks.Task.Delay(100).ContinueWith(_ =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        // البحث عن النافذة الرئيسية وتحديث الصفحة الرئيسية
                        var mainWindow = Application.Current.MainWindow as MainWindow;
                        if (mainWindow != null)
                        {
                            mainWindow.RefreshDashboard();
                            mainWindow.ForceRefreshActivities(); // إجبار تحديث الأنشطة

                            // تحديث صفحة التقارير إذا كانت مفتوحة
                            RefreshReportsPage(mainWindow);

                            System.Diagnostics.Debug.WriteLine("تم تحديث الصفحة الرئيسية والأنشطة والتقارير من صفحة إدارة الأشخاص");
                        }
                    });
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الصفحة الرئيسية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث صفحة التقارير إذا كانت مفتوحة
        /// </summary>
        private void RefreshReportsPage(MainWindow mainWindow)
        {
            try
            {
                // البحث عن صفحة التقارير في المحتوى الحالي
                if (mainWindow.MainContent?.Content is ReportsView reportsView)
                {
                    reportsView.RefreshData();
                    System.Diagnostics.Debug.WriteLine("تم تحديث صفحة التقارير");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث صفحة التقارير: {ex.Message}");
            }
        }

        // تحديث البيانات (للمزامنة الشبكية)
        public async void RefreshData()
        {
            try
            {
                await LoadPersons();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث بيانات الأشخاص: {ex.Message}");
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
