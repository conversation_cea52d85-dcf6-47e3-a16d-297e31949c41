using System;
using System.Windows;

namespace DebtManagementApp.Views
{
    /// <summary>
    /// نافذة تنبيه تحديث قاعدة البيانات من OneDrive
    /// </summary>
    public partial class OneDriveUpdateDialog : Window
    {
        public bool ShouldDownload { get; private set; } = false;
        public bool UserCancelled { get; private set; } = false;

        public OneDriveUpdateDialog(DateTime localModified, DateTime remoteModified)
        {
            InitializeComponent();
            SetVersionInfo(localModified, remoteModified);
        }

        /// <summary>
        /// تعيين معلومات النسخ
        /// </summary>
        private void SetVersionInfo(DateTime localModified, DateTime remoteModified)
        {
            try
            {
                // تنسيق التواريخ
                var localText = localModified == DateTime.MinValue 
                    ? "غير موجودة" 
                    : localModified.ToString("yyyy/MM/dd HH:mm:ss");
                
                var remoteText = remoteModified == DateTime.MinValue 
                    ? "غير موجودة" 
                    : remoteModified.ToString("yyyy/MM/dd HH:mm:ss");

                LocalVersionText.Text = localText;
                RemoteVersionText.Text = remoteText;

                // حساب الفرق الزمني
                if (localModified != DateTime.MinValue && remoteModified != DateTime.MinValue)
                {
                    var timeDiff = remoteModified - localModified;
                    var diffText = FormatTimeDifference(timeDiff);
                    TimeDifferenceText.Text = $"النسخة البعيدة أحدث بـ {diffText}";
                }
                else
                {
                    TimeDifferenceText.Text = "غير محدد";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض معلومات النسخ: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تنسيق الفرق الزمني
        /// </summary>
        private string FormatTimeDifference(TimeSpan timeDiff)
        {
            if (timeDiff.TotalMinutes < 1)
                return "أقل من دقيقة";
            else if (timeDiff.TotalHours < 1)
                return $"{(int)timeDiff.TotalMinutes} دقيقة";
            else if (timeDiff.TotalDays < 1)
                return $"{(int)timeDiff.TotalHours} ساعة و {timeDiff.Minutes} دقيقة";
            else
                return $"{(int)timeDiff.TotalDays} يوم و {timeDiff.Hours} ساعة";
        }

        /// <summary>
        /// معالج زر التأكيد
        /// </summary>
        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShouldDownload = DownloadRadio.IsChecked == true;
                UserCancelled = false;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج زر الإلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShouldDownload = false;
                UserCancelled = true;
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض النافذة والحصول على قرار المستخدم
        /// </summary>
        public static (bool shouldDownload, bool cancelled) ShowDialog(
            Window owner, DateTime localModified, DateTime remoteModified)
        {
            try
            {
                var dialog = new OneDriveUpdateDialog(localModified, remoteModified)
                {
                    Owner = owner
                };

                var result = dialog.ShowDialog();
                
                if (result == true)
                {
                    return (dialog.ShouldDownload, false);
                }
                else
                {
                    return (false, true);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض نافذة التحديث: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return (false, true);
            }
        }
    }
}
