using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using DebtManagementApp.Models;
using DebtManagementApp.Services;
using Microsoft.Win32;

namespace DebtManagementApp.Views
{
    public partial class WorkersManagementView : UserControl, INotifyPropertyChanged
    {
        // سيتم استخدام قائمة محلية للعمال بدلاً من قاعدة البيانات مؤقتاً
        private static List<Worker> _staticWorkers = new List<Worker>();
        private ObservableCollection<Worker> _allWorkers;
        private ObservableCollection<Worker> _filteredWorkers;
        private Worker? _selectedWorker;

        // متغيرات صرف الرواتب
        private ObservableCollection<SalaryPayment> _salaryPayments;
        private ObservableCollection<string> _workersWithSalaries;
        private ObservableCollection<Salary> _selectedWorkerSalaries;

        public ObservableCollection<Worker> FilteredWorkers
        {
            get => _filteredWorkers;
            set
            {
                _filteredWorkers = value;
                OnPropertyChanged(nameof(FilteredWorkers));
            }
        }

        public Worker? SelectedWorker
        {
            get => _selectedWorker;
            set
            {
                _selectedWorker = value;
                OnPropertyChanged(nameof(SelectedWorker));
                UpdateWorkerDetails();
            }
        }

        public WorkersManagementView()
        {
            try
            {
                InitializeComponent();
                _allWorkers = new ObservableCollection<Worker>();
                _filteredWorkers = new ObservableCollection<Worker>();
                _salaryPayments = new ObservableCollection<SalaryPayment>();
                _workersWithSalaries = new ObservableCollection<string>();
                _selectedWorkerSalaries = new ObservableCollection<Salary>();
                DataContext = this;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل واجهة إدارة العمال: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ في التحميل", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void WorkersManagementView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعيين مصدر البيانات بعد تحميل الـ XAML
                if (WorkersDataGrid != null)
                {
                    WorkersDataGrid.ItemsSource = FilteredWorkers;
                }

                if (PaySalariesDataGrid != null)
                {
                    PaySalariesDataGrid.ItemsSource = _salaryPayments;
                }

                if (WorkersWithSalariesListBox != null)
                {
                    WorkersWithSalariesListBox.ItemsSource = _workersWithSalaries;
                }

                if (WorkerSalariesDataGrid != null)
                {
                    WorkerSalariesDataGrid.ItemsSource = _selectedWorkerSalaries;
                }

                // التأكد من أن جميع العناصر محملة قبل تطبيق الفلاتر
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    LoadWorkers();
                    LoadSalaryPayments();
                    LoadWorkersWithSalaries();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات العمال: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadWorkers()
        {
            try
            {
                // تحميل العمال من القائمة المحلية
                _allWorkers.Clear();
                foreach (var worker in _staticWorkers)
                {
                    _allWorkers.Add(worker);
                }

                // إذا لم توجد بيانات، أضف بيانات تجريبية
                if (_allWorkers.Count == 0)
                {
                    LoadSampleWorkers();
                }

                ApplyFilters();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات العمال: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSampleWorkers()
        {
            var sampleWorkers = new List<Worker>
            {
                new Worker
                {
                    Id = 1, Name = "أحمد محمد علي", JobTitle = "عامل حديد",
                    Phone = "07701234567", DailyWage = 25000, WeeklyWage = 150000,
                    HireDate = DateTime.Now.AddMonths(-6), IsActive = true,
                    NationalId = "12345678901", Skills = "لحام، تقطيع، تشكيل الحديد",
                    ExperienceYears = 5, Address = "بغداد - الكرادة", IncludeFridayInWeeklyWage = false
                },
                new Worker
                {
                    Id = 2, Name = "فاطمة عبدالله", JobTitle = "مشرفة ورشة",
                    Phone = "07709876543", DailyWage = 35000, WeeklyWage = 245000,
                    HireDate = DateTime.Now.AddYears(-2), IsActive = true,
                    NationalId = "98765432109", Skills = "إدارة، تخطيط، مراقبة الجودة",
                    ExperienceYears = 8, Address = "بغداد - الجادرية", IncludeFridayInWeeklyWage = true
                },
                new Worker
                {
                    Id = 3, Name = "محمد سعد الغامدي", JobTitle = "عامل تقطيع",
                    Phone = "07705555555", DailyWage = 22000, WeeklyWage = 132000,
                    HireDate = DateTime.Now.AddMonths(-3), IsActive = true,
                    NationalId = "55555555555", Skills = "تقطيع بالليزر، تقطيع بالبلازما",
                    ExperienceYears = 3, Address = "بغداد - الدورة", IncludeFridayInWeeklyWage = false
                },
                new Worker
                {
                    Id = 4, Name = "علي حسن الزهراني", JobTitle = "عامل نقل",
                    Phone = "07702222222", DailyWage = 18000, WeeklyWage = 108000,
                    HireDate = DateTime.Now.AddMonths(-8), IsActive = false,
                    NationalId = "22222222222", Skills = "قيادة الرافعة، نقل المواد الثقيلة",
                    ExperienceYears = 4, Address = "بغداد - الشعلة", IncludeFridayInWeeklyWage = false
                },
                new Worker
                {
                    Id = 5, Name = "زينب أحمد الكعبي", JobTitle = "محاسبة",
                    Phone = "07708888888", DailyWage = 30000, WeeklyWage = 210000,
                    HireDate = DateTime.Now.AddYears(-1), IsActive = true,
                    NationalId = "88888888888", Skills = "محاسبة، إدارة مالية، تقارير",
                    ExperienceYears = 6, Address = "بغداد - الكاظمية", IncludeFridayInWeeklyWage = true
                }
            };

            foreach (var worker in sampleWorkers)
            {
                _allWorkers.Add(worker);
                _staticWorkers.Add(worker);
            }
        }

        private void ApplyFilters()
        {
            try
            {
                var filtered = _allWorkers.AsEnumerable();

                // فلتر البحث
                if (SearchTextBox != null && !string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchTerm = SearchTextBox.Text.ToLower();
                    filtered = filtered.Where(w =>
                        w.Name.ToLower().Contains(searchTerm) ||
                        w.JobTitle.ToLower().Contains(searchTerm) ||
                        w.Phone.Contains(searchTerm) ||
                        w.Skills.ToLower().Contains(searchTerm));
                }

                // فلتر الحالة
                if (StatusFilterComboBox != null)
                {
                    if (StatusFilterComboBox.SelectedIndex == 1) // العمال النشطين
                    {
                        filtered = filtered.Where(w => w.IsActive);
                    }
                    else if (StatusFilterComboBox.SelectedIndex == 2) // العمال غير النشطين
                    {
                        filtered = filtered.Where(w => !w.IsActive);
                    }
                }

                FilteredWorkers.Clear();
                foreach (var worker in filtered.OrderBy(w => w.Name))
                {
                    FilteredWorkers.Add(worker);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            // التأكد من أن عناصر الإحصائيات محملة
            if (TotalWorkersText == null || ActiveWorkersText == null || InactiveWorkersText == null)
                return;

            var totalWorkers = _allWorkers.Count;
            var activeWorkers = _allWorkers.Count(w => w.IsActive);
            var inactiveWorkers = totalWorkers - activeWorkers;

            TotalWorkersText.Text = $"إجمالي العمال: {totalWorkers}";
            ActiveWorkersText.Text = $"العمال النشطين: {activeWorkers}";
            InactiveWorkersText.Text = $"العمال غير النشطين: {inactiveWorkers}";
        }

        private void UpdateWorkerDetails()
        {
            // التأكد من أن العناصر محملة قبل الوصول إليها
            if (NoSelectionMessage == null || WorkerDetailsContent == null)
                return;

            if (SelectedWorker == null)
            {
                NoSelectionMessage.Visibility = Visibility.Visible;
                WorkerDetailsContent.Visibility = Visibility.Collapsed;
                return;
            }

            NoSelectionMessage.Visibility = Visibility.Collapsed;
            WorkerDetailsContent.Visibility = Visibility.Visible;

            var worker = SelectedWorker;

            // تحديث عنوان الفقاعة
            var workerNameTitle = WorkerDetailsContent?.FindName("WorkerNameTitle") as TextBlock;
            if (workerNameTitle != null) workerNameTitle.Text = worker.Name;

            // التأكد من أن جميع عناصر النص محملة وتحديثها بدون تكرار التسميات
            if (WorkerNameText != null) WorkerNameText.Text = worker.Name;
            if (WorkerJobTitleText != null) WorkerJobTitleText.Text = worker.JobTitle;
            if (WorkerPhoneText != null) WorkerPhoneText.Text = worker.Phone;
            if (WorkerNationalIdText != null) WorkerNationalIdText.Text = worker.NationalId;

            if (WorkerDailyWageText != null) WorkerDailyWageText.Text = worker.DailyWageFormatted;
            if (WorkerMonthlyWageText != null) WorkerMonthlyWageText.Text = worker.WeeklyWageFormatted;

            if (WorkerHireDateText != null) WorkerHireDateText.Text = worker.HireDateFormatted;
            if (WorkerWorkDurationText != null) WorkerWorkDurationText.Text = worker.WorkDuration;
            if (WorkerExperienceText != null) WorkerExperienceText.Text = $"{worker.ExperienceYears} سنة";
            if (WorkerLastWorkDateText != null) WorkerLastWorkDateText.Text = worker.LastWorkDateFormatted;
        }

        // معالجات الأحداث
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (IsLoaded)
            {
                ApplyFilters();
            }
        }

        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                ApplyFilters();
            }
        }

        private void WorkersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SelectedWorker = WorkersDataGrid.SelectedItem as Worker;
        }

        private void WorkersDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (SelectedWorker != null)
            {
                EditWorker();
            }
        }

        private void AddWorker_Click(object sender, RoutedEventArgs e)
        {
            ShowWorkerEditBubble(null); // null يعني إضافة عامل جديد
        }

        private void EditWorker_Click(object sender, RoutedEventArgs e)
        {
            EditWorker();
        }

        private void EditSelectedWorker_Click(object sender, RoutedEventArgs e)
        {
            EditWorker();
        }

        private void EditWorker()
        {
            if (SelectedWorker == null) return;
            ShowWorkerEditBubble(SelectedWorker); // تمرير العامل المحدد للتعديل
        }

        private void ViewWorkerDetails_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedWorker != null)
            {
                // التأكد من أن العامل محدد وعرض تفاصيله في الجانب الأيمن
                UpdateWorkerDetails();

                // إضافة تأثير الحركة عند الظهور
                if (WorkerDetailsContent?.Visibility == Visibility.Visible)
                {
                    AnimateDetailsAppearance();
                    WorkerDetailsContent.BringIntoView();
                }
            }
        }

        private void CloseWorkerDetails_Click(object sender, RoutedEventArgs e)
        {
            // إخفاء تفاصيل العامل مع تأثير حركة
            AnimateDetailsDisappearance();
        }

        private void AnimateDetailsAppearance()
        {
            if (WorkerDetailsContent == null) return;

            var scaleTransform = WorkerDetailsContent.FindName("DetailsScaleTransform") as ScaleTransform;
            if (scaleTransform != null)
            {
                // تأثير التكبير التدريجي
                var scaleAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0.8,
                    To = 1.0,
                    Duration = TimeSpan.FromMilliseconds(300),
                    EasingFunction = new System.Windows.Media.Animation.BackEase { Amplitude = 0.3 }
                };

                scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);

                // تأثير الشفافية
                var opacityAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0.0,
                    To = 1.0,
                    Duration = TimeSpan.FromMilliseconds(250)
                };

                WorkerDetailsContent.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
        }

        private void AnimateDetailsDisappearance()
        {
            if (WorkerDetailsContent == null) return;

            var scaleTransform = WorkerDetailsContent.FindName("DetailsScaleTransform") as ScaleTransform;
            if (scaleTransform != null)
            {
                // تأثير التصغير التدريجي
                var scaleAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1.0,
                    To = 0.8,
                    Duration = TimeSpan.FromMilliseconds(200)
                };

                // تأثير الشفافية
                var opacityAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1.0,
                    To = 0.0,
                    Duration = TimeSpan.FromMilliseconds(200)
                };

                opacityAnimation.Completed += (s, e) =>
                {
                    WorkerDetailsContent.Visibility = Visibility.Collapsed;
                    NoSelectionMessage.Visibility = Visibility.Visible;
                    SelectedWorker = null;
                };

                scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);
                WorkerDetailsContent.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
        }

        private Worker _editingWorker = null; // العامل الذي يتم تعديله حالياً
        private bool _isUpdatingWage = false; // لمنع التداخل في تحديث الراتب

        private void ShowWorkerEditBubble(Worker workerToEdit)
        {
            _editingWorker = workerToEdit;

            // تحديث عنوان الفقاعة والأيقونة
            var titleElement = WorkerEditBubble?.FindName("EditBubbleTitle") as TextBlock;
            var iconElement = WorkerEditBubble?.FindName("EditBubbleIcon") as TextBlock;

            if (workerToEdit == null)
            {
                // وضع الإضافة
                if (titleElement != null) titleElement.Text = "إضافة عامل جديد";
                if (iconElement != null) iconElement.Text = "➕";
                ClearWorkerEditBubbleFields();
            }
            else
            {
                // وضع التعديل
                if (titleElement != null) titleElement.Text = $"تعديل العامل: {workerToEdit.Name}";
                if (iconElement != null) iconElement.Text = "✏️";
                FillWorkerEditBubbleFields(workerToEdit);
            }

            // إظهار الفقاعة مع تأثير الحركة
            WorkerEditBubble.Visibility = Visibility.Visible;
            AnimateEditBubbleAppearance();
            WorkerEditBubble.BringIntoView();
        }

        private void CloseWorkerEditBubble_Click(object sender, RoutedEventArgs e)
        {
            AnimateEditBubbleDisappearance();
        }

        private void ClearWorkerEditBubbleFields()
        {
            if (BubbleNameTextBox != null) BubbleNameTextBox.Text = "";
            if (BubbleJobTitleComboBox != null) BubbleJobTitleComboBox.Text = "";
            if (BubblePhoneTextBox != null) BubblePhoneTextBox.Text = "";
            if (BubbleAddressTextBox != null) BubbleAddressTextBox.Text = "";
            if (BubbleDailyWageTextBox != null) BubbleDailyWageTextBox.Text = "";
            if (BubbleWeeklyWageTextBox != null) BubbleWeeklyWageTextBox.Text = "";
            if (BubbleHireDatePicker != null) BubbleHireDatePicker.SelectedDate = DateTime.Now;
            if (BubbleExperienceYearsTextBox != null) BubbleExperienceYearsTextBox.Text = "0";
            if (BubbleIsActiveCheckBox != null) BubbleIsActiveCheckBox.IsChecked = true;
        }

        private void FillWorkerEditBubbleFields(Worker worker)
        {
            if (BubbleNameTextBox != null) BubbleNameTextBox.Text = worker.Name;
            if (BubbleJobTitleComboBox != null) BubbleJobTitleComboBox.Text = worker.JobTitle;
            if (BubblePhoneTextBox != null) BubblePhoneTextBox.Text = worker.Phone;
            if (BubbleAddressTextBox != null) BubbleAddressTextBox.Text = worker.Address;
            if (BubbleDailyWageTextBox != null) BubbleDailyWageTextBox.Text = worker.DailyWage.ToString();
            if (BubbleWeeklyWageTextBox != null) BubbleWeeklyWageTextBox.Text = worker.WeeklyWage.ToString();
            if (BubbleHireDatePicker != null) BubbleHireDatePicker.SelectedDate = worker.HireDate;
            if (BubbleExperienceYearsTextBox != null) BubbleExperienceYearsTextBox.Text = worker.ExperienceYears.ToString();
            if (BubbleIsActiveCheckBox != null) BubbleIsActiveCheckBox.IsChecked = worker.IsActive;
        }

        private void AnimateEditBubbleAppearance()
        {
            if (WorkerEditBubble == null) return;

            var scaleTransform = WorkerEditBubble.FindName("EditBubbleScaleTransform") as ScaleTransform;
            if (scaleTransform != null)
            {
                // تأثير التكبير التدريجي
                var scaleAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0.8,
                    To = 1.0,
                    Duration = TimeSpan.FromMilliseconds(300),
                    EasingFunction = new System.Windows.Media.Animation.BackEase { Amplitude = 0.3 }
                };

                scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);

                // تأثير الشفافية
                var opacityAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0.0,
                    To = 1.0,
                    Duration = TimeSpan.FromMilliseconds(250)
                };

                WorkerEditBubble.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
        }

        private void AnimateEditBubbleDisappearance()
        {
            if (WorkerEditBubble == null) return;

            var scaleTransform = WorkerEditBubble.FindName("EditBubbleScaleTransform") as ScaleTransform;
            if (scaleTransform != null)
            {
                // تأثير التصغير التدريجي
                var scaleAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1.0,
                    To = 0.8,
                    Duration = TimeSpan.FromMilliseconds(200)
                };

                // تأثير الشفافية
                var opacityAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1.0,
                    To = 0.0,
                    Duration = TimeSpan.FromMilliseconds(200)
                };

                opacityAnimation.Completed += (s, e) =>
                {
                    WorkerEditBubble.Visibility = Visibility.Collapsed;
                    _editingWorker = null;
                };

                scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);
                WorkerEditBubble.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
        }

        private void ActivateWorker_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedWorker != null)
            {
                SelectedWorker.IsActive = true;
                ApplyFilters();
                UpdateStatistics();
            }
        }

        private void DeactivateWorker_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedWorker != null)
            {
                SelectedWorker.IsActive = false;
                ApplyFilters();
                UpdateStatistics();
            }
        }

        private void DeleteWorker_Click(object sender, RoutedEventArgs e)
        {
            DeleteSelectedWorker();
        }

        private void DeleteSelectedWorker_Click(object sender, RoutedEventArgs e)
        {
            DeleteSelectedWorker();
        }

        private void DeleteSelectedWorker()
        {
            if (SelectedWorker == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف العامل '{SelectedWorker.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                var workerName = SelectedWorker.Name; // حفظ الاسم قبل الحذف

                // حذف العامل من القائمة المحلية
                var workerToRemove = _staticWorkers.FirstOrDefault(w => w.Id == SelectedWorker.Id);
                if (workerToRemove != null)
                {
                    _staticWorkers.Remove(workerToRemove);
                }

                _allWorkers.Remove(SelectedWorker);
                ApplyFilters();
                UpdateStatistics();
                SelectedWorker = null;

                // تسجيل النشاط
                ActivityService.LogWorkerDeleted(workerName);
            }
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            LoadWorkers();
            LoadSalaryPayments();
            LoadWorkersWithSalaries();
        }

        #region Salary Management Methods

        private void PaySalaries_Click(object sender, RoutedEventArgs e)
        {
            MainTabControl.SelectedItem = PaySalariesTab;
            LoadSalaryPayments();
        }

        private void ViewPaidSalaries_Click(object sender, RoutedEventArgs e)
        {
            MainTabControl.SelectedItem = PaidSalariesTab;
            LoadWorkersWithSalaries();
        }

        private void LoadSalaryPayments()
        {
            _salaryPayments.Clear();

            foreach (var worker in _staticWorkers.Where(w => w.IsActive))
            {
                var salaryPayment = new SalaryPayment
                {
                    WorkerId = worker.Id,
                    Name = worker.Name,
                    DailyWage = worker.DailyWage,
                    WorkDays = 6, // افتراضي 6 أيام
                    AbsenceDays = 0,
                    OvertimeHours = 0,
                    OvertimeRate = worker.DailyWage / 8, // افتراضي: الأجر اليومي ÷ 8 ساعات
                    Deductions = 0,
                    Bonuses = 0
                };

                _salaryPayments.Add(salaryPayment);
            }
        }

        private void LoadWorkersWithSalaries()
        {
            try
            {
                _workersWithSalaries.Clear();
                var workers = DatabaseHelper.GetWorkersWithSalaries();

                foreach (var worker in workers)
                {
                    _workersWithSalaries.Add(worker);
                }

                // إذا لم توجد رواتب مصروفة، أضف رسالة توضيحية
                if (workers.Count == 0)
                {
                    SelectedWorkerSalariesTitle.Text = "📄 لا توجد رواتب مصروفة بعد";
                }
                else
                {
                    SelectedWorkerSalariesTitle.Text = "اختر عاملاً لعرض سجل رواتبه";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة العمال: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PayWorkerSalary_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is SalaryPayment salaryPayment)
            {
                try
                {
                    var paymentWindow = new SalaryPaymentWindow(salaryPayment, false);
                    paymentWindow.Owner = Window.GetWindow(this);

                    if (paymentWindow.ShowDialog() == true && paymentWindow.IsConfirmed)
                    {
                        var salary = salaryPayment.ToSalary(paymentWindow.FromDate, paymentWindow.ToDate, paymentWindow.Notes);
                        DatabaseHelper.AddSalary(salary);

                        MessageBox.Show($"تم صرف راتب {salaryPayment.Name} بمبلغ {salaryPayment.TotalAmountFormatted} بنجاح!\nالفترة: {paymentWindow.FromDate:yyyy/MM/dd} - {paymentWindow.ToDate:yyyy/MM/dd}",
                            "تم صرف الراتب", MessageBoxButton.OK, MessageBoxImage.Information);

                        // تسجيل النشاط
                        ActivityService.LogSalaryPaid(salaryPayment.Name, salaryPayment.TotalAmount);

                        // إعادة تحميل قائمة العمال الذين تم صرف رواتب لهم
                        LoadWorkersWithSalaries();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في صرف الراتب: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void PayAllSalaries_Click(object sender, RoutedEventArgs e)
        {
            if (_salaryPayments.Count == 0)
            {
                MessageBox.Show("لا توجد رواتب للصرف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // استخدام أول عامل كمرجع للنافذة (للصرف الجماعي)
                var firstPayment = _salaryPayments[0];
                var paymentWindow = new SalaryPaymentWindow(firstPayment, true);
                paymentWindow.Owner = Window.GetWindow(this);

                if (paymentWindow.ShowDialog() == true && paymentWindow.IsConfirmed)
                {
                    int paidCount = 0;
                    decimal totalAmount = 0;

                    foreach (var salaryPayment in _salaryPayments)
                    {
                        if (salaryPayment.TotalAmount > 0)
                        {
                            var salary = salaryPayment.ToSalary(paymentWindow.FromDate, paymentWindow.ToDate, paymentWindow.Notes);
                            DatabaseHelper.AddSalary(salary);

                            // تسجيل النشاط لكل راتب
                            ActivityService.LogSalaryPaid(salaryPayment.Name, salaryPayment.TotalAmount);

                            paidCount++;
                            totalAmount += salaryPayment.TotalAmount;
                        }
                    }

                    MessageBox.Show($"تم صرف {paidCount} راتب بإجمالي {totalAmount:N0} دينار بنجاح!\nالفترة: {paymentWindow.FromDate:yyyy/MM/dd} - {paymentWindow.ToDate:yyyy/MM/dd}",
                        "تم صرف الرواتب", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إعادة تحميل البيانات
                    LoadSalaryPayments();
                    LoadWorkersWithSalaries();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في صرف الرواتب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void WorkersWithSalariesListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ViewWorkerSalariesButton.IsEnabled = WorkersWithSalariesListBox.SelectedItem != null;
        }

        private void ViewWorkerSalariesButton_Click(object sender, RoutedEventArgs e)
        {
            if (WorkersWithSalariesListBox.SelectedItem is string selectedWorkerName)
            {
                LoadWorkerSalaries(selectedWorkerName);
            }
        }

        private void WorkersWithSalariesListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (WorkersWithSalariesListBox.SelectedItem is string selectedWorkerName)
            {
                LoadWorkerSalaries(selectedWorkerName);
            }
        }

        private void LoadWorkerSalaries(string workerName)
        {
            try
            {
                _selectedWorkerSalaries.Clear();

                // البحث عن العامل بالاسم في قاعدة البيانات أولاً
                var worker = _staticWorkers.FirstOrDefault(w => w.Name == workerName);
                if (worker != null)
                {
                    var salaries = DatabaseHelper.GetSalariesByWorkerId(worker.Id);

                    foreach (var salary in salaries)
                    {
                        _selectedWorkerSalaries.Add(salary);
                    }

                    SelectedWorkerSalariesTitle.Text = $"📄 سجل رواتب {workerName} ({salaries.Count} راتب)";

                    if (salaries.Count == 0)
                    {
                        SelectedWorkerSalariesTitle.Text = $"📄 لا توجد رواتب مصروفة للعامل {workerName}";
                    }
                }
                else
                {
                    // البحث المباشر بالاسم في جدول الرواتب
                    var allSalaries = DatabaseHelper.GetAllSalaries();
                    var workerSalaries = allSalaries.Where(s => s.WorkerName == workerName).ToList();

                    foreach (var salary in workerSalaries)
                    {
                        _selectedWorkerSalaries.Add(salary);
                    }

                    SelectedWorkerSalariesTitle.Text = $"📄 سجل رواتب {workerName} ({workerSalaries.Count} راتب)";

                    if (workerSalaries.Count == 0)
                    {
                        SelectedWorkerSalariesTitle.Text = $"📄 لا توجد رواتب مصروفة للعامل {workerName}";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل رواتب العامل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                SelectedWorkerSalariesTitle.Text = "خطأ في تحميل البيانات";
            }
        }

        private void ExportSalaries_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    Title = "تصدير سجل الرواتب",
                    FileName = $"سجل_الرواتب_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // هنا يمكن إضافة منطق تصدير Excel
                    MessageBox.Show("سيتم إضافة ميزة التصدير قريباً", "قيد التطوير",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportWorkerSalariesToPdf_Click(object sender, RoutedEventArgs e)
        {
            if (WorkersWithSalariesListBox.SelectedItem is string selectedWorkerName)
            {
                try
                {
                    var saveFileDialog = new SaveFileDialog
                    {
                        Filter = "PDF Files|*.pdf",
                        Title = "تصدير سجل راتب العامل إلى PDF",
                        FileName = $"سجل_راتب_{selectedWorkerName}_{DateTime.Now:yyyy-MM-dd}.pdf"
                    };

                    if (saveFileDialog.ShowDialog() == true)
                    {
                        // هنا يمكن إضافة منطق تصدير PDF
                        MessageBox.Show("سيتم إضافة ميزة التصدير قريباً", "قيد التطوير",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار عامل أولاً", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ExportWorkerSalariesToExcel_Click(object sender, RoutedEventArgs e)
        {
            if (WorkersWithSalariesListBox.SelectedItem is string selectedWorkerName)
            {
                try
                {
                    var saveFileDialog = new SaveFileDialog
                    {
                        Filter = "Excel Files|*.xlsx",
                        Title = "تصدير سجل راتب العامل إلى Excel",
                        FileName = $"سجل_راتب_{selectedWorkerName}_{DateTime.Now:yyyy-MM-dd}.xlsx"
                    };

                    if (saveFileDialog.ShowDialog() == true)
                    {
                        // هنا يمكن إضافة منطق تصدير Excel
                        MessageBox.Show("سيتم إضافة ميزة التصدير قريباً", "قيد التطوير",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار عامل أولاً", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        #endregion

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void SaveWorkerFromBubble_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(BubbleNameTextBox?.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العامل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(BubbleJobTitleComboBox?.Text))
                {
                    MessageBox.Show("يرجى إدخال منصب العامل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء أو تحديث العامل
                Worker worker;
                bool isNewWorker = _editingWorker == null;

                if (isNewWorker)
                {
                    worker = new Worker();
                    worker.Id = _staticWorkers.Count > 0 ? _staticWorkers.Max(w => w.Id) + 1 : 1;
                }
                else
                {
                    worker = _editingWorker;
                }

                // تحديث البيانات
                worker.Name = BubbleNameTextBox.Text.Trim();
                worker.JobTitle = BubbleJobTitleComboBox.Text.Trim();
                worker.Phone = BubblePhoneTextBox?.Text?.Trim() ?? "";
                worker.NationalId = ""; // حقل محذوف
                worker.Address = BubbleAddressTextBox?.Text?.Trim() ?? "";
                worker.EmergencyContact = ""; // حقل محذوف
                worker.HireDate = BubbleHireDatePicker?.SelectedDate ?? DateTime.Now;
                worker.IsActive = BubbleIsActiveCheckBox?.IsChecked ?? true;

                // تحديث الراتب
                if (decimal.TryParse(BubbleDailyWageTextBox?.Text, out decimal dailyWage))
                    worker.DailyWage = dailyWage;
                if (decimal.TryParse(BubbleWeeklyWageTextBox?.Text, out decimal weeklyWage))
                    worker.WeeklyWage = weeklyWage;
                if (int.TryParse(BubbleExperienceYearsTextBox?.Text, out int experienceYears))
                    worker.ExperienceYears = experienceYears;

                // إضافة أو تحديث في القوائم
                if (isNewWorker)
                {
                    _staticWorkers.Add(worker);
                    _allWorkers.Add(worker);
                    ActivityService.LogWorkerAdded(worker.Name);
                }
                else
                {
                    // تحديث العامل في القائمة المحلية
                    var workerInList = _staticWorkers.FirstOrDefault(w => w.Id == worker.Id);
                    if (workerInList != null)
                    {
                        var index = _staticWorkers.IndexOf(workerInList);
                        _staticWorkers[index] = worker;
                    }
                    ActivityService.LogWorkerUpdated(worker.Name);
                }

                // تحديث الواجهة
                ApplyFilters();
                UpdateStatistics();
                UpdateWorkerDetails();

                // إغلاق الفقاعة
                AnimateEditBubbleDisappearance();

                // رسالة نجاح
                string message = isNewWorker ? "تم إضافة العامل بنجاح" : "تم تحديث بيانات العامل بنجاح";
                MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BubbleDailyWage_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingWage) return; // منع التداخل

            if (BubbleDailyWageTextBox != null && BubbleWeeklyWageTextBox != null)
            {
                if (decimal.TryParse(BubbleDailyWageTextBox.Text, out decimal dailyWage))
                {
                    _isUpdatingWage = true;
                    // حساب الراتب الأسبوعي (6 أيام عمل)
                    decimal weeklyWage = dailyWage * 6;
                    BubbleWeeklyWageTextBox.Text = weeklyWage.ToString("F0");
                    _isUpdatingWage = false;
                }
            }
        }

        private void BubbleWeeklyWage_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingWage) return; // منع التداخل

            if (BubbleWeeklyWageTextBox != null && BubbleDailyWageTextBox != null)
            {
                if (decimal.TryParse(BubbleWeeklyWageTextBox.Text, out decimal weeklyWage))
                {
                    _isUpdatingWage = true;
                    // حساب الراتب اليومي
                    decimal dailyWage = weeklyWage / 6;
                    BubbleDailyWageTextBox.Text = dailyWage.ToString("F0");
                    _isUpdatingWage = false;
                }
            }
        }

        // دالة تحسين التمرير بالماوس مع السلاسة
        private void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (sender is ScrollViewer scrollViewer)
            {
                // تحسين سرعة التمرير مع السلاسة
                double scrollSpeed = 2.0; // سرعة معتدلة للسلاسة
                double targetOffset = scrollViewer.VerticalOffset - (e.Delta * scrollSpeed);

                // التأكد من أن القيمة ضمن الحدود المسموحة
                targetOffset = Math.Max(0, Math.Min(scrollViewer.ScrollableHeight, targetOffset));

                // تطبيق التمرير السلس
                SmoothScrollToOffset(scrollViewer, targetOffset);

                // منع التمرير الافتراضي
                e.Handled = true;
            }
        }

        // دالة التمرير السلس
        private void SmoothScrollToOffset(ScrollViewer scrollViewer, double targetOffset)
        {
            double startOffset = scrollViewer.VerticalOffset;
            double distance = targetOffset - startOffset;

            if (Math.Abs(distance) < 1) return; // إذا كانت المسافة صغيرة جداً، لا حاجة للحركة

            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromMilliseconds(16); // 60 FPS

            DateTime startTime = DateTime.Now;
            double duration = 150; // مدة الحركة بالميلي ثانية

            timer.Tick += (s, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                // استخدام دالة Ease Out للسلاسة
                double easedProgress = 1 - Math.Pow(1 - progress, 3);

                double currentOffset = startOffset + (distance * easedProgress);
                scrollViewer.ScrollToVerticalOffset(currentOffset);

                if (progress >= 1.0)
                {
                    timer.Stop();
                }
            };

            timer.Start();
        }
    }
}
