﻿#pragma checksum "..\..\..\..\Views\WorkersManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "22FC80FEDB6406AC1DF8EBDF6BA615E00A5F9E07"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// WorkersManagementView
    /// </summary>
    public partial class WorkersManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 23 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PaySalariesButton;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewPaidSalariesButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl MainTabControl;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem WorkersManagementTab;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid WorkersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalWorkersText;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveWorkersText;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InactiveWorkersText;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WorkerDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoSelectionMessage;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border WorkerDetailsContent;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ScaleTransform DetailsScaleTransform;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerNameTitle;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerNameText;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerJobTitleText;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerPhoneText;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerNationalIdText;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerDailyWageText;
        
        #line default
        #line hidden
        
        
        #line 436 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerMonthlyWageText;
        
        #line default
        #line hidden
        
        
        #line 472 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerHireDateText;
        
        #line default
        #line hidden
        
        
        #line 478 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerWorkDurationText;
        
        #line default
        #line hidden
        
        
        #line 484 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerExperienceText;
        
        #line default
        #line hidden
        
        
        #line 490 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerLastWorkDateText;
        
        #line default
        #line hidden
        
        
        #line 525 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem PaySalariesTab;
        
        #line default
        #line hidden
        
        
        #line 566 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaySalariesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 797 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem PaidSalariesTab;
        
        #line default
        #line hidden
        
        
        #line 855 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox WorkersWithSalariesListBox;
        
        #line default
        #line hidden
        
        
        #line 895 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewWorkerSalariesButton;
        
        #line default
        #line hidden
        
        
        #line 931 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedWorkerSalariesTitle;
        
        #line default
        #line hidden
        
        
        #line 936 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid WorkerSalariesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/workersmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\WorkersManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 4 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((DebtManagementApp.Views.WorkersManagementView)(target)).Loaded += new System.Windows.RoutedEventHandler(this.WorkersManagementView_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 26 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 32 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 45 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddWorker_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PaySalariesButton = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.PaySalariesButton.Click += new System.Windows.RoutedEventHandler(this.PaySalaries_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ViewPaidSalariesButton = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.ViewPaidSalariesButton.Click += new System.Windows.RoutedEventHandler(this.ViewPaidSalaries_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 63 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.MainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 9:
            this.WorkersManagementTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 10:
            this.WorkersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 131 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.WorkersDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WorkersDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 132 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.WorkersDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.WorkersDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 177 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.EditWorker_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 178 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewWorkerDetails_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 180 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ActivateWorker_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 181 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeactivateWorker_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 183 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteWorker_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TotalWorkersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ActiveWorkersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.InactiveWorkersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.WorkerDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 20:
            this.NoSelectionMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.WorkerDetailsContent = ((System.Windows.Controls.Border)(target));
            return;
            case 22:
            this.DetailsScaleTransform = ((System.Windows.Media.ScaleTransform)(target));
            return;
            case 23:
            this.WorkerNameTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.WorkerNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.WorkerJobTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.WorkerPhoneText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.WorkerNationalIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.WorkerDailyWageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.WorkerMonthlyWageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.WorkerHireDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.WorkerWorkDurationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.WorkerExperienceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.WorkerLastWorkDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            
            #line 502 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditSelectedWorker_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            
            #line 508 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteSelectedWorker_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            
            #line 513 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseWorkerDetails_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.PaySalariesTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 38:
            
            #line 542 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PayAllSalaries_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.PaySalariesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 41:
            this.PaidSalariesTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 42:
            
            #line 813 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportSalaries_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.WorkersWithSalariesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 859 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.WorkersWithSalariesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WorkersWithSalariesListBox_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 860 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.WorkersWithSalariesListBox.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.WorkersWithSalariesListBox_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 44:
            this.ViewWorkerSalariesButton = ((System.Windows.Controls.Button)(target));
            
            #line 899 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.ViewWorkerSalariesButton.Click += new System.Windows.RoutedEventHandler(this.ViewWorkerSalariesButton_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.SelectedWorkerSalariesTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 46:
            this.WorkerSalariesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 47:
            
            #line 1108 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportWorkerSalariesToPdf_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            
            #line 1132 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportWorkerSalariesToExcel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 40:
            
            #line 766 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PayWorkerSalary_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

