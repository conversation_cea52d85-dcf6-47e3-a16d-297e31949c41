﻿#pragma checksum "..\..\..\..\Views\WorkersManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "67F38EA0F2C4895856650B33A293C5F6E0B4EBC8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// WorkersManagementView
    /// </summary>
    public partial class WorkersManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 23 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PaySalariesButton;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewPaidSalariesButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl MainTabControl;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem WorkersManagementTab;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid WorkersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalWorkersText;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveWorkersText;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InactiveWorkersText;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WorkerDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoSelectionMessage;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border WorkerDetailsContent;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ScaleTransform DetailsScaleTransform;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerNameTitle;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerNameText;
        
        #line default
        #line hidden
        
        
        #line 387 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerJobTitleText;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerPhoneText;
        
        #line default
        #line hidden
        
        
        #line 399 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerNationalIdText;
        
        #line default
        #line hidden
        
        
        #line 433 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerDailyWageText;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerMonthlyWageText;
        
        #line default
        #line hidden
        
        
        #line 475 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerHireDateText;
        
        #line default
        #line hidden
        
        
        #line 481 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerWorkDurationText;
        
        #line default
        #line hidden
        
        
        #line 487 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerExperienceText;
        
        #line default
        #line hidden
        
        
        #line 493 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerLastWorkDateText;
        
        #line default
        #line hidden
        
        
        #line 526 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border WorkerEditBubble;
        
        #line default
        #line hidden
        
        
        #line 543 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ScaleTransform EditBubbleScaleTransform;
        
        #line default
        #line hidden
        
        
        #line 555 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EditBubbleIcon;
        
        #line default
        #line hidden
        
        
        #line 557 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EditBubbleTitle;
        
        #line default
        #line hidden
        
        
        #line 609 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BubbleNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 619 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BubbleJobTitleComboBox;
        
        #line default
        #line hidden
        
        
        #line 638 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BubblePhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 648 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BubbleAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 685 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BubbleDailyWageTextBox;
        
        #line default
        #line hidden
        
        
        #line 696 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BubbleWeeklyWageTextBox;
        
        #line default
        #line hidden
        
        
        #line 735 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker BubbleHireDatePicker;
        
        #line default
        #line hidden
        
        
        #line 745 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BubbleExperienceYearsTextBox;
        
        #line default
        #line hidden
        
        
        #line 753 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BubbleIsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 765 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BubbleSaveButton;
        
        #line default
        #line hidden
        
        
        #line 784 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem PaySalariesTab;
        
        #line default
        #line hidden
        
        
        #line 827 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaySalariesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 1058 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem PaidSalariesTab;
        
        #line default
        #line hidden
        
        
        #line 1116 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox WorkersWithSalariesListBox;
        
        #line default
        #line hidden
        
        
        #line 1156 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewWorkerSalariesButton;
        
        #line default
        #line hidden
        
        
        #line 1192 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedWorkerSalariesTitle;
        
        #line default
        #line hidden
        
        
        #line 1197 "..\..\..\..\Views\WorkersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid WorkerSalariesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/workersmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\WorkersManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 4 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((DebtManagementApp.Views.WorkersManagementView)(target)).Loaded += new System.Windows.RoutedEventHandler(this.WorkersManagementView_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 26 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 32 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 45 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddWorker_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PaySalariesButton = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.PaySalariesButton.Click += new System.Windows.RoutedEventHandler(this.PaySalaries_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ViewPaidSalariesButton = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.ViewPaidSalariesButton.Click += new System.Windows.RoutedEventHandler(this.ViewPaidSalaries_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 63 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.MainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 9:
            this.WorkersManagementTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 10:
            this.WorkersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 131 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.WorkersDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WorkersDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 132 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.WorkersDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.WorkersDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 177 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.EditWorker_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 178 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewWorkerDetails_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 180 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ActivateWorker_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 181 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeactivateWorker_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 183 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteWorker_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TotalWorkersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ActiveWorkersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.InactiveWorkersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            
            #line 294 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.ScrollViewer)(target)).PreviewMouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.ScrollViewer_PreviewMouseWheel);
            
            #line default
            #line hidden
            return;
            case 20:
            this.WorkerDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 21:
            this.NoSelectionMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.WorkerDetailsContent = ((System.Windows.Controls.Border)(target));
            return;
            case 23:
            this.DetailsScaleTransform = ((System.Windows.Media.ScaleTransform)(target));
            return;
            case 24:
            this.WorkerNameTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.WorkerNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.WorkerJobTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.WorkerPhoneText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.WorkerNationalIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.WorkerDailyWageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.WorkerMonthlyWageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.WorkerHireDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.WorkerWorkDurationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.WorkerExperienceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.WorkerLastWorkDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            
            #line 505 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditSelectedWorker_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            
            #line 511 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteSelectedWorker_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            
            #line 516 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseWorkerDetails_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.WorkerEditBubble = ((System.Windows.Controls.Border)(target));
            return;
            case 39:
            this.EditBubbleScaleTransform = ((System.Windows.Media.ScaleTransform)(target));
            return;
            case 40:
            
            #line 548 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.ScrollViewer)(target)).PreviewMouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.ScrollViewer_PreviewMouseWheel);
            
            #line default
            #line hidden
            return;
            case 41:
            this.EditBubbleIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.EditBubbleTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            
            #line 574 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseWorkerEditBubble_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.BubbleNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 45:
            this.BubbleJobTitleComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 46:
            this.BubblePhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 47:
            this.BubbleAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 48:
            this.BubbleDailyWageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 689 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.BubbleDailyWageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.BubbleDailyWage_TextChanged);
            
            #line default
            #line hidden
            return;
            case 49:
            this.BubbleWeeklyWageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 700 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.BubbleWeeklyWageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.BubbleWeeklyWage_TextChanged);
            
            #line default
            #line hidden
            return;
            case 50:
            this.BubbleHireDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 51:
            this.BubbleExperienceYearsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 52:
            this.BubbleIsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 53:
            this.BubbleSaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 769 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.BubbleSaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveWorkerFromBubble_Click);
            
            #line default
            #line hidden
            return;
            case 54:
            
            #line 774 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseWorkerEditBubble_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            this.PaySalariesTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 56:
            
            #line 801 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PayAllSalaries_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            
            #line 826 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.ScrollViewer)(target)).PreviewMouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.ScrollViewer_PreviewMouseWheel);
            
            #line default
            #line hidden
            return;
            case 58:
            this.PaySalariesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 60:
            this.PaidSalariesTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 61:
            
            #line 1074 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportSalaries_Click);
            
            #line default
            #line hidden
            return;
            case 62:
            this.WorkersWithSalariesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 1120 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.WorkersWithSalariesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WorkersWithSalariesListBox_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 1121 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.WorkersWithSalariesListBox.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.WorkersWithSalariesListBox_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 63:
            this.ViewWorkerSalariesButton = ((System.Windows.Controls.Button)(target));
            
            #line 1160 "..\..\..\..\Views\WorkersManagementView.xaml"
            this.ViewWorkerSalariesButton.Click += new System.Windows.RoutedEventHandler(this.ViewWorkerSalariesButton_Click);
            
            #line default
            #line hidden
            return;
            case 64:
            this.SelectedWorkerSalariesTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 65:
            this.WorkerSalariesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 66:
            
            #line 1369 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportWorkerSalariesToPdf_Click);
            
            #line default
            #line hidden
            return;
            case 67:
            
            #line 1393 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportWorkerSalariesToExcel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 59:
            
            #line 1027 "..\..\..\..\Views\WorkersManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PayWorkerSalary_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

