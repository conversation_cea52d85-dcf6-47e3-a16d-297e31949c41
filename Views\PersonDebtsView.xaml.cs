using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Documents;
using System.Text;
using System.IO;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;
using DebtManagementApp.Services;

namespace DebtManagementApp.Views
{
    public partial class PersonDebtsView : UserControl, INotifyPropertyChanged
    {
        private readonly ObservableCollection<Debt> _personDebts;
        private Person _selectedPerson;
        private Debt _selectedDebt;
        private Action _goBackAction;

        public PersonDebtsView()
        {
            InitializeComponent();
            _personDebts = new ObservableCollection<Debt>();
            DataContext = this;

            // تسجيل أحداث حفظ إعدادات الأعمدة
            Loaded += (s, e) => ColumnSettingsHelper.RegisterColumnEvents(PersonDebtsDataGrid, "PersonDebtsGrid");
        }

        public ObservableCollection<Debt> PersonDebts
        {
            get => _personDebts;
        }

        public Person SelectedPerson
        {
            get => _selectedPerson;
            set
            {
                _selectedPerson = value;
                OnPropertyChanged(nameof(SelectedPerson));
                LoadPersonDebts();
            }
        }

        public Debt SelectedDebt
        {
            get => _selectedDebt;
            set
            {
                _selectedDebt = value;
                OnPropertyChanged(nameof(SelectedDebt));
            }
        }

        public Action GoBackAction
        {
            get => _goBackAction;
            set => _goBackAction = value;
        }

        public void SetPerson(Person person, Action goBackAction = null)
        {
            SelectedPerson = person;
            GoBackAction = goBackAction;
            LoadPersonDebts(); // تحميل الديون فوراً
        }

        private void LoadPersonDebts()
        {
            if (SelectedPerson == null) return;

            try
            {
                // تحديث معلومات الشخص
                PersonNameTitle.Text = $"💰 ديون {SelectedPerson.Name}";
                PersonInfoText.Text = $"الهاتف: {SelectedPerson.Phone ?? "غير محدد"} | العنوان: {SelectedPerson.Address ?? "غير محدد"}";

                // تحميل الديون من قاعدة البيانات
                _personDebts.Clear();

                try
                {
                    var allDebts = DatabaseHelper.GetAllDebts();
                    var personDebts = allDebts.Where(d => d.PersonId == SelectedPerson.Id).ToList();

                    foreach (var debt in personDebts)
                    {
                        _personDebts.Add(debt);
                    }

                    System.Diagnostics.Debug.WriteLine($"تم تحميل {personDebts.Count} دين للشخص {SelectedPerson.Name}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الديون: {ex.Message}");
                    MessageBox.Show($"خطأ في تحميل الديون من قاعدة البيانات: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل ديون الشخص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private System.Collections.Generic.List<Debt> GetSampleDebtsForPerson(int personId)
        {
            var allDebts = new System.Collections.Generic.List<Debt>
            {
                new Debt { Id = 1, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 750000, Date = DateTime.Now.AddDays(-30), DueDate = DateTime.Now.AddDays(-5), Description = "حديد تسليح 16 ملم - 5 طن", IsSettled = false },
                new Debt { Id = 2, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 450000, Date = DateTime.Now.AddDays(-20), DueDate = DateTime.Now.AddDays(10), Description = "قطع وثني حديد للأعمدة", IsSettled = true, PaymentDate = DateTime.Now.AddDays(-5) },
                new Debt { Id = 3, PersonId = 2, PersonName = "فاطمة عبدالله", Amount = 320000, Date = DateTime.Now.AddDays(-15), DueDate = DateTime.Now.AddDays(15), Description = "حديد تسليح 12 ملم - 2 طن", IsSettled = false },
                new Debt { Id = 4, PersonId = 3, PersonName = "محمد سعد الغامدي", Amount = 890000, Date = DateTime.Now.AddDays(-25), DueDate = DateTime.Now.AddDays(-2), Description = "لحام وتركيب هيكل حديدي", IsSettled = false },
                new Debt { Id = 5, PersonId = 3, PersonName = "محمد سعد الغامدي", Amount = 150000, Date = DateTime.Now.AddDays(-10), DueDate = DateTime.Now.AddDays(20), Description = "حديد زاوية وقطع صغيرة", IsSettled = true, PaymentDate = DateTime.Now.AddDays(-3) },
                new Debt { Id = 6, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 980000, Date = DateTime.Now.AddDays(-10), DueDate = DateTime.Now.AddDays(20), Description = "تصنيع وتركيب بوابة حديدية", IsSettled = false },
                new Debt { Id = 7, PersonId = 2, PersonName = "فاطمة عبدالله", Amount = 220000, Date = DateTime.Now.AddDays(-5), DueDate = DateTime.Now.AddDays(25), Description = "حديد مشبك وأسلاك ربط", IsSettled = true, PaymentDate = DateTime.Now.AddDays(-1) }
            };

            return allDebts.Where(d => d.PersonId == personId).ToList();
        }

        private void UpdateStatistics()
        {
            try
            {
                var debts = _personDebts.ToList();
                var totalDebts = debts.Count;
                var totalAmount = debts.Sum(d => d.Amount);
                var paidAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
                var remainingAmount = totalAmount - paidAmount;

                TotalDebtsText.Text = totalDebts.ToString();
                TotalAmountText.Text = $"{totalAmount:N0} دينار";
                PaidAmountText.Text = $"{paidAmount:N0} دينار";
                RemainingAmountText.Text = $"{remainingAmount:N0} دينار";
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في تحديث الإحصائيات
            }
        }

        private void AddDebt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SelectedPerson == null)
                {
                    MessageBox.Show("لم يتم تحديد شخص لإضافة دين له", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إظهار فقاعة إضافة الدين
                ShowAddDebtBubble();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowAddDebtBubble()
        {
            try
            {
                // تعيين اسم الشخص في الفقاعة
                BubblePersonNameLabel.Text = $"للشخص: {SelectedPerson?.Name}";

                // تعيين تاريخ الاستحقاق الافتراضي (بعد شهر من اليوم)
                BubbleDueDatePicker.SelectedDate = DateTime.Now.AddMonths(1);

                // مسح الحقول
                BubbleAmountTextBox.Text = "";
                BubbleDescriptionTextBox.Text = "";
                BubbleNotesTextBox.Text = "";
                BubbleOperationTypeComboBox.Text = "قطع"; // استخدام Text بدلاً من SelectedIndex للسماح بالكتابة
                BubblePriorityComboBox.SelectedIndex = 1;
                BubbleStatusComboBox.SelectedIndex = 0;

                // إظهار الفقاعة مع تأثير انيميشن
                AddDebtBubbleOverlay.Visibility = Visibility.Visible;

                // تطبيق انيميشن الظهور
                AnimationHelper.FadeIn(AddDebtBubbleOverlay, 0.3);
                ScaleInTransform(BubbleScaleTransform, 0.4);

                // التركيز على حقل نوع العملية
                BubbleOperationTypeComboBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إظهار نموذج إضافة الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseBubble_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تطبيق انيميشن الإخفاء
                AnimationHelper.FadeOut(AddDebtBubbleOverlay, 0.2, () => {
                    AddDebtBubbleOverlay.Visibility = Visibility.Collapsed;
                });
                ScaleOutTransform(BubbleScaleTransform, 0.3);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إغلاق النموذج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BubbleAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                var textBox = sender as TextBox;
                if (textBox == null) return;

                // إزالة الأحرف غير الرقمية
                string text = textBox.Text;
                string numericText = "";

                foreach (char c in text)
                {
                    if (char.IsDigit(c) || c == '.')
                    {
                        numericText += c;
                    }
                }

                // تحديث النص إذا تغير
                if (text != numericText)
                {
                    int caretIndex = textBox.CaretIndex;
                    textBox.Text = numericText;
                    textBox.CaretIndex = Math.Min(caretIndex, numericText.Length);
                }

                // تنسيق المبلغ مع الفواصل
                if (decimal.TryParse(numericText, out decimal amount) && amount > 0)
                {
                    // تغيير لون الزر حسب صحة المبلغ
                    SaveDebtButton.IsEnabled = true;
                }
                else
                {
                    SaveDebtButton.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من المبلغ: {ex.Message}");
            }
        }

        private void SaveDebtBubble_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateBubbleInput())
                    return;

                // تطبيق انيميشن التحميل على الزر
                SaveDebtButton.IsEnabled = false;
                SaveDebtButton.Content = "⏳ جاري الحفظ...";

                // إنشاء كائن الدين الجديد
                var newDebt = CreateDebtFromBubbleInput();

                // حفظ الدين في قاعدة البيانات
                DatabaseHelper.AddDebt(newDebt);

                // تسجيل النشاط
                ActivityService.LogActivity(
                    $"تم إضافة دين لـ {SelectedPerson?.Name} بمبلغ {newDebt.Amount:N0} دينار",
                    "Debt");

                // تطبيق انيميشن النجاح
                AnimationHelper.Pulse(SaveDebtButton, 0.3);
                SaveDebtButton.Content = "✅ تم الحفظ";

                // إخفاء الفقاعة بعد تأخير قصير
                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(1);
                timer.Tick += (s, args) => {
                    timer.Stop();
                    CloseBubble_Click(sender, e);
                };
                timer.Start();

                // تحديث قائمة الديون
                LoadPersonDebts();
                UpdateStatistics();

                // إظهار إشعار نجاح
                NotificationHelper.ShowSuccess("تم إضافة الدين بنجاح");
            }
            catch (Exception ex)
            {
                // إعادة تعيين الزر
                SaveDebtButton.IsEnabled = true;
                SaveDebtButton.Content = "💾 حفظ الدين";

                MessageBox.Show($"خطأ في حفظ الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditDebt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الدين من الزر أو من DataGrid
                Debt selectedDebt = null;

                if (sender is Button button && button.Tag is Debt debt)
                {
                    selectedDebt = debt;
                }
                else
                {
                    selectedDebt = PersonDebtsDataGrid.SelectedItem as Debt;
                }

                if (selectedDebt == null)
                {
                    MessageBox.Show("يرجى تحديد دين للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // حفظ الدين المحدد للتعديل
                _selectedDebt = selectedDebt;

                // ملء الحقول بالبيانات الحالية
                if (EditOperationTypeComboBox != null)
                    EditOperationTypeComboBox.Text = selectedDebt.OperationType ?? "قطع";

                if (EditAmountTextBox != null)
                    EditAmountTextBox.Text = selectedDebt.Amount.ToString();

                if (EditDueDatePicker != null)
                    EditDueDatePicker.SelectedDate = selectedDebt.DueDate;

                if (EditDescriptionTextBox != null)
                    EditDescriptionTextBox.Text = selectedDebt.Description ?? "";

                // تحديث اسم الشخص في الفقاعة
                if (EditBubblePersonNameLabel != null)
                    EditBubblePersonNameLabel.Text = $"للشخص: {SelectedPerson?.Name ?? "غير محدد"}";

                // إظهار فقاعة التعديل مع انيميشن
                ShowEditBubble();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج التعديل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PayDebt_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الدين المحدد من DataGrid
            var selectedDebt = PersonDebtsDataGrid.SelectedItem as Debt;
            if (selectedDebt == null)
            {
                MessageBox.Show("يرجى تحديد دين للتسديد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (selectedDebt.IsSettled)
            {
                MessageBox.Show("هذا الدين مسدد بالفعل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show(
                $"هل تريد تسديد هذا الدين؟\nالمبلغ: {selectedDebt.Amount:N0} دينار\nالوصف: {selectedDebt.Description}",
                "تأكيد التسديد",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    selectedDebt.IsSettled = true;
                    selectedDebt.SettlementDate = DateTime.Now;
                    selectedDebt.PaymentDate = DateTime.Now;

                    // تحديث في قاعدة البيانات
                    bool updateSuccess = DatabaseHelper.UpdateDebt(selectedDebt);

                    if (updateSuccess)
                    {
                        // إعادة تحميل الديون من قاعدة البيانات لضمان التزامن
                        LoadPersonDebts();
                        UpdateStatistics();

                        MessageBox.Show("تم تسديد الدين بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                        System.Diagnostics.Debug.WriteLine($"تم تسديد الدين {selectedDebt.Id} بنجاح");
                    }
                    else
                    {
                        // إعادة الحالة السابقة في حالة فشل الحفظ
                        selectedDebt.IsSettled = false;
                        selectedDebt.SettlementDate = null;
                        selectedDebt.PaymentDate = null;

                        MessageBox.Show("فشل في حفظ التسديد في قاعدة البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تسديد الدين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void TogglePayment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Debt debt)
            {
                try
                {
                    var newStatus = !debt.IsSettled;
                    var confirmMessage = newStatus ?
                        $"هل تريد تسديد هذا الدين؟\nالمبلغ: {debt.Amount:N0} دينار" :
                        $"هل تريد إلغاء تسديد هذا الدين؟\nالمبلغ: {debt.Amount:N0} دينار";

                    var result = MessageBox.Show(confirmMessage, "تأكيد العملية",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // تحديث حالة الدين
                        debt.IsSettled = newStatus;
                        debt.SettlementDate = newStatus ? DateTime.Now : null;
                        debt.PaymentDate = newStatus ? DateTime.Now : null;

                        // حفظ التحديث في قاعدة البيانات
                        bool updateSuccess = DatabaseHelper.UpdateDebt(debt);

                        if (updateSuccess)
                        {
                            // إعادة تحميل البيانات من قاعدة البيانات للتأكد من التزامن
                            LoadPersonDebts();
                            UpdateStatistics();

                            var message = debt.IsSettled ? "تم تسجيل الدفع بنجاح" : "تم إلغاء تسجيل الدفع";
                            MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                            System.Diagnostics.Debug.WriteLine($"تم تحديث حالة الدين {debt.Id} إلى {(debt.IsSettled ? "مسدد" : "غير مسدد")}");
                        }
                        else
                        {
                            // إعادة الحالة السابقة في حالة فشل الحفظ
                            debt.IsSettled = !newStatus;
                            debt.SettlementDate = null;
                            debt.PaymentDate = null;

                            MessageBox.Show("فشل في حفظ التحديث في قاعدة البيانات", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث حالة الدفع: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteDebt_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الدين المحدد من DataGrid
            var selectedDebt = PersonDebtsDataGrid.SelectedItem as Debt;
            if (selectedDebt == null)
            {
                MessageBox.Show("يرجى تحديد دين للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الدين؟\nالمبلغ: {selectedDebt.Amount:N0} دينار\nالوصف: {selectedDebt.Description}",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // حذف من قاعدة البيانات
                    try
                    {
                        DatabaseHelper.DeleteDebt(selectedDebt.Id);
                    }
                    catch (Exception dbEx)
                    {
                        MessageBox.Show($"تم حذف الدين من الذاكرة ولكن فشل حذفه من قاعدة البيانات: {dbEx.Message}",
                            "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }

                    _personDebts.Remove(selectedDebt);
                    UpdateStatistics();

                    MessageBox.Show("تم حذف الدين بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الدين: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void GoBack_Click(object sender, RoutedEventArgs e)
        {
            GoBackAction?.Invoke();
        }

        private bool ValidateBubbleInput()
        {
            // التحقق من المبلغ
            if (!decimal.TryParse(BubbleAmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من الصفر", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                BubbleAmountTextBox.Focus();
                return false;
            }

            // التحقق من تاريخ الاستحقاق
            if (!BubbleDueDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد تاريخ الاستحقاق", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                BubbleDueDatePicker.Focus();
                return false;
            }

            // التحقق من أن تاريخ الاستحقاق ليس في الماضي
            if (BubbleDueDatePicker.SelectedDate.Value.Date < DateTime.Now.Date)
            {
                var result = MessageBox.Show("تاريخ الاستحقاق في الماضي. هل تريد المتابعة؟",
                    "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return false;
            }

            // التحقق من الوصف
            if (string.IsNullOrWhiteSpace(BubbleDescriptionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال وصف للدين", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                BubbleDescriptionTextBox.Focus();
                return false;
            }

            return true;
        }

        private Debt CreateDebtFromBubbleInput()
        {
            // استخدام النص المكتوب في ComboBox بدلاً من العنصر المحدد
            var operationType = BubbleOperationTypeComboBox.Text?.Trim() ?? "قطع";

            // إزالة الرموز التعبيرية من الأولوية والحالة
            var priority = ((System.Windows.Controls.ComboBoxItem)BubblePriorityComboBox.SelectedItem)?.Content?.ToString() ?? "🟡 متوسطة";
            priority = priority.Replace("🔵 ", "").Replace("🟡 ", "").Replace("🟠 ", "").Replace("🔴 ", "");

            var status = ((System.Windows.Controls.ComboBoxItem)BubbleStatusComboBox.SelectedItem)?.Content?.ToString() ?? "⏳ غير مسدد";
            status = status.Replace("⏳ ", "").Replace("🔄 ", "").Replace("✅ ", "");

            return new Debt
            {
                PersonId = SelectedPerson?.Id ?? 0,
                PersonName = SelectedPerson?.Name ?? "",
                Amount = decimal.Parse(BubbleAmountTextBox.Text),
                DueDate = BubbleDueDatePicker.SelectedDate.Value,
                Description = BubbleDescriptionTextBox.Text.Trim(),
                OperationType = operationType,
                Notes = BubbleNotesTextBox.Text?.Trim() ?? "",
                IsSettled = status.Contains("مسدد") && !status.Contains("غير"),
                Date = DateTime.Now,
                LastUpdated = DateTime.Now,

                // خصائص إضافية حسب نوع العملية
                CuttingCost = operationType.Contains("قطع") || operationType.Contains("تقطيع") ? decimal.Parse(BubbleAmountTextBox.Text) : 0,
                WeldingCost = operationType.Contains("لحام") ? decimal.Parse(BubbleAmountTextBox.Text) : 0,
                BendingCost = operationType.Contains("ثني") ? decimal.Parse(BubbleAmountTextBox.Text) : 0,
                IronCost = 0,
                TransportCost = 0
            };
        }

        private void ScaleInTransform(ScaleTransform transform, double duration = 0.4)
        {
            try
            {
                var storyboard = new System.Windows.Media.Animation.Storyboard();

                var scaleXAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0.8,
                    To = 1.0,
                    Duration = TimeSpan.FromSeconds(duration),
                    EasingFunction = new System.Windows.Media.Animation.BackEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                };

                var scaleYAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0.8,
                    To = 1.0,
                    Duration = TimeSpan.FromSeconds(duration),
                    EasingFunction = new System.Windows.Media.Animation.BackEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                };

                System.Windows.Media.Animation.Storyboard.SetTarget(scaleXAnimation, transform);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleXAnimation, new PropertyPath(ScaleTransform.ScaleXProperty));

                System.Windows.Media.Animation.Storyboard.SetTarget(scaleYAnimation, transform);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleYAnimation, new PropertyPath(ScaleTransform.ScaleYProperty));

                storyboard.Children.Add(scaleXAnimation);
                storyboard.Children.Add(scaleYAnimation);
                storyboard.Begin();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في انيميشن ScaleIn: {ex.Message}");
            }
        }

        private void ScaleOutTransform(ScaleTransform transform, double duration = 0.3)
        {
            try
            {
                var storyboard = new System.Windows.Media.Animation.Storyboard();

                var scaleXAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1.0,
                    To = 0.8,
                    Duration = TimeSpan.FromSeconds(duration),
                    EasingFunction = new System.Windows.Media.Animation.QuarticEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseIn }
                };

                var scaleYAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1.0,
                    To = 0.8,
                    Duration = TimeSpan.FromSeconds(duration),
                    EasingFunction = new System.Windows.Media.Animation.QuarticEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseIn }
                };

                System.Windows.Media.Animation.Storyboard.SetTarget(scaleXAnimation, transform);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleXAnimation, new PropertyPath(ScaleTransform.ScaleXProperty));

                System.Windows.Media.Animation.Storyboard.SetTarget(scaleYAnimation, transform);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleYAnimation, new PropertyPath(ScaleTransform.ScaleYProperty));

                storyboard.Children.Add(scaleXAnimation);
                storyboard.Children.Add(scaleYAnimation);
                storyboard.Begin();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في انيميشن ScaleOut: {ex.Message}");
            }
        }

        private void ShowEditBubble()
        {
            try
            {
                EditDebtBubbleOverlay.Visibility = Visibility.Visible;

                // انيميشن الظهور
                AnimationHelper.FadeIn(EditDebtBubbleOverlay, 0.3);

                // انيميشن التكبير للفقاعة نفسها
                var border = EditDebtBubbleOverlay.Children[0] as Border;
                if (border != null)
                {
                    AnimationHelper.ScaleIn(border, 0.3);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إظهار فقاعة التعديل: {ex.Message}");
                EditDebtBubbleOverlay.Visibility = Visibility.Visible;
            }
        }

        private void CloseEditBubble_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // انيميشن الاختفاء
                AnimationHelper.FadeOut(EditDebtBubbleOverlay, 0.3, () =>
                {
                    EditDebtBubbleOverlay.Visibility = Visibility.Collapsed;

                    // مسح الحقول
                    EditOperationTypeComboBox.Text = "";
                    EditAmountTextBox.Text = "";
                    EditDueDatePicker.SelectedDate = null;
                    EditDescriptionTextBox.Text = "";

                    _selectedDebt = null;
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إخفاء فقاعة التعديل: {ex.Message}");
                EditDebtBubbleOverlay.Visibility = Visibility.Collapsed;

                // مسح الحقول في حالة الخطأ
                EditOperationTypeComboBox.Text = "";
                EditAmountTextBox.Text = "";
                EditDueDatePicker.SelectedDate = null;
                EditDescriptionTextBox.Text = "";

                _selectedDebt = null;
            }
        }

        private void EditAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                var textBox = sender as TextBox;
                if (textBox == null) return;

                // التحقق من صحة المبلغ
                if (decimal.TryParse(textBox.Text, out decimal amount) && amount > 0)
                {
                    UpdateDebtButton.IsEnabled = true;
                }
                else
                {
                    UpdateDebtButton.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من المبلغ: {ex.Message}");
            }
        }

        private void UpdateDebtBubble_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedDebt == null)
                {
                    MessageBox.Show("لم يتم تحديد دين للتعديل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // التحقق من وجود الحقول المطلوبة
                if (EditAmountTextBox == null || EditDueDatePicker == null ||
                    EditOperationTypeComboBox == null || EditDescriptionTextBox == null)
                {
                    MessageBox.Show("خطأ في تحميل واجهة التعديل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(EditAmountTextBox.Text) ||
                    !decimal.TryParse(EditAmountTextBox.Text, out decimal amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    EditAmountTextBox.Focus();
                    return;
                }

                if (EditDueDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("يرجى تحديد تاريخ الاستحقاق", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    EditDueDatePicker.Focus();
                    return;
                }

                // تحديث بيانات الدين
                _selectedDebt.OperationType = EditOperationTypeComboBox.Text ?? "قطع";
                _selectedDebt.Amount = amount;
                _selectedDebt.DueDate = EditDueDatePicker.SelectedDate.Value;
                _selectedDebt.Description = EditDescriptionTextBox.Text ?? "";
                _selectedDebt.LastUpdated = DateTime.Now;

                // حفظ التحديث في قاعدة البيانات
                bool updateSuccess = DatabaseHelper.UpdateDebt(_selectedDebt);

                if (updateSuccess)
                {
                    // تحديث قائمة الديون أولاً
                    LoadPersonDebts();
                    UpdateStatistics();

                    // إخفاء الفقاعة
                    try
                    {
                        AnimationHelper.FadeOut(EditDebtBubbleOverlay, 0.3, () =>
                        {
                            EditDebtBubbleOverlay.Visibility = Visibility.Collapsed;

                            // مسح الحقول
                            EditOperationTypeComboBox.Text = "";
                            EditAmountTextBox.Text = "";
                            EditDueDatePicker.SelectedDate = null;
                            EditDescriptionTextBox.Text = "";

                            _selectedDebt = null;
                        });
                    }
                    catch
                    {
                        // في حالة فشل الانيميشن، أخفي الفقاعة مباشرة
                        EditDebtBubbleOverlay.Visibility = Visibility.Collapsed;

                        // مسح الحقول
                        EditOperationTypeComboBox.Text = "";
                        EditAmountTextBox.Text = "";
                        EditDueDatePicker.SelectedDate = null;
                        EditDescriptionTextBox.Text = "";

                        _selectedDebt = null;
                    }

                    // إظهار رسالة نجاح
                    MessageBox.Show("تم تحديث الدين بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                    System.Diagnostics.Debug.WriteLine($"تم تحديث الدين {_selectedDebt?.Id} بنجاح");
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التحديثات في قاعدة البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TogglePaymentFromContext_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الدين المحدد من DataGrid
                var selectedDebt = PersonDebtsDataGrid.SelectedItem as Debt;
                if (selectedDebt == null)
                {
                    MessageBox.Show("يرجى تحديد دين لتبديل حالة التسديد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var newStatus = !selectedDebt.IsSettled;
                var confirmMessage = newStatus ?
                    $"هل تريد تسديد هذا الدين؟\n\nالمبلغ: {selectedDebt.Amount:N0} دينار\nالوصف: {selectedDebt.Description}" :
                    $"هل تريد إلغاء تسديد هذا الدين؟\n\nالمبلغ: {selectedDebt.Amount:N0} دينار\nالوصف: {selectedDebt.Description}";

                var result = MessageBox.Show(confirmMessage, "تأكيد العملية",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // تحديث حالة الدين
                    selectedDebt.IsSettled = newStatus;
                    selectedDebt.SettlementDate = newStatus ? DateTime.Now : null;
                    selectedDebt.PaymentDate = newStatus ? DateTime.Now : null;

                    // حفظ التحديث في قاعدة البيانات
                    bool updateSuccess = DatabaseHelper.UpdateDebt(selectedDebt);

                    if (updateSuccess)
                    {
                        // إعادة تحميل البيانات من قاعدة البيانات للتأكد من التزامن
                        LoadPersonDebts();
                        UpdateStatistics();

                        var message = selectedDebt.IsSettled ?
                            "✅ تم تسجيل الدفع بنجاح" :
                            "❌ تم إلغاء تسجيل الدفع بنجاح";

                        MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                        System.Diagnostics.Debug.WriteLine($"تم تحديث حالة الدين {selectedDebt.Id} إلى {(selectedDebt.IsSettled ? "مسدد" : "غير مسدد")}");
                    }
                    else
                    {
                        // إعادة الحالة السابقة في حالة فشل الحفظ
                        selectedDebt.IsSettled = !newStatus;
                        selectedDebt.SettlementDate = null;
                        selectedDebt.PaymentDate = null;

                        MessageBox.Show("فشل في حفظ التحديث في قاعدة البيانات", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث حالة الدفع: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PersonDebtsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تحديث نص قائمة السياق حسب حالة الدين المحدد
                var selectedDebt = PersonDebtsDataGrid.SelectedItem as Debt;
                if (selectedDebt != null && TogglePaymentMenuItem != null)
                {
                    if (selectedDebt.IsSettled)
                    {
                        TogglePaymentMenuItem.Header = "❌ إلغاء التسديد";
                        TogglePaymentMenuItem.Foreground = new SolidColorBrush(Color.FromRgb(239, 68, 68)); // أحمر
                    }
                    else
                    {
                        TogglePaymentMenuItem.Header = "✅ تسديد الدين";
                        TogglePaymentMenuItem.Foreground = new SolidColorBrush(Color.FromRgb(34, 197, 94)); // أخضر
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث قائمة السياق: {ex.Message}");
            }
        }

        private void PrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SelectedPerson == null)
                {
                    MessageBox.Show("لا يوجد شخص محدد للطباعة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء مستند للطباعة
                var printDocument = CreatePrintDocument();

                // إظهار معاينة الطباعة
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    IDocumentPaginatorSource idpSource = printDocument;
                    printDialog.PrintDocument(idpSource.DocumentPaginator, $"تقرير ديون {SelectedPerson.Name}");
                    MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FlowDocument CreatePrintDocument()
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);
            document.FontFamily = new FontFamily("Arial");
            document.FontSize = 12;
            document.FlowDirection = FlowDirection.RightToLeft;

            // عنوان التقرير
            var title = new Paragraph();
            title.FontSize = 20;
            title.FontWeight = FontWeights.Bold;
            title.TextAlignment = TextAlignment.Center;
            title.Margin = new Thickness(0, 0, 0, 20);
            title.Inlines.Add(new Run($"تقرير ديون {SelectedPerson?.Name}"));
            document.Blocks.Add(title);

            // معلومات الشخص
            var personInfo = new Paragraph();
            personInfo.FontSize = 14;
            personInfo.Margin = new Thickness(0, 0, 0, 15);
            personInfo.Inlines.Add(new Run("معلومات الشخص:") { FontWeight = FontWeights.Bold });
            personInfo.Inlines.Add(new LineBreak());
            personInfo.Inlines.Add(new Run($"الاسم: {SelectedPerson?.Name}"));
            personInfo.Inlines.Add(new LineBreak());
            personInfo.Inlines.Add(new Run($"الهاتف: {SelectedPerson?.Phone ?? "غير محدد"}"));
            personInfo.Inlines.Add(new LineBreak());
            personInfo.Inlines.Add(new Run($"العنوان: {SelectedPerson?.Address ?? "غير محدد"}"));
            personInfo.Inlines.Add(new LineBreak());
            personInfo.Inlines.Add(new Run($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}"));
            document.Blocks.Add(personInfo);

            // إحصائيات الديون
            var totalAmount = PersonDebts.Sum(d => d.Amount);
            var settledAmount = PersonDebts.Where(d => d.IsSettled).Sum(d => d.Amount);
            var pendingAmount = totalAmount - settledAmount;
            var settledCount = PersonDebts.Count(d => d.IsSettled);
            var pendingCount = PersonDebts.Count(d => !d.IsSettled);

            var statistics = new Paragraph();
            statistics.FontSize = 14;
            statistics.Margin = new Thickness(0, 0, 0, 20);
            statistics.Inlines.Add(new Run("إحصائيات الديون:") { FontWeight = FontWeights.Bold });
            statistics.Inlines.Add(new LineBreak());
            statistics.Inlines.Add(new Run($"إجمالي الديون: {PersonDebts.Count} دين"));
            statistics.Inlines.Add(new LineBreak());
            statistics.Inlines.Add(new Run($"الديون المسددة: {settledCount} دين"));
            statistics.Inlines.Add(new LineBreak());
            statistics.Inlines.Add(new Run($"الديون غير المسددة: {pendingCount} دين"));
            statistics.Inlines.Add(new LineBreak());
            statistics.Inlines.Add(new Run($"إجمالي المبلغ: {totalAmount:N0} دينار عراقي"));
            statistics.Inlines.Add(new LineBreak());
            statistics.Inlines.Add(new Run($"المبلغ المسدد: {settledAmount:N0} دينار عراقي"));
            statistics.Inlines.Add(new LineBreak());
            statistics.Inlines.Add(new Run($"المبلغ المتبقي: {pendingAmount:N0} دينار عراقي"));
            document.Blocks.Add(statistics);

            // جدول الديون
            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);

            // تعريف الأعمدة
            table.Columns.Add(new TableColumn() { Width = new GridLength(60) });  // المبلغ
            table.Columns.Add(new TableColumn() { Width = new GridLength(80) });  // تاريخ الدين
            table.Columns.Add(new TableColumn() { Width = new GridLength(80) });  // تاريخ الاستحقاق
            table.Columns.Add(new TableColumn() { Width = new GridLength(80) });  // نوع العملية
            table.Columns.Add(new TableColumn() { Width = new GridLength(120) }); // الوصف
            table.Columns.Add(new TableColumn() { Width = new GridLength(100) }); // الملاحظات
            table.Columns.Add(new TableColumn() { Width = new GridLength(60) });  // الحالة

            // رأس الجدول
            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;

            AddTableCell(headerRow, "المبلغ (د.ع)", true);
            AddTableCell(headerRow, "تاريخ الدين", true);
            AddTableCell(headerRow, "تاريخ الاستحقاق", true);
            AddTableCell(headerRow, "نوع العملية", true);
            AddTableCell(headerRow, "الوصف", true);
            AddTableCell(headerRow, "الملاحظات", true);
            AddTableCell(headerRow, "الحالة", true);

            headerRowGroup.Rows.Add(headerRow);
            table.RowGroups.Add(headerRowGroup);

            // بيانات الجدول
            var dataRowGroup = new TableRowGroup();
            foreach (var debt in PersonDebts.OrderByDescending(d => d.Date))
            {
                var row = new TableRow();

                AddTableCell(row, debt.Amount.ToString("N0"));
                AddTableCell(row, debt.Date.ToString("yyyy/MM/dd"));
                AddTableCell(row, debt.DueDate.ToString("yyyy/MM/dd"));
                AddTableCell(row, debt.OperationType ?? "غير محدد");
                AddTableCell(row, debt.Description ?? "");
                AddTableCell(row, debt.Notes ?? "");
                AddTableCell(row, debt.IsSettled ? "مسدد" : "غير مسدد");

                dataRowGroup.Rows.Add(row);
            }
            table.RowGroups.Add(dataRowGroup);

            document.Blocks.Add(table);

            // تذييل التقرير
            var footer = new Paragraph();
            footer.FontSize = 10;
            footer.TextAlignment = TextAlignment.Center;
            footer.Margin = new Thickness(0, 20, 0, 0);
            footer.Inlines.Add(new Run($"تم إنشاء هذا التقرير بواسطة نظام إدارة الديون - {DateTime.Now:yyyy/MM/dd HH:mm}"));
            document.Blocks.Add(footer);

            return document;
        }

        private void AddTableCell(TableRow row, string content, bool isHeader = false)
        {
            var cell = new TableCell();
            cell.BorderBrush = Brushes.Black;
            cell.BorderThickness = new Thickness(1);
            cell.Padding = new Thickness(5);

            var paragraph = new Paragraph();
            paragraph.TextAlignment = TextAlignment.Center;
            paragraph.Margin = new Thickness(0);

            if (isHeader)
            {
                paragraph.FontWeight = FontWeights.Bold;
            }

            paragraph.Inlines.Add(new Run(content));
            cell.Blocks.Add(paragraph);
            row.Cells.Add(cell);
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
