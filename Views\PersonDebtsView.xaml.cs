using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;
using DebtManagementApp.Services;

namespace DebtManagementApp.Views
{
    public partial class PersonDebtsView : UserControl, INotifyPropertyChanged
    {
        private readonly ObservableCollection<Debt> _personDebts;
        private Person _selectedPerson;
        private Debt _selectedDebt;
        private Action _goBackAction;

        public PersonDebtsView()
        {
            InitializeComponent();
            _personDebts = new ObservableCollection<Debt>();
            DataContext = this;

            // تسجيل أحداث حفظ إعدادات الأعمدة
            Loaded += (s, e) => ColumnSettingsHelper.RegisterColumnEvents(PersonDebtsDataGrid, "PersonDebtsGrid");
        }

        public ObservableCollection<Debt> PersonDebts
        {
            get => _personDebts;
        }

        public Person SelectedPerson
        {
            get => _selectedPerson;
            set
            {
                _selectedPerson = value;
                OnPropertyChanged(nameof(SelectedPerson));
                LoadPersonDebts();
            }
        }

        public Debt SelectedDebt
        {
            get => _selectedDebt;
            set
            {
                _selectedDebt = value;
                OnPropertyChanged(nameof(SelectedDebt));
            }
        }

        public Action GoBackAction
        {
            get => _goBackAction;
            set => _goBackAction = value;
        }

        public void SetPerson(Person person, Action goBackAction = null)
        {
            SelectedPerson = person;
            GoBackAction = goBackAction;
            LoadPersonDebts(); // تحميل الديون فوراً
        }

        private void LoadPersonDebts()
        {
            if (SelectedPerson == null) return;

            try
            {
                // تحديث معلومات الشخص
                PersonNameTitle.Text = $"💰 ديون {SelectedPerson.Name}";
                PersonInfoText.Text = $"الهاتف: {SelectedPerson.Phone ?? "غير محدد"} | العنوان: {SelectedPerson.Address ?? "غير محدد"}";

                // تحميل الديون من قاعدة البيانات
                _personDebts.Clear();

                try
                {
                    var allDebts = DatabaseHelper.GetAllDebts();
                    var personDebts = allDebts.Where(d => d.PersonId == SelectedPerson.Id).ToList();

                    foreach (var debt in personDebts)
                    {
                        _personDebts.Add(debt);
                    }

                    System.Diagnostics.Debug.WriteLine($"تم تحميل {personDebts.Count} دين للشخص {SelectedPerson.Name}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الديون: {ex.Message}");
                    MessageBox.Show($"خطأ في تحميل الديون من قاعدة البيانات: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل ديون الشخص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private System.Collections.Generic.List<Debt> GetSampleDebtsForPerson(int personId)
        {
            var allDebts = new System.Collections.Generic.List<Debt>
            {
                new Debt { Id = 1, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 750000, Date = DateTime.Now.AddDays(-30), DueDate = DateTime.Now.AddDays(-5), Description = "حديد تسليح 16 ملم - 5 طن", IsSettled = false },
                new Debt { Id = 2, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 450000, Date = DateTime.Now.AddDays(-20), DueDate = DateTime.Now.AddDays(10), Description = "قطع وثني حديد للأعمدة", IsSettled = true, PaymentDate = DateTime.Now.AddDays(-5) },
                new Debt { Id = 3, PersonId = 2, PersonName = "فاطمة عبدالله", Amount = 320000, Date = DateTime.Now.AddDays(-15), DueDate = DateTime.Now.AddDays(15), Description = "حديد تسليح 12 ملم - 2 طن", IsSettled = false },
                new Debt { Id = 4, PersonId = 3, PersonName = "محمد سعد الغامدي", Amount = 890000, Date = DateTime.Now.AddDays(-25), DueDate = DateTime.Now.AddDays(-2), Description = "لحام وتركيب هيكل حديدي", IsSettled = false },
                new Debt { Id = 5, PersonId = 3, PersonName = "محمد سعد الغامدي", Amount = 150000, Date = DateTime.Now.AddDays(-10), DueDate = DateTime.Now.AddDays(20), Description = "حديد زاوية وقطع صغيرة", IsSettled = true, PaymentDate = DateTime.Now.AddDays(-3) },
                new Debt { Id = 6, PersonId = 1, PersonName = "أحمد محمد علي", Amount = 980000, Date = DateTime.Now.AddDays(-10), DueDate = DateTime.Now.AddDays(20), Description = "تصنيع وتركيب بوابة حديدية", IsSettled = false },
                new Debt { Id = 7, PersonId = 2, PersonName = "فاطمة عبدالله", Amount = 220000, Date = DateTime.Now.AddDays(-5), DueDate = DateTime.Now.AddDays(25), Description = "حديد مشبك وأسلاك ربط", IsSettled = true, PaymentDate = DateTime.Now.AddDays(-1) }
            };

            return allDebts.Where(d => d.PersonId == personId).ToList();
        }

        private void UpdateStatistics()
        {
            try
            {
                var debts = _personDebts.ToList();
                var totalDebts = debts.Count;
                var totalAmount = debts.Sum(d => d.Amount);
                var paidAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
                var remainingAmount = totalAmount - paidAmount;

                TotalDebtsText.Text = totalDebts.ToString();
                TotalAmountText.Text = $"{totalAmount:N0} دينار";
                PaidAmountText.Text = $"{paidAmount:N0} دينار";
                RemainingAmountText.Text = $"{remainingAmount:N0} دينار";
            }
            catch (Exception)
            {
                // تجاهل الأخطاء في تحديث الإحصائيات
            }
        }

        private void AddDebt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SelectedPerson == null)
                {
                    MessageBox.Show("لم يتم تحديد شخص لإضافة دين له", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إظهار فقاعة إضافة الدين
                ShowAddDebtBubble();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowAddDebtBubble()
        {
            try
            {
                // تعيين اسم الشخص في الفقاعة
                BubblePersonNameLabel.Text = $"للشخص: {SelectedPerson?.Name}";

                // تعيين تاريخ الاستحقاق الافتراضي (بعد شهر من اليوم)
                BubbleDueDatePicker.SelectedDate = DateTime.Now.AddMonths(1);

                // مسح الحقول
                BubbleAmountTextBox.Text = "";
                BubbleDescriptionTextBox.Text = "";
                BubbleNotesTextBox.Text = "";
                BubbleOperationTypeComboBox.Text = "قطع"; // استخدام Text بدلاً من SelectedIndex للسماح بالكتابة
                BubblePriorityComboBox.SelectedIndex = 1;
                BubbleStatusComboBox.SelectedIndex = 0;

                // إظهار الفقاعة مع تأثير انيميشن
                AddDebtBubbleOverlay.Visibility = Visibility.Visible;

                // تطبيق انيميشن الظهور
                AnimationHelper.FadeIn(AddDebtBubbleOverlay, 0.3);
                ScaleInTransform(BubbleScaleTransform, 0.4);

                // التركيز على حقل نوع العملية
                BubbleOperationTypeComboBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إظهار نموذج إضافة الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseBubble_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تطبيق انيميشن الإخفاء
                AnimationHelper.FadeOut(AddDebtBubbleOverlay, 0.2, () => {
                    AddDebtBubbleOverlay.Visibility = Visibility.Collapsed;
                });
                ScaleOutTransform(BubbleScaleTransform, 0.3);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إغلاق النموذج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BubbleAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                var textBox = sender as TextBox;
                if (textBox == null) return;

                // إزالة الأحرف غير الرقمية
                string text = textBox.Text;
                string numericText = "";

                foreach (char c in text)
                {
                    if (char.IsDigit(c) || c == '.')
                    {
                        numericText += c;
                    }
                }

                // تحديث النص إذا تغير
                if (text != numericText)
                {
                    int caretIndex = textBox.CaretIndex;
                    textBox.Text = numericText;
                    textBox.CaretIndex = Math.Min(caretIndex, numericText.Length);
                }

                // تنسيق المبلغ مع الفواصل
                if (decimal.TryParse(numericText, out decimal amount) && amount > 0)
                {
                    // تغيير لون الزر حسب صحة المبلغ
                    SaveDebtButton.IsEnabled = true;
                }
                else
                {
                    SaveDebtButton.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من المبلغ: {ex.Message}");
            }
        }

        private void SaveDebtBubble_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateBubbleInput())
                    return;

                // تطبيق انيميشن التحميل على الزر
                SaveDebtButton.IsEnabled = false;
                SaveDebtButton.Content = "⏳ جاري الحفظ...";

                // إنشاء كائن الدين الجديد
                var newDebt = CreateDebtFromBubbleInput();

                // حفظ الدين في قاعدة البيانات
                DatabaseHelper.AddDebt(newDebt);

                // تسجيل النشاط
                ActivityService.LogActivity(
                    $"تم إضافة دين لـ {SelectedPerson?.Name} بمبلغ {newDebt.Amount:N0} دينار",
                    "Debt");

                // تطبيق انيميشن النجاح
                AnimationHelper.Pulse(SaveDebtButton, 0.3);
                SaveDebtButton.Content = "✅ تم الحفظ";

                // إخفاء الفقاعة بعد تأخير قصير
                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(1);
                timer.Tick += (s, args) => {
                    timer.Stop();
                    CloseBubble_Click(sender, e);
                };
                timer.Start();

                // تحديث قائمة الديون
                LoadPersonDebts();
                UpdateStatistics();

                // إظهار إشعار نجاح
                NotificationHelper.ShowSuccess("تم إضافة الدين بنجاح");
            }
            catch (Exception ex)
            {
                // إعادة تعيين الزر
                SaveDebtButton.IsEnabled = true;
                SaveDebtButton.Content = "💾 حفظ الدين";

                MessageBox.Show($"خطأ في حفظ الدين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditDebt_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الدين المحدد من DataGrid
            var selectedDebt = PersonDebtsDataGrid.SelectedItem as Debt;
            if (selectedDebt == null)
            {
                MessageBox.Show("يرجى تحديد دين للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // TODO: فتح نموذج تعديل الدين
            MessageBox.Show($"سيتم فتح نموذج تعديل الدين:\nالمبلغ: {selectedDebt.Amount:N0} دينار\nالوصف: {selectedDebt.Description}",
                "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PayDebt_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الدين المحدد من DataGrid
            var selectedDebt = PersonDebtsDataGrid.SelectedItem as Debt;
            if (selectedDebt == null)
            {
                MessageBox.Show("يرجى تحديد دين للتسديد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (selectedDebt.IsSettled)
            {
                MessageBox.Show("هذا الدين مسدد بالفعل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show(
                $"هل تريد تسديد هذا الدين؟\nالمبلغ: {selectedDebt.Amount:N0} دينار\nالوصف: {selectedDebt.Description}",
                "تأكيد التسديد",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    selectedDebt.IsSettled = true;
                    selectedDebt.SettlementDate = DateTime.Now;
                    selectedDebt.PaymentDate = DateTime.Now;

                    // تحديث في قاعدة البيانات
                    bool updateSuccess = DatabaseHelper.UpdateDebt(selectedDebt);

                    if (updateSuccess)
                    {
                        // إعادة تحميل الديون من قاعدة البيانات لضمان التزامن
                        LoadPersonDebts();
                        UpdateStatistics();

                        MessageBox.Show("تم تسديد الدين بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                        System.Diagnostics.Debug.WriteLine($"تم تسديد الدين {selectedDebt.Id} بنجاح");
                    }
                    else
                    {
                        // إعادة الحالة السابقة في حالة فشل الحفظ
                        selectedDebt.IsSettled = false;
                        selectedDebt.SettlementDate = null;
                        selectedDebt.PaymentDate = null;

                        MessageBox.Show("فشل في حفظ التسديد في قاعدة البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تسديد الدين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void TogglePayment_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Debt debt)
            {
                try
                {
                    var newStatus = !debt.IsSettled;
                    var confirmMessage = newStatus ?
                        $"هل تريد تسديد هذا الدين؟\nالمبلغ: {debt.Amount:N0} دينار" :
                        $"هل تريد إلغاء تسديد هذا الدين؟\nالمبلغ: {debt.Amount:N0} دينار";

                    var result = MessageBox.Show(confirmMessage, "تأكيد العملية",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // تحديث حالة الدين
                        debt.IsSettled = newStatus;
                        debt.SettlementDate = newStatus ? DateTime.Now : null;
                        debt.PaymentDate = newStatus ? DateTime.Now : null;

                        // حفظ التحديث في قاعدة البيانات
                        bool updateSuccess = DatabaseHelper.UpdateDebt(debt);

                        if (updateSuccess)
                        {
                            // إعادة تحميل البيانات من قاعدة البيانات للتأكد من التزامن
                            LoadPersonDebts();
                            UpdateStatistics();

                            var message = debt.IsSettled ? "تم تسجيل الدفع بنجاح" : "تم إلغاء تسجيل الدفع";
                            MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                            System.Diagnostics.Debug.WriteLine($"تم تحديث حالة الدين {debt.Id} إلى {(debt.IsSettled ? "مسدد" : "غير مسدد")}");
                        }
                        else
                        {
                            // إعادة الحالة السابقة في حالة فشل الحفظ
                            debt.IsSettled = !newStatus;
                            debt.SettlementDate = null;
                            debt.PaymentDate = null;

                            MessageBox.Show("فشل في حفظ التحديث في قاعدة البيانات", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث حالة الدفع: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteDebt_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الدين المحدد من DataGrid
            var selectedDebt = PersonDebtsDataGrid.SelectedItem as Debt;
            if (selectedDebt == null)
            {
                MessageBox.Show("يرجى تحديد دين للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الدين؟\nالمبلغ: {selectedDebt.Amount:N0} دينار\nالوصف: {selectedDebt.Description}",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // حذف من قاعدة البيانات
                    try
                    {
                        DatabaseHelper.DeleteDebt(selectedDebt.Id);
                    }
                    catch (Exception dbEx)
                    {
                        MessageBox.Show($"تم حذف الدين من الذاكرة ولكن فشل حذفه من قاعدة البيانات: {dbEx.Message}",
                            "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }

                    _personDebts.Remove(selectedDebt);
                    UpdateStatistics();

                    MessageBox.Show("تم حذف الدين بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الدين: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void GoBack_Click(object sender, RoutedEventArgs e)
        {
            GoBackAction?.Invoke();
        }

        private bool ValidateBubbleInput()
        {
            // التحقق من المبلغ
            if (!decimal.TryParse(BubbleAmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من الصفر", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                BubbleAmountTextBox.Focus();
                return false;
            }

            // التحقق من تاريخ الاستحقاق
            if (!BubbleDueDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد تاريخ الاستحقاق", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                BubbleDueDatePicker.Focus();
                return false;
            }

            // التحقق من أن تاريخ الاستحقاق ليس في الماضي
            if (BubbleDueDatePicker.SelectedDate.Value.Date < DateTime.Now.Date)
            {
                var result = MessageBox.Show("تاريخ الاستحقاق في الماضي. هل تريد المتابعة؟",
                    "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return false;
            }

            // التحقق من الوصف
            if (string.IsNullOrWhiteSpace(BubbleDescriptionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال وصف للدين", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                BubbleDescriptionTextBox.Focus();
                return false;
            }

            return true;
        }

        private Debt CreateDebtFromBubbleInput()
        {
            // استخدام النص المكتوب في ComboBox بدلاً من العنصر المحدد
            var operationType = BubbleOperationTypeComboBox.Text?.Trim() ?? "قطع";

            // إزالة الرموز التعبيرية من الأولوية والحالة
            var priority = ((System.Windows.Controls.ComboBoxItem)BubblePriorityComboBox.SelectedItem)?.Content?.ToString() ?? "🟡 متوسطة";
            priority = priority.Replace("🔵 ", "").Replace("🟡 ", "").Replace("🟠 ", "").Replace("🔴 ", "");

            var status = ((System.Windows.Controls.ComboBoxItem)BubbleStatusComboBox.SelectedItem)?.Content?.ToString() ?? "⏳ غير مسدد";
            status = status.Replace("⏳ ", "").Replace("🔄 ", "").Replace("✅ ", "");

            return new Debt
            {
                PersonId = SelectedPerson?.Id ?? 0,
                PersonName = SelectedPerson?.Name ?? "",
                Amount = decimal.Parse(BubbleAmountTextBox.Text),
                DueDate = BubbleDueDatePicker.SelectedDate.Value,
                Description = BubbleDescriptionTextBox.Text.Trim(),
                OperationType = operationType,
                Notes = BubbleNotesTextBox.Text?.Trim() ?? "",
                IsSettled = status.Contains("مسدد") && !status.Contains("غير"),
                Date = DateTime.Now,
                LastUpdated = DateTime.Now,

                // خصائص إضافية حسب نوع العملية
                CuttingCost = operationType.Contains("قطع") || operationType.Contains("تقطيع") ? decimal.Parse(BubbleAmountTextBox.Text) : 0,
                WeldingCost = operationType.Contains("لحام") ? decimal.Parse(BubbleAmountTextBox.Text) : 0,
                BendingCost = operationType.Contains("ثني") ? decimal.Parse(BubbleAmountTextBox.Text) : 0,
                IronCost = 0,
                TransportCost = 0
            };
        }

        private void ScaleInTransform(ScaleTransform transform, double duration = 0.4)
        {
            try
            {
                var storyboard = new System.Windows.Media.Animation.Storyboard();

                var scaleXAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0.8,
                    To = 1.0,
                    Duration = TimeSpan.FromSeconds(duration),
                    EasingFunction = new System.Windows.Media.Animation.BackEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                };

                var scaleYAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 0.8,
                    To = 1.0,
                    Duration = TimeSpan.FromSeconds(duration),
                    EasingFunction = new System.Windows.Media.Animation.BackEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                };

                System.Windows.Media.Animation.Storyboard.SetTarget(scaleXAnimation, transform);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleXAnimation, new PropertyPath(ScaleTransform.ScaleXProperty));

                System.Windows.Media.Animation.Storyboard.SetTarget(scaleYAnimation, transform);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleYAnimation, new PropertyPath(ScaleTransform.ScaleYProperty));

                storyboard.Children.Add(scaleXAnimation);
                storyboard.Children.Add(scaleYAnimation);
                storyboard.Begin();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في انيميشن ScaleIn: {ex.Message}");
            }
        }

        private void ScaleOutTransform(ScaleTransform transform, double duration = 0.3)
        {
            try
            {
                var storyboard = new System.Windows.Media.Animation.Storyboard();

                var scaleXAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1.0,
                    To = 0.8,
                    Duration = TimeSpan.FromSeconds(duration),
                    EasingFunction = new System.Windows.Media.Animation.QuarticEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseIn }
                };

                var scaleYAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1.0,
                    To = 0.8,
                    Duration = TimeSpan.FromSeconds(duration),
                    EasingFunction = new System.Windows.Media.Animation.QuarticEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseIn }
                };

                System.Windows.Media.Animation.Storyboard.SetTarget(scaleXAnimation, transform);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleXAnimation, new PropertyPath(ScaleTransform.ScaleXProperty));

                System.Windows.Media.Animation.Storyboard.SetTarget(scaleYAnimation, transform);
                System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleYAnimation, new PropertyPath(ScaleTransform.ScaleYProperty));

                storyboard.Children.Add(scaleXAnimation);
                storyboard.Children.Add(scaleYAnimation);
                storyboard.Begin();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في انيميشن ScaleOut: {ex.Message}");
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
