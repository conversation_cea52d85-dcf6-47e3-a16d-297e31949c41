using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using Microsoft.Data.Sqlite;
using DebtManagementApp.Models;
using DebtManagementApp.Exceptions;

namespace DebtManagementApp
{
    public static class DatabaseHelper
    {
        private static readonly string DatabaseFile = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DebtManagementApp",
            "DebtManagement.db");

        public static string GetDatabaseFile() => DatabaseFile;

        public static void InitializeDatabase()
        {
            try
            {
                // إنشاء المجلد إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(DatabaseFile);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

            string createPersonsTable = @"CREATE TABLE IF NOT EXISTS Persons (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Phone TEXT,
                Location TEXT,
                AdditionalInfo TEXT
            );";

            string createDebtsTable = @"CREATE TABLE IF NOT EXISTS Debts (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                PersonId INTEGER NOT NULL,
                Date TEXT NOT NULL,
                Amount REAL NOT NULL,
                IronCost REAL DEFAULT 0,
                CuttingCost REAL DEFAULT 0,
                BendingCost REAL DEFAULT 0,
                WeldingCost REAL DEFAULT 0,
                IsSettled INTEGER DEFAULT 0,
                Description TEXT,
                PdfFilePath TEXT,
                CuttingLength REAL DEFAULT 0,
                CuttingRatePerMeter REAL DEFAULT 0,
                IronThickness REAL DEFAULT 0,
                IronLength REAL DEFAULT 0,
                IronWidth REAL DEFAULT 0,
                IronThicknessMm REAL DEFAULT 0,
                TonPriceUsd REAL DEFAULT 0,
                UsdToIqdRate REAL DEFAULT 0,
                TransportCost REAL DEFAULT 0,
                LastUpdated TEXT DEFAULT CURRENT_TIMESTAMP,
                DueDate TEXT DEFAULT (datetime('now', '+30 days')),
                SettlementDate TEXT,
                IsConnected INTEGER DEFAULT 1,
                OperationType TEXT DEFAULT 'عادية',
                Notes TEXT,
                FOREIGN KEY (PersonId) REFERENCES Persons(Id)
            );";
            
                using var command = new SqliteCommand(createPersonsTable, connection);
                command.ExecuteNonQuery();
                
                command.CommandText = createDebtsTable;
                command.ExecuteNonQuery();

                // تحديث هيكل جدول الديون لإضافة الأعمدة المفقودة
                UpdateDebtsTableStructure(connection);

                // إنشاء جدول ملفات PDF
                string createPdfDocumentsTable = @"CREATE TABLE IF NOT EXISTS PdfDocuments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    PersonId INTEGER NOT NULL,
                    FileName TEXT NOT NULL,
                    FilePath TEXT NOT NULL,
                    Description TEXT,
                    FileSize INTEGER NOT NULL DEFAULT 0,
                    UploadDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModified TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (PersonId) REFERENCES Persons(Id)
                );";

                // إنشاء جدول الرواتب المصروفة
                string createSalariesTable = @"CREATE TABLE IF NOT EXISTS Salaries (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    WorkerId INTEGER NOT NULL,
                    WorkerName TEXT NOT NULL,
                    PaymentDate TEXT NOT NULL,
                    FromDate TEXT NOT NULL,
                    ToDate TEXT NOT NULL,
                    DailyWage REAL NOT NULL,
                    WorkDays INTEGER NOT NULL,
                    AbsenceDays INTEGER DEFAULT 0,
                    OvertimeHours REAL DEFAULT 0,
                    OvertimeRate REAL DEFAULT 0,
                    Deductions REAL DEFAULT 0,
                    Bonuses REAL DEFAULT 0,
                    TotalAmount REAL NOT NULL,
                    Notes TEXT,
                    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP
                );";

                command.CommandText = createPdfDocumentsTable;
                command.ExecuteNonQuery();

                command.CommandText = createSalariesTable;
                command.ExecuteNonQuery();

                // تحديث جدول الرواتب إذا كان موجوداً بدون الأعمدة الجديدة
                UpdateSalariesTableStructure(connection);

                // إنشاء جدول ديون المعمل
                string createFactoryDebtsTable = @"CREATE TABLE IF NOT EXISTS FactoryDebts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    PersonId INTEGER NOT NULL,
                    PersonName TEXT NOT NULL,
                    Amount REAL NOT NULL,
                    DebtDate TEXT NOT NULL,
                    InvoiceType TEXT NOT NULL,
                    Description TEXT,
                    IsPaid INTEGER DEFAULT 0,
                    AttachmentPath TEXT,
                    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (PersonId) REFERENCES Persons (Id)
                );";

                command.CommandText = createFactoryDebtsTable;
                command.ExecuteNonQuery();

                // إنشاء جدول الأنشطة
                string createActivitiesTable = @"CREATE TABLE IF NOT EXISTS Activities (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Description TEXT NOT NULL,
                    ActivityDate TEXT NOT NULL,
                    ActivityType TEXT NOT NULL
                );";

                command.CommandText = createActivitiesTable;
                command.ExecuteNonQuery();

                // إنشاء جدول البيانات المحذوفة للتراجع
                string createDeletedDataTable = @"CREATE TABLE IF NOT EXISTS DeletedData (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    DataType TEXT NOT NULL,
                    OriginalId INTEGER NOT NULL,
                    DataJson TEXT NOT NULL,
                    DeletedDate TEXT NOT NULL,
                    ActivityId INTEGER,
                    FOREIGN KEY (ActivityId) REFERENCES Activities (Id)
                );";

                command.CommandText = createDeletedDataTable;
                command.ExecuteNonQuery();

                // إنشاء جدول تفاصيل القطع
                string createCuttingDetailsTable = @"CREATE TABLE IF NOT EXISTS CuttingDetails (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    DebtId INTEGER,
                    CuttingDistanceMm REAL NOT NULL,
                    PricePerMeter REAL NOT NULL,
                    IronThickness REAL NOT NULL DEFAULT 0,
                    TotalCuttingCost REAL NOT NULL,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (DebtId) REFERENCES Debts(Id)
                );";

                command.CommandText = createCuttingDetailsTable;
                command.ExecuteNonQuery();

                // إنشاء جدول تفاصيل الحديد
                string createIronDetailsTable = @"CREATE TABLE IF NOT EXISTS IronDetails (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    DebtId INTEGER,
                    PricePerTonUsd REAL NOT NULL,
                    UsdToIqdRate REAL NOT NULL,
                    PlateLength REAL NOT NULL,
                    PlateWidth REAL NOT NULL,
                    PlateThicknessMm REAL NOT NULL,
                    TransportCost REAL NOT NULL DEFAULT 0,
                    TotalWeight REAL NOT NULL,
                    TotalIronCost REAL NOT NULL,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (DebtId) REFERENCES Debts(Id)
                );";

                command.CommandText = createIronDetailsTable;
                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        // CRUD operations for Persons
        public static List<Person> GetAllPersons()
        {
            var persons = new List<Person>();
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();
                
                using var command = new SqliteCommand("SELECT * FROM Persons", connection);
                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    persons.Add(new Person
                    {
                        Id = reader.GetInt32(0), // Id column
                        Name = reader.GetString(1), // Name column
                        Phone = reader.IsDBNull(2) ? null : reader.GetString(2), // Phone column
                        Location = reader.IsDBNull(3) ? null : reader.GetString(3), // Location column
                        AdditionalInfo = reader.IsDBNull(4) ? null : reader.GetString(4) // AdditionalInfo column
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في قراءة الأشخاص: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            
            return persons;
        }

        public static void AddPerson(Person person)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();
                
                using var command = new SqliteCommand(
                    "INSERT INTO Persons (Name, Phone, Location, AdditionalInfo) VALUES (@Name, @Phone, @Location, @AdditionalInfo)",
                    connection);
                
                command.Parameters.AddWithValue("@Name", person.Name);
                command.Parameters.AddWithValue("@Phone", person.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Location", person.Location ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@AdditionalInfo", person.AdditionalInfo ?? (object)DBNull.Value);
                
                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الشخص: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        public static void UpdatePerson(Person person)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();
            
            using var command = new SqliteCommand(
                "UPDATE Persons SET Name = @Name, Phone = @Phone, Location = @Location, AdditionalInfo = @AdditionalInfo WHERE Id = @Id",
                connection);
            
            command.Parameters.AddWithValue("@Id", person.Id);
            command.Parameters.AddWithValue("@Name", person.Name);
            command.Parameters.AddWithValue("@Phone", person.Phone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Location", person.Location ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@AdditionalInfo", person.AdditionalInfo ?? (object)DBNull.Value);
            
            command.ExecuteNonQuery();
        }

        public static void DeletePerson(int personId)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var transaction = connection.BeginTransaction();
            try
            {
                // TODO: إضافة نظام حفظ البيانات المحذوفة للتراجع لاحقاً

                // حذف جميع الديون المرتبطة بالشخص أولاً
                using var deleteDebtsCommand = new SqliteCommand("DELETE FROM Debts WHERE PersonId = @PersonId", connection, transaction);
                deleteDebtsCommand.Parameters.AddWithValue("@PersonId", personId);
                deleteDebtsCommand.ExecuteNonQuery();

                // حذف جميع المستندات المرتبطة بالشخص
                using var deleteDocsCommand = new SqliteCommand("DELETE FROM PdfDocuments WHERE PersonId = @PersonId", connection, transaction);
                deleteDocsCommand.Parameters.AddWithValue("@PersonId", personId);
                deleteDocsCommand.ExecuteNonQuery();

                // حذف الشخص
                using var deletePersonCommand = new SqliteCommand("DELETE FROM Persons WHERE Id = @Id", connection, transaction);
                deletePersonCommand.Parameters.AddWithValue("@Id", personId);

                deletePersonCommand.ExecuteNonQuery();

                transaction.Commit();
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        // CRUD operations for Debts
        public static List<Debt> GetAllDebts()
        {
            var debts = new List<Debt>();
            var persons = GetAllPersons().ToDictionary(p => p.Id, p => p.Name);
            
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();
            
            using var command = new SqliteCommand(@"
                SELECT d.*, p.Name as PersonName 
                FROM Debts d 
                LEFT JOIN Persons p ON d.PersonId = p.Id", connection);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                var debt = new Debt
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    PersonId = reader.GetInt32(reader.GetOrdinal("PersonId")),
                    Date = DateTime.Parse(reader.GetString(reader.GetOrdinal("Date"))),
                    Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                    IronCost = reader.GetDecimal(reader.GetOrdinal("IronCost")),
                    CuttingCost = reader.GetDecimal(reader.GetOrdinal("CuttingCost")),
                    BendingCost = reader.GetDecimal(reader.GetOrdinal("BendingCost")),
                    WeldingCost = reader.GetDecimal(reader.GetOrdinal("WeldingCost")),
                    IsSettled = reader.GetInt32(reader.GetOrdinal("IsSettled")) == 1,
                    Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description")),
                    LastUpdated = DateTime.Parse(reader.GetString(reader.GetOrdinal("LastUpdated"))),
                    PersonName = reader.IsDBNull(reader.GetOrdinal("PersonName")) ? "غير معروف" : reader.GetString(reader.GetOrdinal("PersonName"))
                };

                // تعيين تاريخ الاستحقاق
                var dueDateOrdinal = reader.GetOrdinal("DueDate");
                if (!reader.IsDBNull(dueDateOrdinal))
                {
                    debt.DueDate = DateTime.Parse(reader.GetString(dueDateOrdinal));
                }

                // تعيين تاريخ التسديد
                var settlementDateOrdinal = reader.GetOrdinal("SettlementDate");
                if (!reader.IsDBNull(settlementDateOrdinal))
                {
                    debt.SettlementDate = DateTime.Parse(reader.GetString(settlementDateOrdinal));
                }

                // تعيين الحقول الجديدة
                try
                {
                    var isConnectedOrdinal = reader.GetOrdinal("IsConnected");
                    if (!reader.IsDBNull(isConnectedOrdinal))
                    {
                        debt.IsConnected = reader.GetInt32(isConnectedOrdinal) == 1;
                    }
                }
                catch { debt.IsConnected = true; } // القيمة الافتراضية

                try
                {
                    var operationTypeOrdinal = reader.GetOrdinal("OperationType");
                    if (!reader.IsDBNull(operationTypeOrdinal))
                    {
                        debt.OperationType = reader.GetString(operationTypeOrdinal);
                    }
                }
                catch { debt.OperationType = "عادية"; } // القيمة الافتراضية

                try
                {
                    var notesOrdinal = reader.GetOrdinal("Notes");
                    if (!reader.IsDBNull(notesOrdinal))
                    {
                        debt.Notes = reader.GetString(notesOrdinal);
                    }
                }
                catch { debt.Notes = null; } // القيمة الافتراضية

                // تعيين تاريخ الدفع
                try
                {
                    var paymentDateOrdinal = reader.GetOrdinal("PaymentDate");
                    if (!reader.IsDBNull(paymentDateOrdinal))
                    {
                        debt.PaymentDate = DateTime.Parse(reader.GetString(paymentDateOrdinal));
                    }
                }
                catch { debt.PaymentDate = null; } // القيمة الافتراضية

                debts.Add(debt);
            }
            
            return debts;
        }

        private static void UpdateDebtsTableStructure(SqliteConnection connection)
        {
            try
            {
                // التحقق من وجود عمود PaymentDate
                var checkPaymentDateColumn = @"PRAGMA table_info(Debts)";
                using var checkCommand = new SqliteCommand(checkPaymentDateColumn, connection);
                using var reader = checkCommand.ExecuteReader();

                bool hasPaymentDate = false;
                while (reader.Read())
                {
                    var columnName = reader.GetString(1); // العمود الثاني يحتوي على اسم العمود
                    if (columnName == "PaymentDate")
                    {
                        hasPaymentDate = true;
                        break;
                    }
                }
                reader.Close();

                // إضافة عمود PaymentDate إذا لم يكن موجوداً
                if (!hasPaymentDate)
                {
                    var addPaymentDateColumn = @"ALTER TABLE Debts ADD COLUMN PaymentDate TEXT";
                    using var addCommand = new SqliteCommand(addPaymentDateColumn, connection);
                    addCommand.ExecuteNonQuery();
                    System.Diagnostics.Debug.WriteLine("تم إضافة عمود PaymentDate إلى جدول الديون");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث هيكل جدول الديون: {ex.Message}");
            }
        }

        public static bool UpdateDebt(Debt debt)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    UPDATE Debts SET
                        Amount = @Amount,
                        IronCost = @IronCost,
                        CuttingCost = @CuttingCost,
                        BendingCost = @BendingCost,
                        WeldingCost = @WeldingCost,
                        IsSettled = @IsSettled,
                        SettlementDate = @SettlementDate,
                        LastUpdated = @LastUpdated,
                        IsConnected = @IsConnected,
                        OperationType = @OperationType,
                        Notes = @Notes,
                        Description = @Description,
                        PdfFilePath = @PdfFilePath,
                        DueDate = @DueDate,
                        PaymentDate = @PaymentDate
                    WHERE Id = @Id", connection);

                command.Parameters.AddWithValue("@Id", debt.Id);
                command.Parameters.AddWithValue("@Amount", debt.Amount);
                command.Parameters.AddWithValue("@IronCost", debt.IronCost);
                command.Parameters.AddWithValue("@CuttingCost", debt.CuttingCost);
                command.Parameters.AddWithValue("@BendingCost", debt.BendingCost);
                command.Parameters.AddWithValue("@WeldingCost", debt.WeldingCost);
                command.Parameters.AddWithValue("@IsSettled", debt.IsSettled ? 1 : 0);
                command.Parameters.AddWithValue("@SettlementDate", debt.SettlementDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PaymentDate", debt.PaymentDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LastUpdated", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@IsConnected", debt.IsConnected ? 1 : 0);
                command.Parameters.AddWithValue("@OperationType", debt.OperationType ?? "عادية");
                command.Parameters.AddWithValue("@Notes", debt.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Description", debt.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PdfFilePath", debt.PdfFilePath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@DueDate", debt.DueDate.ToString("yyyy-MM-dd HH:mm:ss"));

                int rowsAffected = command.ExecuteNonQuery();
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الدين: {ex.Message}");
                return false;
            }
        }

        public static void AddDebt(Debt debt)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand(@"
                INSERT INTO Debts (
                    PersonId, Date, Amount, IronCost, CuttingCost, BendingCost, WeldingCost,
                    IsSettled, Description, PdfFilePath, DueDate, SettlementDate, LastUpdated,
                    IsConnected, OperationType, Notes
                ) VALUES (
                    @PersonId, @Date, @Amount, @IronCost, @CuttingCost, @BendingCost, @WeldingCost,
                    @IsSettled, @Description, @PdfFilePath, @DueDate, @SettlementDate, @LastUpdated,
                    @IsConnected, @OperationType, @Notes
                )", connection);

            command.Parameters.AddWithValue("@PersonId", debt.PersonId);
            command.Parameters.AddWithValue("@Date", debt.Date.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@Amount", debt.Amount);
            command.Parameters.AddWithValue("@IronCost", debt.IronCost);
            command.Parameters.AddWithValue("@CuttingCost", debt.CuttingCost);
            command.Parameters.AddWithValue("@BendingCost", debt.BendingCost);
            command.Parameters.AddWithValue("@WeldingCost", debt.WeldingCost);
            command.Parameters.AddWithValue("@IsSettled", debt.IsSettled ? 1 : 0);
            command.Parameters.AddWithValue("@Description", debt.Description ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@PdfFilePath", debt.PdfFilePath ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@DueDate", debt.DueDate.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@SettlementDate", debt.SettlementDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@LastUpdated", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@IsConnected", debt.IsConnected ? 1 : 0);
            command.Parameters.AddWithValue("@OperationType", debt.OperationType ?? "عادية");
            command.Parameters.AddWithValue("@Notes", debt.Notes ?? (object)DBNull.Value);

            command.ExecuteNonQuery();

            // الحصول على ID الجديد
            command.CommandText = "SELECT last_insert_rowid()";
            debt.Id = Convert.ToInt32(command.ExecuteScalar());
        }

        public static List<Debt> GetDebtsByPersonId(int personId)
        {
            var debts = new List<Debt>();
            
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();
            
            using var command = new SqliteCommand(@"
                SELECT d.*, p.Name as PersonName 
                FROM Debts d 
                LEFT JOIN Persons p ON d.PersonId = p.Id 
                WHERE d.PersonId = @PersonId 
                ORDER BY d.Date DESC", connection);
            
            command.Parameters.AddWithValue("@PersonId", personId);
            
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                var debt = new Debt
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    PersonId = reader.GetInt32(reader.GetOrdinal("PersonId")),
                    Date = DateTime.Parse(reader.GetString(reader.GetOrdinal("Date"))),
                    Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                    IronCost = reader.GetDecimal(reader.GetOrdinal("IronCost")),
                    CuttingCost = reader.GetDecimal(reader.GetOrdinal("CuttingCost")),
                    BendingCost = reader.GetDecimal(reader.GetOrdinal("BendingCost")),
                    WeldingCost = reader.GetDecimal(reader.GetOrdinal("WeldingCost")),
                    IsSettled = reader.GetInt32(reader.GetOrdinal("IsSettled")) == 1,
                    Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description")),
                    LastUpdated = DateTime.Parse(reader.GetString(reader.GetOrdinal("LastUpdated"))),
                    PersonName = reader.IsDBNull(reader.GetOrdinal("PersonName")) ? "غير معروف" : reader.GetString(reader.GetOrdinal("PersonName"))
                };

                // تعيين تاريخ الاستحقاق
                var dueDateOrdinal = reader.GetOrdinal("DueDate");
                if (!reader.IsDBNull(dueDateOrdinal))
                {
                    debt.DueDate = DateTime.Parse(reader.GetString(dueDateOrdinal));
                }

                // تعيين تاريخ التسديد
                var settlementDateOrdinal = reader.GetOrdinal("SettlementDate");
                if (!reader.IsDBNull(settlementDateOrdinal))
                {
                    debt.SettlementDate = DateTime.Parse(reader.GetString(settlementDateOrdinal));
                }

                // تعيين الحقول الجديدة
                try
                {
                    var isConnectedOrdinal = reader.GetOrdinal("IsConnected");
                    if (!reader.IsDBNull(isConnectedOrdinal))
                    {
                        debt.IsConnected = reader.GetInt32(isConnectedOrdinal) == 1;
                    }
                }
                catch { debt.IsConnected = true; }

                try
                {
                    var operationTypeOrdinal = reader.GetOrdinal("OperationType");
                    if (!reader.IsDBNull(operationTypeOrdinal))
                    {
                        debt.OperationType = reader.GetString(operationTypeOrdinal);
                    }
                }
                catch { debt.OperationType = "عادية"; }

                try
                {
                    var notesOrdinal = reader.GetOrdinal("Notes");
                    if (!reader.IsDBNull(notesOrdinal))
                    {
                        debt.Notes = reader.GetString(notesOrdinal);
                    }
                }
                catch { debt.Notes = null; }

                try
                {
                    var pdfFilePathOrdinal = reader.GetOrdinal("PdfFilePath");
                    if (!reader.IsDBNull(pdfFilePathOrdinal))
                    {
                        debt.PdfFilePath = reader.GetString(pdfFilePathOrdinal);
                    }
                }
                catch { debt.PdfFilePath = null; }

                debts.Add(debt);
            }
            
            return debts;
        }

        public static Debt? GetDebtById(int debtId)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand(@"
                SELECT d.*, p.Name as PersonName
                FROM Debts d
                LEFT JOIN Persons p ON d.PersonId = p.Id
                WHERE d.Id = @DebtId",
                connection);

            command.Parameters.AddWithValue("@DebtId", debtId);
            using var reader = command.ExecuteReader();

            if (reader.Read())
            {
                var debt = new Debt
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    PersonId = reader.GetInt32(reader.GetOrdinal("PersonId")),
                    Date = DateTime.Parse(reader.GetString(reader.GetOrdinal("Date"))),
                    Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                    IronCost = reader.GetDecimal(reader.GetOrdinal("IronCost")),
                    CuttingCost = reader.GetDecimal(reader.GetOrdinal("CuttingCost")),
                    BendingCost = reader.GetDecimal(reader.GetOrdinal("BendingCost")),
                    WeldingCost = reader.GetDecimal(reader.GetOrdinal("WeldingCost")),
                    IsSettled = reader.GetInt32(reader.GetOrdinal("IsSettled")) == 1,
                    Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description")),
                    LastUpdated = DateTime.Parse(reader.GetString(reader.GetOrdinal("LastUpdated"))),
                    PersonName = reader.IsDBNull(reader.GetOrdinal("PersonName")) ? "غير معروف" : reader.GetString(reader.GetOrdinal("PersonName"))
                };

                // تعيين تاريخ الاستحقاق
                var dueDateOrdinal = reader.GetOrdinal("DueDate");
                if (!reader.IsDBNull(dueDateOrdinal))
                {
                    debt.DueDate = DateTime.Parse(reader.GetString(dueDateOrdinal));
                }

                // تعيين تاريخ التسديد
                var settlementDateOrdinal = reader.GetOrdinal("SettlementDate");
                if (!reader.IsDBNull(settlementDateOrdinal))
                {
                    debt.SettlementDate = DateTime.Parse(reader.GetString(settlementDateOrdinal));
                }

                // تعيين الحقول الإضافية
                try
                {
                    var isConnectedOrdinal = reader.GetOrdinal("IsConnected");
                    if (!reader.IsDBNull(isConnectedOrdinal))
                    {
                        debt.IsConnected = reader.GetInt32(isConnectedOrdinal) == 1;
                    }
                }
                catch { debt.IsConnected = true; }

                try
                {
                    var operationTypeOrdinal = reader.GetOrdinal("OperationType");
                    if (!reader.IsDBNull(operationTypeOrdinal))
                    {
                        debt.OperationType = reader.GetString(operationTypeOrdinal);
                    }
                }
                catch { debt.OperationType = "عادية"; }

                try
                {
                    var notesOrdinal = reader.GetOrdinal("Notes");
                    if (!reader.IsDBNull(notesOrdinal))
                    {
                        debt.Notes = reader.GetString(notesOrdinal);
                    }
                }
                catch { debt.Notes = null; }

                try
                {
                    var pdfFilePathOrdinal = reader.GetOrdinal("PdfFilePath");
                    if (!reader.IsDBNull(pdfFilePathOrdinal))
                    {
                        debt.PdfFilePath = reader.GetString(pdfFilePathOrdinal);
                    }
                }
                catch { debt.PdfFilePath = null; }

                return debt;
            }

            return null;
        }

        public static List<Debt> GetOverdueDebts()
        {
            var overdueDebts = new List<Debt>();
            var currentDate = DateTime.Now;
            
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();
            
            using var command = new SqliteCommand(@"
                SELECT d.*, p.Name as PersonName 
                FROM Debts d 
                LEFT JOIN Persons p ON d.PersonId = p.Id 
                WHERE d.IsSettled = 0 AND d.DueDate < @CurrentDate",
                connection);
            
            command.Parameters.AddWithValue("@CurrentDate", currentDate.ToString("yyyy-MM-dd"));
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                var debt = new Debt
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    PersonId = reader.GetInt32(reader.GetOrdinal("PersonId")),
                    Date = DateTime.Parse(reader.GetString(reader.GetOrdinal("Date"))),
                    Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                    IronCost = reader.GetDecimal(reader.GetOrdinal("IronCost")),
                    CuttingCost = reader.GetDecimal(reader.GetOrdinal("CuttingCost")),
                    BendingCost = reader.GetDecimal(reader.GetOrdinal("BendingCost")),
                    WeldingCost = reader.GetDecimal(reader.GetOrdinal("WeldingCost")),
                    IsSettled = reader.GetInt32(reader.GetOrdinal("IsSettled")) == 1,
                    Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description")),
                    LastUpdated = DateTime.Parse(reader.GetString(reader.GetOrdinal("LastUpdated"))),
                    PersonName = reader.IsDBNull(reader.GetOrdinal("PersonName")) ? "غير معروف" : reader.GetString(reader.GetOrdinal("PersonName"))
                };

                // تعيين تاريخ الاستحقاق
                var dueDateOrdinal = reader.GetOrdinal("DueDate");
                if (!reader.IsDBNull(dueDateOrdinal))
                {
                    debt.DueDate = DateTime.Parse(reader.GetString(dueDateOrdinal));
                }

                overdueDebts.Add(debt);
            }
            
            return overdueDebts;
        }

        // PDF Documents operations
        public static List<PdfDocument> GetPdfDocumentsByPersonId(int personId)
        {
            var documents = new List<PdfDocument>();

            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand(@"
                SELECT * FROM PdfDocuments
                WHERE PersonId = @PersonId
                ORDER BY UploadDate DESC", connection);

            command.Parameters.AddWithValue("@PersonId", personId);

            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                var document = new PdfDocument
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    PersonId = reader.GetInt32(reader.GetOrdinal("PersonId")),
                    FileName = reader.GetString(reader.GetOrdinal("FileName")),
                    FilePath = reader.GetString(reader.GetOrdinal("FilePath")),
                    Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? string.Empty : reader.GetString(reader.GetOrdinal("Description")),
                    FileSize = reader.GetInt64(reader.GetOrdinal("FileSize")),
                    UploadDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("UploadDate"))),
                    LastModified = DateTime.Parse(reader.GetString(reader.GetOrdinal("LastModified")))
                };

                documents.Add(document);
            }

            return documents;
        }

        public static void AddPdfDocument(PdfDocument document)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand(@"
                INSERT INTO PdfDocuments (
                    PersonId, FileName, FilePath, Description, FileSize, UploadDate, LastModified
                ) VALUES (
                    @PersonId, @FileName, @FilePath, @Description, @FileSize, @UploadDate, @LastModified
                )", connection);

            command.Parameters.AddWithValue("@PersonId", document.PersonId);
            command.Parameters.AddWithValue("@FileName", document.FileName);
            command.Parameters.AddWithValue("@FilePath", document.FilePath);
            command.Parameters.AddWithValue("@Description", document.Description ?? string.Empty);
            command.Parameters.AddWithValue("@FileSize", document.FileSize);
            command.Parameters.AddWithValue("@UploadDate", document.UploadDate.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@LastModified", document.LastModified.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();

            // الحصول على ID الجديد
            command.CommandText = "SELECT last_insert_rowid()";
            document.Id = Convert.ToInt32(command.ExecuteScalar());
        }

        public static void UpdatePdfDocument(PdfDocument document)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand(@"
                UPDATE PdfDocuments SET
                    FileName = @FileName,
                    FilePath = @FilePath,
                    Description = @Description,
                    FileSize = @FileSize,
                    LastModified = @LastModified
                WHERE Id = @Id", connection);

            command.Parameters.AddWithValue("@Id", document.Id);
            command.Parameters.AddWithValue("@FileName", document.FileName);
            command.Parameters.AddWithValue("@FilePath", document.FilePath);
            command.Parameters.AddWithValue("@Description", document.Description ?? string.Empty);
            command.Parameters.AddWithValue("@FileSize", document.FileSize);
            command.Parameters.AddWithValue("@LastModified", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();
        }

        public static void DeletePdfDocument(int documentId)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand("DELETE FROM PdfDocuments WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", documentId);

            command.ExecuteNonQuery();
        }

        // Cutting Details operations
        public static List<CuttingDetails> GetCuttingDetailsByDebtId(int debtId)
        {
            var details = new List<CuttingDetails>();

            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand(@"
                SELECT * FROM CuttingDetails
                WHERE DebtId = @DebtId
                ORDER BY CreatedDate DESC", connection);

            command.Parameters.AddWithValue("@DebtId", debtId);

            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                var detail = new CuttingDetails
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    CuttingDistanceMm = reader.GetDecimal(reader.GetOrdinal("CuttingDistanceMm")),
                    PricePerMeter = reader.GetDecimal(reader.GetOrdinal("PricePerMeter")),
                    IronThickness = reader.GetDecimal(reader.GetOrdinal("IronThickness")),
                    Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? string.Empty : reader.GetString(reader.GetOrdinal("Notes")),
                    CreatedDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("CreatedDate")))
                };

                details.Add(detail);
            }

            return details;
        }

        public static void AddCuttingDetails(CuttingDetails details, int debtId)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand(@"
                INSERT INTO CuttingDetails (
                    DebtId, CuttingDistanceMm, PricePerMeter, IronThickness, TotalCuttingCost, Notes, CreatedDate
                ) VALUES (
                    @DebtId, @CuttingDistanceMm, @PricePerMeter, @IronThickness, @TotalCuttingCost, @Notes, @CreatedDate
                )", connection);

            command.Parameters.AddWithValue("@DebtId", debtId);
            command.Parameters.AddWithValue("@CuttingDistanceMm", details.CuttingDistanceMm);
            command.Parameters.AddWithValue("@PricePerMeter", details.PricePerMeter);
            command.Parameters.AddWithValue("@IronThickness", details.IronThickness);
            command.Parameters.AddWithValue("@TotalCuttingCost", details.TotalCuttingCost);
            command.Parameters.AddWithValue("@Notes", details.Notes ?? string.Empty);
            command.Parameters.AddWithValue("@CreatedDate", details.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));

            command.ExecuteNonQuery();

            // الحصول على ID الجديد
            command.CommandText = "SELECT last_insert_rowid()";
            details.Id = Convert.ToInt32(command.ExecuteScalar());
        }

        public static void DeleteDebt(int debtId)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand("DELETE FROM Debts WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", debtId);

            command.ExecuteNonQuery();
        }

        #region Backup and Restore Methods

        /// <summary>
        /// مسح جميع البيانات من قاعدة البيانات
        /// </summary>
        public static void ClearAllData()
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                using var transaction = connection.BeginTransaction();
                try
                {
                    // حذف البيانات بالترتيب الصحيح (بسبب المفاتيح الخارجية)
                    var commands = new[]
                    {
                        "DELETE FROM Salaries",
                        "DELETE FROM PdfDocuments",
                        "DELETE FROM IronDetails",
                        "DELETE FROM CuttingDetails",
                        "DELETE FROM Debts",
                        "DELETE FROM Persons"
                    };

                    foreach (var commandText in commands)
                    {
                        using var command = new SqliteCommand(commandText, connection, transaction);
                        command.ExecuteNonQuery();
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في مسح البيانات: {ex.Message}");
            }
        }

        #endregion

        #region Database Update Methods

        private static void UpdateSalariesTableStructure(SqliteConnection connection)
        {
            try
            {
                // التحقق من وجود الأعمدة الجديدة
                var checkColumnQuery = "PRAGMA table_info(Salaries)";
                using var checkCommand = new SqliteCommand(checkColumnQuery, connection);
                using var reader = checkCommand.ExecuteReader();

                bool hasFromDate = false;
                bool hasToDate = false;

                while (reader.Read())
                {
                    var columnName = reader.GetString(1); // العمود الثاني يحتوي على اسم العمود
                    if (columnName == "FromDate") hasFromDate = true;
                    if (columnName == "ToDate") hasToDate = true;
                }
                reader.Close();

                // إضافة الأعمدة المفقودة
                if (!hasFromDate)
                {
                    var addFromDateColumn = "ALTER TABLE Salaries ADD COLUMN FromDate TEXT DEFAULT ''";
                    using var addCommand1 = new SqliteCommand(addFromDateColumn, connection);
                    addCommand1.ExecuteNonQuery();
                }

                if (!hasToDate)
                {
                    var addToDateColumn = "ALTER TABLE Salaries ADD COLUMN ToDate TEXT DEFAULT ''";
                    using var addCommand2 = new SqliteCommand(addToDateColumn, connection);
                    addCommand2.ExecuteNonQuery();
                }

                // تحديث السجلات الموجودة بتواريخ افتراضية إذا كانت فارغة
                if (!hasFromDate || !hasToDate)
                {
                    var updateQuery = @"UPDATE Salaries
                                       SET FromDate = CASE WHEN FromDate = '' OR FromDate IS NULL THEN date(PaymentDate) ELSE FromDate END,
                                           ToDate = CASE WHEN ToDate = '' OR ToDate IS NULL THEN date(PaymentDate) ELSE ToDate END
                                       WHERE FromDate = '' OR FromDate IS NULL OR ToDate = '' OR ToDate IS NULL";
                    using var updateCommand = new SqliteCommand(updateQuery, connection);
                    updateCommand.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، نتجاهل التحديث ونستمر
                Console.WriteLine($"خطأ في تحديث هيكل جدول الرواتب: {ex.Message}");
            }
        }

        #endregion

        #region Salary Management

        public static void AddSalary(Salary salary)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                // التأكد من تحديث هيكل الجدول
                UpdateSalariesTableStructure(connection);

                using var command = new SqliteCommand(@"
                    INSERT INTO Salaries (
                        WorkerId, WorkerName, PaymentDate, FromDate, ToDate, DailyWage, WorkDays, AbsenceDays,
                        OvertimeHours, OvertimeRate, Deductions, Bonuses, TotalAmount, Notes
                    ) VALUES (
                        @WorkerId, @WorkerName, @PaymentDate, @FromDate, @ToDate, @DailyWage, @WorkDays, @AbsenceDays,
                        @OvertimeHours, @OvertimeRate, @Deductions, @Bonuses, @TotalAmount, @Notes
                    )", connection);

                command.Parameters.AddWithValue("@WorkerId", salary.WorkerId);
                command.Parameters.AddWithValue("@WorkerName", salary.WorkerName);
                command.Parameters.AddWithValue("@PaymentDate", salary.PaymentDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@FromDate", salary.FromDate.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@ToDate", salary.ToDate.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@DailyWage", salary.DailyWage);
                command.Parameters.AddWithValue("@WorkDays", salary.WorkDays);
                command.Parameters.AddWithValue("@AbsenceDays", salary.AbsenceDays);
                command.Parameters.AddWithValue("@OvertimeHours", salary.OvertimeHours);
                command.Parameters.AddWithValue("@OvertimeRate", salary.OvertimeRate);
                command.Parameters.AddWithValue("@Deductions", salary.Deductions);
                command.Parameters.AddWithValue("@Bonuses", salary.Bonuses);
                command.Parameters.AddWithValue("@TotalAmount", salary.TotalAmount);
                command.Parameters.AddWithValue("@Notes", salary.Notes ?? "");

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الراتب: {ex.Message}", ex);
            }
        }

        public static List<Salary> GetAllSalaries()
        {
            var salaries = new List<Salary>();
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                // التأكد من تحديث هيكل الجدول
                UpdateSalariesTableStructure(connection);

                using var command = new SqliteCommand("SELECT * FROM Salaries ORDER BY PaymentDate DESC", connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var salary = new Salary
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("Id")),
                        WorkerId = reader.GetInt32(reader.GetOrdinal("WorkerId")),
                        WorkerName = reader.GetString(reader.GetOrdinal("WorkerName")),
                        PaymentDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("PaymentDate"))),
                        DailyWage = reader.GetDecimal(reader.GetOrdinal("DailyWage")),
                        WorkDays = reader.GetInt32(reader.GetOrdinal("WorkDays")),
                        AbsenceDays = reader.GetInt32(reader.GetOrdinal("AbsenceDays")),
                        OvertimeHours = reader.GetDecimal(reader.GetOrdinal("OvertimeHours")),
                        OvertimeRate = reader.GetDecimal(reader.GetOrdinal("OvertimeRate")),
                        Deductions = reader.GetDecimal(reader.GetOrdinal("Deductions")),
                        Bonuses = reader.GetDecimal(reader.GetOrdinal("Bonuses")),
                        TotalAmount = reader.GetDecimal(reader.GetOrdinal("TotalAmount")),
                        Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? "" : reader.GetString(reader.GetOrdinal("Notes"))
                    };

                    // التعامل مع الأعمدة الجديدة بحذر
                    try
                    {
                        var fromDateOrdinal = reader.GetOrdinal("FromDate");
                        var toDateOrdinal = reader.GetOrdinal("ToDate");

                        if (!reader.IsDBNull(fromDateOrdinal) && !string.IsNullOrEmpty(reader.GetString(fromDateOrdinal)))
                        {
                            salary.FromDate = DateTime.Parse(reader.GetString(fromDateOrdinal));
                        }
                        else
                        {
                            salary.FromDate = salary.PaymentDate.Date;
                        }

                        if (!reader.IsDBNull(toDateOrdinal) && !string.IsNullOrEmpty(reader.GetString(toDateOrdinal)))
                        {
                            salary.ToDate = DateTime.Parse(reader.GetString(toDateOrdinal));
                        }
                        else
                        {
                            salary.ToDate = salary.PaymentDate.Date;
                        }
                    }
                    catch
                    {
                        // إذا لم توجد الأعمدة، استخدم تاريخ الدفع
                        salary.FromDate = salary.PaymentDate.Date;
                        salary.ToDate = salary.PaymentDate.Date;
                    }

                    salaries.Add(salary);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الرواتب: {ex.Message}", ex);
            }

            return salaries;
        }

        public static List<Salary> GetSalariesByWorkerId(int workerId)
        {
            var salaries = new List<Salary>();
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                // التأكد من تحديث هيكل الجدول
                UpdateSalariesTableStructure(connection);

                using var command = new SqliteCommand("SELECT * FROM Salaries WHERE WorkerId = @WorkerId ORDER BY PaymentDate DESC", connection);
                command.Parameters.AddWithValue("@WorkerId", workerId);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var salary = new Salary
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("Id")),
                        WorkerId = reader.GetInt32(reader.GetOrdinal("WorkerId")),
                        WorkerName = reader.GetString(reader.GetOrdinal("WorkerName")),
                        PaymentDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("PaymentDate"))),
                        DailyWage = reader.GetDecimal(reader.GetOrdinal("DailyWage")),
                        WorkDays = reader.GetInt32(reader.GetOrdinal("WorkDays")),
                        AbsenceDays = reader.GetInt32(reader.GetOrdinal("AbsenceDays")),
                        OvertimeHours = reader.GetDecimal(reader.GetOrdinal("OvertimeHours")),
                        OvertimeRate = reader.GetDecimal(reader.GetOrdinal("OvertimeRate")),
                        Deductions = reader.GetDecimal(reader.GetOrdinal("Deductions")),
                        Bonuses = reader.GetDecimal(reader.GetOrdinal("Bonuses")),
                        TotalAmount = reader.GetDecimal(reader.GetOrdinal("TotalAmount")),
                        Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? "" : reader.GetString(reader.GetOrdinal("Notes"))
                    };

                    // التعامل مع الأعمدة الجديدة بحذر
                    try
                    {
                        var fromDateOrdinal = reader.GetOrdinal("FromDate");
                        var toDateOrdinal = reader.GetOrdinal("ToDate");

                        if (!reader.IsDBNull(fromDateOrdinal) && !string.IsNullOrEmpty(reader.GetString(fromDateOrdinal)))
                        {
                            salary.FromDate = DateTime.Parse(reader.GetString(fromDateOrdinal));
                        }
                        else
                        {
                            salary.FromDate = salary.PaymentDate.Date;
                        }

                        if (!reader.IsDBNull(toDateOrdinal) && !string.IsNullOrEmpty(reader.GetString(toDateOrdinal)))
                        {
                            salary.ToDate = DateTime.Parse(reader.GetString(toDateOrdinal));
                        }
                        else
                        {
                            salary.ToDate = salary.PaymentDate.Date;
                        }
                    }
                    catch
                    {
                        // إذا لم توجد الأعمدة، استخدم تاريخ الدفع
                        salary.FromDate = salary.PaymentDate.Date;
                        salary.ToDate = salary.PaymentDate.Date;
                    }

                    salaries.Add(salary);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع رواتب العامل: {ex.Message}", ex);
            }

            return salaries;
        }

        public static List<string> GetWorkersWithSalaries()
        {
            var workers = new List<string>();
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand("SELECT DISTINCT WorkerName FROM Salaries ORDER BY WorkerName", connection);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                workers.Add(reader.GetString(reader.GetOrdinal("WorkerName")));
            }

            return workers;
        }

        #endregion

        #region Factory Debts Management

        public static void AddFactoryDebt(FactoryDebt debt)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    INSERT INTO FactoryDebts (
                        PersonId, PersonName, Amount, DebtDate, InvoiceType, Description, IsPaid, AttachmentPath
                    ) VALUES (
                        @PersonId, @PersonName, @Amount, @DebtDate, @InvoiceType, @Description, @IsPaid, @AttachmentPath
                    )", connection);

                command.Parameters.AddWithValue("@PersonId", debt.PersonId);
                command.Parameters.AddWithValue("@PersonName", debt.PersonName);
                command.Parameters.AddWithValue("@Amount", debt.Amount);
                command.Parameters.AddWithValue("@DebtDate", debt.DebtDate.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@InvoiceType", debt.InvoiceType);
                command.Parameters.AddWithValue("@Description", debt.Description ?? "");
                command.Parameters.AddWithValue("@IsPaid", debt.IsPaid ? 1 : 0);
                command.Parameters.AddWithValue("@AttachmentPath", debt.AttachmentPath ?? "");

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة دين المعمل: {ex.Message}", ex);
            }
        }

        public static void UpdateFactoryDebt(FactoryDebt debt)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    UPDATE FactoryDebts SET
                        PersonId = @PersonId, PersonName = @PersonName, Amount = @Amount,
                        DebtDate = @DebtDate, InvoiceType = @InvoiceType, Description = @Description,
                        IsPaid = @IsPaid, AttachmentPath = @AttachmentPath
                    WHERE Id = @Id", connection);

                command.Parameters.AddWithValue("@Id", debt.Id);
                command.Parameters.AddWithValue("@PersonId", debt.PersonId);
                command.Parameters.AddWithValue("@PersonName", debt.PersonName);
                command.Parameters.AddWithValue("@Amount", debt.Amount);
                command.Parameters.AddWithValue("@DebtDate", debt.DebtDate.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@InvoiceType", debt.InvoiceType);
                command.Parameters.AddWithValue("@Description", debt.Description ?? "");
                command.Parameters.AddWithValue("@IsPaid", debt.IsPaid ? 1 : 0);
                command.Parameters.AddWithValue("@AttachmentPath", debt.AttachmentPath ?? "");

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث دين المعمل: {ex.Message}", ex);
            }
        }

        public static void DeleteFactoryDebt(int debtId)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand("DELETE FROM FactoryDebts WHERE Id = @Id", connection);
                command.Parameters.AddWithValue("@Id", debtId);
                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف دين المعمل: {ex.Message}", ex);
            }
        }

        public static List<FactoryDebt> GetAllFactoryDebts()
        {
            var debts = new List<FactoryDebt>();
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand("SELECT * FROM FactoryDebts ORDER BY DebtDate DESC", connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    debts.Add(new FactoryDebt
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("Id")),
                        PersonId = reader.GetInt32(reader.GetOrdinal("PersonId")),
                        PersonName = reader.GetString(reader.GetOrdinal("PersonName")),
                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                        DebtDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("DebtDate"))),
                        InvoiceType = reader.GetString(reader.GetOrdinal("InvoiceType")),
                        Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? "" : reader.GetString(reader.GetOrdinal("Description")),
                        IsPaid = reader.GetInt32(reader.GetOrdinal("IsPaid")) == 1,
                        AttachmentPath = reader.IsDBNull(reader.GetOrdinal("AttachmentPath")) ? "" : reader.GetString(reader.GetOrdinal("AttachmentPath")),
                        CreatedDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("CreatedDate")))
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع ديون المعمل: {ex.Message}", ex);
            }

            return debts;
        }

        public static List<FactoryDebt> GetFactoryDebtsByPersonId(int personId)
        {
            var debts = new List<FactoryDebt>();
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand("SELECT * FROM FactoryDebts WHERE PersonId = @PersonId ORDER BY DebtDate DESC", connection);
                command.Parameters.AddWithValue("@PersonId", personId);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    debts.Add(new FactoryDebt
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("Id")),
                        PersonId = reader.GetInt32(reader.GetOrdinal("PersonId")),
                        PersonName = reader.GetString(reader.GetOrdinal("PersonName")),
                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                        DebtDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("DebtDate"))),
                        InvoiceType = reader.GetString(reader.GetOrdinal("InvoiceType")),
                        Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? "" : reader.GetString(reader.GetOrdinal("Description")),
                        IsPaid = reader.GetInt32(reader.GetOrdinal("IsPaid")) == 1,
                        AttachmentPath = reader.IsDBNull(reader.GetOrdinal("AttachmentPath")) ? "" : reader.GetString(reader.GetOrdinal("AttachmentPath")),
                        CreatedDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("CreatedDate")))
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع ديون الشخص: {ex.Message}", ex);
            }

            return debts;
        }

        public static List<FactoryDebtSummary> GetFactoryDebtsSummary()
        {
            var summaries = new List<FactoryDebtSummary>();
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    SELECT
                        PersonId, PersonName,
                        SUM(CASE WHEN IsPaid = 0 THEN Amount ELSE 0 END) as TotalDebt,
                        COUNT(*) as InvoiceCount,
                        SUM(CASE WHEN IsPaid = 1 THEN 1 ELSE 0 END) as PaidInvoices,
                        SUM(CASE WHEN IsPaid = 0 THEN 1 ELSE 0 END) as UnpaidInvoices,
                        MAX(DebtDate) as LastDebtDate
                    FROM FactoryDebts
                    GROUP BY PersonId, PersonName
                    HAVING TotalDebt > 0
                    ORDER BY TotalDebt DESC", connection);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    summaries.Add(new FactoryDebtSummary
                    {
                        PersonId = reader.GetInt32(reader.GetOrdinal("PersonId")),
                        PersonName = reader.GetString(reader.GetOrdinal("PersonName")),
                        TotalDebt = reader.GetDecimal(reader.GetOrdinal("TotalDebt")),
                        InvoiceCount = reader.GetInt32(reader.GetOrdinal("InvoiceCount")),
                        PaidInvoices = reader.GetInt32(reader.GetOrdinal("PaidInvoices")),
                        UnpaidInvoices = reader.GetInt32(reader.GetOrdinal("UnpaidInvoices")),
                        LastDebtDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("LastDebtDate")))
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع ملخص ديون المعمل: {ex.Message}", ex);
            }

            return summaries;
        }

        #endregion

        #region Activities Management

        // دوال إدارة الأنشطة
        public static void AddActivity(string description, string activityType = "General")
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();
                using var command = new SqliteCommand(@"
                    INSERT INTO Activities (Description, ActivityDate, ActivityType)
                    VALUES (@Description, @ActivityDate, @ActivityType)", connection);

                command.Parameters.AddWithValue("@Description", description);
                command.Parameters.AddWithValue("@ActivityDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@ActivityType", activityType);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة النشاط: {ex.Message}");
            }
        }

        public static List<(int Id, string Description, DateTime Date, string Type)> GetRecentActivities(int count = 10)
        {
            var activities = new List<(int Id, string Description, DateTime Date, string Type)>();

            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();
                using var command = new SqliteCommand(@"
                    SELECT Id, Description, ActivityDate, ActivityType
                    FROM Activities
                    ORDER BY ActivityDate DESC
                    LIMIT @Count", connection);

                command.Parameters.AddWithValue("@Count", count);

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    var id = reader.GetInt32(reader.GetOrdinal("Id"));
                    var description = reader.GetString(reader.GetOrdinal("Description"));
                    var dateStr = reader.GetString(reader.GetOrdinal("ActivityDate"));
                    var type = reader.GetString(reader.GetOrdinal("ActivityType"));

                    if (DateTime.TryParse(dateStr, out DateTime date))
                    {
                        activities.Add((id, description, date, type));
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في قراءة الأنشطة: {ex.Message}");
            }

            return activities;
        }

        public static void DeleteActivity(int activityId)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
                connection.Open();
                using var command = new SqliteCommand(@"
                    DELETE FROM Activities WHERE Id = @Id", connection);

                command.Parameters.AddWithValue("@Id", activityId);
                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف النشاط: {ex.Message}");
            }
        }

        // TODO: إضافة دوال حفظ واسترداد البيانات المحذوفة لاحقاً

        #endregion

        #region Network Sync Support

        // دوال إضافية للمزامنة
        public static Person? GetPersonById(int id)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand("SELECT * FROM Persons WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);

            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new Person
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Phone = reader.IsDBNull("Phone") ? "" : reader.GetString("Phone"),
                    Address = reader.IsDBNull("Address") ? "" : reader.GetString("Address"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"),
                    LastUpdated = reader.IsDBNull("LastUpdated") ? DateTime.Now : DateTime.Parse(reader.GetString("LastUpdated"))
                };
            }
            return null;
        }



        public static Worker? GetWorkerById(int id)
        {
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand("SELECT * FROM Workers WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);

            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new Worker
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    JobTitle = reader.IsDBNull("JobTitle") ? "" : reader.GetString("JobTitle"),
                    DailyWage = reader.GetDecimal("DailyWage"),
                    Phone = reader.IsDBNull("Phone") ? "" : reader.GetString("Phone"),
                    HireDate = DateTime.Parse(reader.GetString("HireDate")),
                    IsActive = reader.GetBoolean("IsActive"),
                    LastUpdated = reader.IsDBNull("LastUpdated") ? DateTime.Now : DateTime.Parse(reader.GetString("LastUpdated"))
                };
            }
            return null;
        }

        public static List<SalaryPayment> GetAllSalaryPayments()
        {
            var salaries = new List<SalaryPayment>();
            using var connection = new SqliteConnection($"Data Source={DatabaseFile};");
            connection.Open();

            using var command = new SqliteCommand("SELECT * FROM SalaryPayments ORDER BY PaymentDate DESC", connection);

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                salaries.Add(new SalaryPayment
                {
                    Id = reader.GetInt32("Id"),
                    WorkerId = reader.GetInt32("WorkerId"),
                    WorkerName = reader.IsDBNull("WorkerName") ? "" : reader.GetString("WorkerName"),
                    Amount = reader.GetDecimal("Amount"),
                    PaymentDate = DateTime.Parse(reader.GetString("PaymentDate")),
                    FromDate = DateTime.Parse(reader.GetString("FromDate")),
                    ToDate = DateTime.Parse(reader.GetString("ToDate")),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                });
            }
            return salaries;
        }

        #endregion
    }
}
