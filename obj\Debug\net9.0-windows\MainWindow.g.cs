﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7CDB79FF626C947D1452B1352921BD8E6309268B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DebtManagementApp;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeDisplay;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NavigationButtons;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDashboard;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPersons;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnWorkers;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDebts;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnFactoryDebts;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOverdue;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReports;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBackup;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnIronCalc;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCuttingCalc;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReminders;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSearch;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSettings;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContentTitle;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl MainContent;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPersonsCount;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDebtsCount;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueDebtsCount;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountDisplay;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView RecentActivitiesList;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContextMenu ActivityContextMenu;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TimeDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.NavigationButtons = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.BtnDashboard = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\MainWindow.xaml"
            this.BtnDashboard.Click += new System.Windows.RoutedEventHandler(this.ReturnToDashboard_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnPersons = ((System.Windows.Controls.Button)(target));
            
            #line 105 "..\..\..\MainWindow.xaml"
            this.BtnPersons.Click += new System.Windows.RoutedEventHandler(this.NavigateToPersons);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnWorkers = ((System.Windows.Controls.Button)(target));
            
            #line 111 "..\..\..\MainWindow.xaml"
            this.BtnWorkers.Click += new System.Windows.RoutedEventHandler(this.NavigateToWorkers);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnDebts = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\MainWindow.xaml"
            this.BtnDebts.Click += new System.Windows.RoutedEventHandler(this.NavigateToDebts);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnFactoryDebts = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\MainWindow.xaml"
            this.BtnFactoryDebts.Click += new System.Windows.RoutedEventHandler(this.NavigateToFactoryDebts);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnOverdue = ((System.Windows.Controls.Button)(target));
            
            #line 129 "..\..\..\MainWindow.xaml"
            this.BtnOverdue.Click += new System.Windows.RoutedEventHandler(this.NavigateToOverdue);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnReports = ((System.Windows.Controls.Button)(target));
            
            #line 135 "..\..\..\MainWindow.xaml"
            this.BtnReports.Click += new System.Windows.RoutedEventHandler(this.NavigateToReports);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnBackup = ((System.Windows.Controls.Button)(target));
            
            #line 141 "..\..\..\MainWindow.xaml"
            this.BtnBackup.Click += new System.Windows.RoutedEventHandler(this.NavigateToBackup);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnIronCalc = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\MainWindow.xaml"
            this.BtnIronCalc.Click += new System.Windows.RoutedEventHandler(this.NavigateToIronCalc);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnCuttingCalc = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\..\MainWindow.xaml"
            this.BtnCuttingCalc.Click += new System.Windows.RoutedEventHandler(this.NavigateToCuttingCalc);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnReminders = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\MainWindow.xaml"
            this.BtnReminders.Click += new System.Windows.RoutedEventHandler(this.NavigateToReminders);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnSearch = ((System.Windows.Controls.Button)(target));
            
            #line 171 "..\..\..\MainWindow.xaml"
            this.BtnSearch.Click += new System.Windows.RoutedEventHandler(this.NavigateToSearch);
            
            #line default
            #line hidden
            return;
            case 15:
            this.BtnSettings = ((System.Windows.Controls.Button)(target));
            
            #line 176 "..\..\..\MainWindow.xaml"
            this.BtnSettings.Click += new System.Windows.RoutedEventHandler(this.NavigateToSettings);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ContentTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            
            #line 206 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshContent);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 208 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportContent);
            
            #line default
            #line hidden
            return;
            case 19:
            this.MainContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 20:
            this.DashboardContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 21:
            this.TotalPersonsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.TotalDebtsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.OverdueDebtsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.TotalAmountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.RecentActivitiesList = ((System.Windows.Controls.ListView)(target));
            return;
            case 26:
            this.ActivityContextMenu = ((System.Windows.Controls.ContextMenu)(target));
            return;
            case 27:
            
            #line 286 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteActivity_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 294 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.UndoActivity_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 337 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAddPerson);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 340 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAddDebt);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 356 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickReport);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 372 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestUndo);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

