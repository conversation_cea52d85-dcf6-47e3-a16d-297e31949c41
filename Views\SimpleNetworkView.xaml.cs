using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Text.Json;
using Microsoft.Data.Sqlite;
using DebtManagementApp.Services;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;

namespace DebtManagementApp.Views
{
    public partial class SimpleNetworkView : UserControl
    {
        private readonly SimpleNetworkService _networkService;
        private readonly object _databaseService;
        private bool _isScanning;
        private readonly List<string> _connectedDevices = new();

        public SimpleNetworkView()
        {
            InitializeComponent();
            _networkService = new SimpleNetworkService();
            _databaseService = null; // سيتم استخدام DatabaseHelper مباشرة

            InitializeEvents();
            RefreshLocalIP();
            UpdateConnectionStatus(false);
            UpdateServerStatus(false);

            // تهيئة خدمة المزامنة
            NetworkSyncService.Initialize(_networkService);
        }

        private void InitializeEvents()
        {
            _networkService.OnConnectionStatusChanged += OnConnectionStatusChanged;
            _networkService.OnLog += OnLog;
            _networkService.OnMessageReceived += OnMessageReceived;
        }

        private void RefreshLocalIP()
        {
            LocalIPTextBox.Text = SimpleNetworkService.GetLocalIPAddress();
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateConnectionStatus(isConnected);

                if (isConnected)
                {
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = _networkService.IsServer;
                    ConnectButton.IsEnabled = false;
                    DisconnectButton.IsEnabled = true;
                    TestButton.IsEnabled = true;

                    // إشعار بالاتصال
                    ShowConnectionNotification("✅ تم الاتصال بنجاح!", true);
                }
                else
                {
                    StartServerButton.IsEnabled = true;
                    StopServerButton.IsEnabled = false;
                    ConnectButton.IsEnabled = true;
                    DisconnectButton.IsEnabled = false;
                    TestButton.IsEnabled = false;

                    // إشعار بقطع الاتصال
                    ShowConnectionNotification("🔴 تم قطع الاتصال", false);
                    _connectedDevices.Clear();
                    UpdateDevicesList();
                }
            });
        }

        private void OnLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                LogTextBlock.Text += $"\n[{DateTime.Now:HH:mm:ss}] {message}";
                
                // التمرير لأسفل
                if (LogTextBlock.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToEnd();
                }
            });
        }

        private void OnMessageReceived(string message)
        {
            OnLog($"📥 استقبال رسالة: {message}");

            try
            {
                // معالجة الرسائل المختلفة
                var messageData = JsonSerializer.Deserialize<Services.NetworkMessage>(message);
                OnLog($"🔍 نوع الرسالة: {messageData?.Type}");

                Dispatcher.Invoke(async () =>
                {
                    switch (messageData?.Type)
                    {
                        case "PERSON_ADDED":
                            OnLog("🔄 معالجة إضافة شخص...");
                            await HandlePersonAdded(messageData.Data);
                            break;
                        case "PERSON_UPDATED":
                            OnLog("🔄 معالجة تحديث شخص...");
                            await HandlePersonUpdated(messageData.Data);
                            break;
                        case "PERSON_DELETED":
                            OnLog("🔄 معالجة حذف شخص...");
                            await HandlePersonDeleted(messageData.Data);
                            break;
                        case "DEBT_ADDED":
                            OnLog("🔄 معالجة إضافة دين...");
                            await HandleDebtAdded(messageData.Data);
                            break;
                        case "DEBT_UPDATED":
                            OnLog("🔄 معالجة تحديث دين...");
                            await HandleDebtUpdated(messageData.Data);
                            break;
                        case "DEBT_DELETED":
                            OnLog("🔄 معالجة حذف دين...");
                            await HandleDebtDeleted(messageData.Data);
                            break;
                        case "DEVICE_INFO":
                            OnLog("🔄 معالجة معلومات الجهاز...");
                            HandleDeviceInfo(messageData.Data);
                            break;
                        case "FACTORY_DEBT_ADDED":
                            OnLog("🔄 معالجة إضافة دين معمل...");
                            await HandleFactoryDebtAdded(messageData.Data);
                            break;
                        case "FACTORY_DEBT_UPDATED":
                            OnLog("🔄 معالجة تحديث دين معمل...");
                            await HandleFactoryDebtUpdated(messageData.Data);
                            break;
                        case "FACTORY_DEBT_DELETED":
                            OnLog("🔄 معالجة حذف دين معمل...");
                            await HandleFactoryDebtDeleted(messageData.Data);
                            break;
                        case "SALARY_ADDED":
                            OnLog("🔄 معالجة إضافة راتب...");
                            await HandleSalaryAdded(messageData.Data);
                            break;
                        case "SALARY_UPDATED":
                            OnLog("🔄 معالجة تحديث راتب...");
                            await HandleSalaryUpdated(messageData.Data);
                            break;
                        case "SALARY_DELETED":
                            OnLog("🔄 معالجة حذف راتب...");
                            await HandleSalaryDeleted(messageData.Data);
                            break;
                        case "SYNC_REQUEST":
                            OnLog("🔄 معالجة طلب المزامنة...");
                            await HandleSyncRequest();
                            break;
                        default:
                            OnLog($"⚠️ نوع رسالة غير معروف: {messageData?.Type}");
                            break;
                    }
                });
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في معالجة الرسالة: {ex.Message}");
            }
        }

        // معالجة إضافة شخص
        private async Task HandlePersonAdded(string data)
        {
            try
            {
                var person = JsonSerializer.Deserialize<Person>(data);
                if (person != null)
                {
                    OnLog($"📝 إضافة شخص: {person.Name}");

                    // تجنب إرسال تحديث شبكة مرة أخرى
                    var originalId = person.Id;
                    person.Id = 0; // إعادة تعيين ID للسماح بإضافة جديدة

                    // إضافة مؤقتة بدون إرسال تحديث شبكة
                    AddPersonWithoutNetworkSync(person);

                    ShowDataSyncNotification($"تم إضافة شخص جديد: {person.Name}");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إضافة الشخص: {ex.Message}");
            }
        }

        // معالجة تحديث شخص
        private async Task HandlePersonUpdated(string data)
        {
            try
            {
                var person = JsonSerializer.Deserialize<Person>(data);
                if (person != null)
                {
                    DatabaseHelper.UpdatePerson(person);
                    ShowDataSyncNotification($"تم تحديث بيانات: {person.Name}");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث الشخص: {ex.Message}");
            }
        }

        // معالجة حذف شخص
        private async Task HandlePersonDeleted(string data)
        {
            try
            {
                var personId = JsonSerializer.Deserialize<int>(data);
                DatabaseHelper.DeletePerson(personId);
                ShowDataSyncNotification("تم حذف شخص");
                RefreshMainWindow();
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حذف الشخص: {ex.Message}");
            }
        }

        // معالجة إضافة دين
        private async Task HandleDebtAdded(string data)
        {
            try
            {
                var debt = JsonSerializer.Deserialize<Debt>(data);
                if (debt != null)
                {
                    OnLog($"💰 إضافة دين: {debt.Amount:N0} د.ع");

                    // تجنب إرسال تحديث شبكة مرة أخرى
                    var originalId = debt.Id;
                    debt.Id = 0; // إعادة تعيين ID للسماح بإضافة جديدة

                    // إضافة مؤقتة بدون إرسال تحديث شبكة
                    AddDebtWithoutNetworkSync(debt);

                    ShowDataSyncNotification($"تم إضافة دين جديد: {debt.Amount:N0} د.ع");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إضافة الدين: {ex.Message}");
            }
        }

        // معالجة تحديث دين
        private async Task HandleDebtUpdated(string data)
        {
            try
            {
                var debt = JsonSerializer.Deserialize<Debt>(data);
                if (debt != null)
                {
                    DatabaseHelper.UpdateDebt(debt);
                    ShowDataSyncNotification($"تم تحديث دين: {debt.Amount:N0} د.ع");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث الدين: {ex.Message}");
            }
        }

        // معالجة حذف دين
        private async Task HandleDebtDeleted(string data)
        {
            try
            {
                var debtId = JsonSerializer.Deserialize<int>(data);
                DatabaseHelper.DeleteDebt(debtId);
                ShowDataSyncNotification("تم حذف دين");
                RefreshMainWindow();
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حذف الدين: {ex.Message}");
            }
        }

        // معالجة معلومات الجهاز
        private void HandleDeviceInfo(string data)
        {
            try
            {
                var deviceInfo = JsonSerializer.Deserialize<DeviceInfo>(data);
                if (deviceInfo != null && !_connectedDevices.Contains(deviceInfo.Name))
                {
                    _connectedDevices.Add(deviceInfo.Name);
                    UpdateDevicesList();
                    ShowConnectionNotification($"🔗 جهاز جديد متصل: {deviceInfo.Name}", true);
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في معالجة معلومات الجهاز: {ex.Message}");
            }
        }

        // معالجة طلب المزامنة
        private async Task HandleSyncRequest()
        {
            try
            {
                OnLog("🔄 بدء معالجة طلب المزامنة الشاملة...");

                // إرسال جميع البيانات للجهاز الجديد
                var persons = DatabaseHelper.GetAllPersons();
                var debts = DatabaseHelper.GetAllDebts();
                var factoryDebts = DatabaseHelper.GetAllFactoryDebts();
                var salaries = DatabaseHelper.GetAllSalaries();

                OnLog($"📊 الإحصائيات:");
                OnLog($"   👥 الأشخاص: {persons.Count}");
                OnLog($"   💰 الديون العادية: {debts.Count}");
                OnLog($"   🏭 ديون المعمل: {factoryDebts.Count}");
                OnLog($"   💼 رواتب العمال: {salaries.Count}");

                // إرسال الأشخاص
                foreach (var person in persons)
                {
                    await SendNetworkMessage("PERSON_ADDED", JsonSerializer.Serialize(person));
                    OnLog($"📤 تم إرسال شخص: {person.Name}");
                }

                // إرسال الديون العادية
                foreach (var debt in debts)
                {
                    await SendNetworkMessage("DEBT_ADDED", JsonSerializer.Serialize(debt));
                    OnLog($"📤 تم إرسال دين عادي: {debt.Amount:N0} د.ع");
                }

                // إرسال ديون المعمل
                foreach (var factoryDebt in factoryDebts)
                {
                    await SendNetworkMessage("FACTORY_DEBT_ADDED", JsonSerializer.Serialize(factoryDebt));
                    OnLog($"📤 تم إرسال دين معمل: {factoryDebt.Amount:N0} د.ع - {factoryDebt.PersonName}");
                }

                // إرسال رواتب العمال
                foreach (var salary in salaries)
                {
                    await SendNetworkMessage("SALARY_ADDED", JsonSerializer.Serialize(salary));
                    OnLog($"📤 تم إرسال راتب: {salary.TotalAmount:N0} د.ع - {salary.WorkerName}");
                }

                OnLog("✅ تم إرسال جميع البيانات للمزامنة بنجاح");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في المزامنة: {ex.Message}");
            }
        }

        private async void StartServer_Click(object sender, RoutedEventArgs e)
        {
            StartServerButton.IsEnabled = false;
            OnLog("🚀 بدء تشغيل الخادم...");

            bool success = await _networkService.StartServer();

            if (success)
            {
                UpdateServerStatus(true);

                // إضافة الخادم إلى قائمة الأجهزة
                var serverName = $"{Environment.MachineName} (خادم)";
                if (!_connectedDevices.Contains(serverName))
                {
                    _connectedDevices.Add(serverName);
                    UpdateDevicesList();
                }

                OnLog("✅ تم بدء الخادم بنجاح!");
                ShowConnectionNotification("تم بدء الخادم بنجاح!", true);
            }
            else
            {
                StartServerButton.IsEnabled = true;
                UpdateServerStatus(false);
                OnLog("❌ فشل في بدء الخادم");
                ShowConnectionNotification("فشل في بدء الخادم", false);
            }
        }

        private void StopServer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _networkService.Disconnect();
                UpdateServerStatus(false);
                UpdateConnectionStatus(false);

                // مسح قائمة الأجهزة
                _connectedDevices.Clear();
                UpdateDevicesList();

                // إعادة تفعيل الأزرار
                StartServerButton.IsEnabled = true;
                StopServerButton.IsEnabled = false;
                ConnectButton.IsEnabled = true;
                DisconnectButton.IsEnabled = false;
                TestButton.IsEnabled = false;

                OnLog("⏹️ تم إيقاف الخادم بنجاح");
                ShowConnectionNotification("تم إيقاف الخادم", false);
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إيقاف الخادم: {ex.Message}");
            }
        }

        private async void ConnectToServer_Click(object sender, RoutedEventArgs e)
        {
            string serverIP = ServerIPTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(serverIP))
            {
                MessageBox.Show("يرجى إدخال عنوان IP الخادم", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            ConnectButton.IsEnabled = false;
            OnLog($"🔗 محاولة الاتصال بـ {serverIP}...");

            bool success = await _networkService.ConnectToServer(serverIP);

            if (success)
            {
                // إرسال معلومات الجهاز
                var deviceInfo = new DeviceInfo
                {
                    Name = Environment.MachineName,
                    IP = SimpleNetworkService.GetLocalIPAddress(),
                    ConnectedAt = DateTime.Now
                };

                await SendNetworkMessage("DEVICE_INFO", JsonSerializer.Serialize(deviceInfo));

                // طلب مزامنة البيانات
                await SendNetworkMessage("SYNC_REQUEST", "");
            }
            else
            {
                ConnectButton.IsEnabled = true;
            }
        }

        private void Disconnect_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _networkService.Disconnect();
                UpdateConnectionStatus(false);

                // مسح قائمة الأجهزة إذا لم نكن خادم
                if (!_networkService.IsServer)
                {
                    _connectedDevices.Clear();
                    UpdateDevicesList();
                }

                OnLog("🔌 تم قطع الاتصال");
                ShowConnectionNotification("تم قطع الاتصال", false);
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في قطع الاتصال: {ex.Message}");
            }
        }

        private async void ScanNetwork_Click(object sender, RoutedEventArgs e)
        {
            if (_isScanning) return;

            _isScanning = true;
            ScanStatusText.Text = "🔍 جاري البحث عن الأجهزة...";
            DevicesListBox.Items.Clear();

            try
            {
                var devices = await SimpleNetworkService.ScanNetwork();
                
                foreach (var device in devices)
                {
                    DevicesListBox.Items.Add(device);
                }

                ScanStatusText.Text = $"تم العثور على {devices.Count} جهاز";
                OnLog($"🔍 تم العثور على {devices.Count} جهاز في الشبكة");
            }
            catch (Exception ex)
            {
                ScanStatusText.Text = $"خطأ في البحث: {ex.Message}";
                OnLog($"❌ خطأ في البحث: {ex.Message}");
            }
            finally
            {
                _isScanning = false;
            }
        }

        private void DevicesListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DevicesListBox.SelectedItem is string selectedIP)
            {
                ServerIPTextBox.Text = selectedIP;
                OnLog($"📍 تم اختيار الجهاز: {selectedIP}");
            }
        }

        private async void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            if (_networkService.IsConnected)
            {
                await _networkService.SendSimpleData("TEST", "اختبار الاتصال");
                OnLog("🧪 تم إرسال رسالة اختبار");
            }
        }

        private void RefreshIP_Click(object sender, RoutedEventArgs e)
        {
            RefreshLocalIP();
            OnLog("🔄 تم تحديث عنوان IP المحلي");
        }

        private void ClearLog_Click(object sender, RoutedEventArgs e)
        {
            LogTextBlock.Text = "جاهز للاتصال...";
        }

        // تحديث حالة الاتصال
        private void UpdateConnectionStatus(bool isConnected)
        {
            if (isConnected)
            {
                ConnectionStatusText.Text = "متصل";
                ConnectionStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0x28, 0xA7, 0x45));
                ConnectionStatusBorder.Background = new SolidColorBrush(Color.FromRgb(0xE8, 0xF5, 0xE8));
            }
            else
            {
                ConnectionStatusText.Text = "غير متصل";
                ConnectionStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0xD3, 0x2F, 0x2F));
                ConnectionStatusBorder.Background = new SolidColorBrush(Color.FromRgb(0xFF, 0xE0, 0xE0));
            }
        }

        // تحديث حالة الخادم
        private void UpdateServerStatus(bool isRunning)
        {
            if (isRunning)
            {
                ServerStatusText.Text = "خادم نشط";
                ServerStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0x28, 0xA7, 0x45));
                ServerStatusBorder.Background = new SolidColorBrush(Color.FromRgb(0xE8, 0xF5, 0xE8));
            }
            else
            {
                ServerStatusText.Text = "خادم متوقف";
                ServerStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0xD3, 0x2F, 0x2F));
                ServerStatusBorder.Background = new SolidColorBrush(Color.FromRgb(0xFF, 0xE0, 0xE0));
            }
        }

        // إظهار تنبيه الاتصال
        private void ShowConnectionNotification(string message, bool isSuccess)
        {
            try
            {
                if (isSuccess)
                {
                    NotificationHelper.ShowSuccess(message);
                }
                else
                {
                    NotificationHelper.ShowWarning(message);
                }
                OnLog(message);
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إظهار التنبيه: {ex.Message}");
            }
        }

        // إظهار تنبيه مزامنة البيانات
        private void ShowDataSyncNotification(string message)
        {
            try
            {
                NotificationHelper.ShowInfo(message);
                OnLog($"🔄 {message}");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إظهار تنبيه المزامنة: {ex.Message}");
            }
        }

        // تحديث قائمة الأجهزة
        private void UpdateDevicesList()
        {
            DevicesListBox.Items.Clear();
            foreach (var device in _connectedDevices)
            {
                DevicesListBox.Items.Add($"🖥️ {device}");
            }

            // تحديث عداد الأجهزة
            ConnectedDevicesCount.Text = $"{_connectedDevices.Count} جهاز متصل";
        }

        // تحديث النافذة الرئيسية
        private void RefreshMainWindow()
        {
            try
            {
                var mainWindow = Application.Current.MainWindow as MainWindow;
                mainWindow?.RefreshCurrentView();
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث النافذة الرئيسية: {ex.Message}");
            }
        }

        // إرسال رسالة شبكة
        private async Task SendNetworkMessage(string type, string data)
        {
            try
            {
                var message = new Services.NetworkMessage
                {
                    Type = type,
                    Data = data,
                    Timestamp = DateTime.Now
                };

                var json = JsonSerializer.Serialize(message);
                await _networkService.SendSimpleData(type, json);
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إرسال الرسالة: {ex.Message}");
            }
        }

        // معالجة إضافة دين معمل
        private async Task HandleFactoryDebtAdded(string data)
        {
            try
            {
                var factoryDebt = JsonSerializer.Deserialize<FactoryDebt>(data);
                if (factoryDebt != null)
                {
                    OnLog($"🏭 إضافة دين معمل: {factoryDebt.Amount:N0} د.ع - {factoryDebt.PersonName}");

                    // تجنب إرسال تحديث شبكة مرة أخرى
                    var originalId = factoryDebt.Id;
                    factoryDebt.Id = 0;

                    AddFactoryDebtWithoutNetworkSync(factoryDebt);
                    ShowDataSyncNotification($"تم إضافة دين معمل: {factoryDebt.PersonName} - {factoryDebt.Amount:N0} د.ع");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إضافة دين المعمل: {ex.Message}");
            }
        }

        // معالجة تحديث دين معمل
        private async Task HandleFactoryDebtUpdated(string data)
        {
            try
            {
                var factoryDebt = JsonSerializer.Deserialize<FactoryDebt>(data);
                if (factoryDebt != null)
                {
                    OnLog($"🏭 تحديث دين معمل: {factoryDebt.Amount:N0} د.ع - {factoryDebt.PersonName}");
                    UpdateFactoryDebtWithoutNetworkSync(factoryDebt);
                    ShowDataSyncNotification($"تم تحديث دين معمل: {factoryDebt.PersonName}");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث دين المعمل: {ex.Message}");
            }
        }

        // معالجة حذف دين معمل
        private async Task HandleFactoryDebtDeleted(string data)
        {
            try
            {
                var debtId = JsonSerializer.Deserialize<int>(data);
                DeleteFactoryDebtWithoutNetworkSync(debtId);
                ShowDataSyncNotification("تم حذف دين معمل");
                RefreshMainWindow();
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حذف دين المعمل: {ex.Message}");
            }
        }

        // معالجة إضافة راتب
        private async Task HandleSalaryAdded(string data)
        {
            try
            {
                var salary = JsonSerializer.Deserialize<Salary>(data);
                if (salary != null)
                {
                    OnLog($"💼 إضافة راتب: {salary.TotalAmount:N0} د.ع - {salary.WorkerName}");

                    // تجنب إرسال تحديث شبكة مرة أخرى
                    var originalId = salary.Id;
                    salary.Id = 0;

                    AddSalaryWithoutNetworkSync(salary);
                    ShowDataSyncNotification($"تم إضافة راتب: {salary.WorkerName} - {salary.TotalAmount:N0} د.ع");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إضافة الراتب: {ex.Message}");
            }
        }

        // معالجة تحديث راتب
        private async Task HandleSalaryUpdated(string data)
        {
            try
            {
                var salary = JsonSerializer.Deserialize<Salary>(data);
                if (salary != null)
                {
                    OnLog($"💼 تحديث راتب: {salary.TotalAmount:N0} د.ع - {salary.WorkerName}");
                    UpdateSalaryWithoutNetworkSync(salary);
                    ShowDataSyncNotification($"تم تحديث راتب: {salary.WorkerName}");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث الراتب: {ex.Message}");
            }
        }

        // معالجة حذف راتب
        private async Task HandleSalaryDeleted(string data)
        {
            try
            {
                var salaryId = JsonSerializer.Deserialize<int>(data);
                DeleteSalaryWithoutNetworkSync(salaryId);
                ShowDataSyncNotification("تم حذف راتب");
                RefreshMainWindow();
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حذف الراتب: {ex.Message}");
            }
        }

        // إضافة شخص بدون مزامنة شبكة
        private void AddPersonWithoutNetworkSync(Person person)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseHelper.DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(
                    "INSERT INTO Persons (Name, Phone, Location, AdditionalInfo) VALUES (@Name, @Phone, @Location, @AdditionalInfo)",
                    connection);

                command.Parameters.AddWithValue("@Name", person.Name);
                command.Parameters.AddWithValue("@Phone", person.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Location", person.Location ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@AdditionalInfo", person.AdditionalInfo ?? (object)DBNull.Value);

                command.ExecuteNonQuery();
                OnLog($"✅ تم حفظ الشخص في قاعدة البيانات: {person.Name}");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حفظ الشخص: {ex.Message}");
            }
        }

        // إضافة دين بدون مزامنة شبكة
        private void AddDebtWithoutNetworkSync(Debt debt)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseHelper.DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    INSERT INTO Debts (
                        PersonId, Date, Amount, IronCost, CuttingCost, BendingCost, WeldingCost,
                        IsConnected, OperationType, Notes, IsSettled, DueDate
                    ) VALUES (
                        @PersonId, @Date, @Amount, @IronCost, @CuttingCost, @BendingCost, @WeldingCost,
                        @IsConnected, @OperationType, @Notes, @IsSettled, @DueDate
                    )", connection);

                command.Parameters.AddWithValue("@PersonId", debt.PersonId);
                command.Parameters.AddWithValue("@Date", debt.Date.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@Amount", debt.Amount);
                command.Parameters.AddWithValue("@IronCost", debt.IronCost);
                command.Parameters.AddWithValue("@CuttingCost", debt.CuttingCost);
                command.Parameters.AddWithValue("@BendingCost", debt.BendingCost);
                command.Parameters.AddWithValue("@WeldingCost", debt.WeldingCost);
                command.Parameters.AddWithValue("@IsConnected", debt.IsConnected);
                command.Parameters.AddWithValue("@OperationType", debt.OperationType ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Notes", debt.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsSettled", debt.IsSettled);
                command.Parameters.AddWithValue("@DueDate", debt.DueDate.ToString("yyyy-MM-dd HH:mm:ss"));

                command.ExecuteNonQuery();
                OnLog($"✅ تم حفظ الدين في قاعدة البيانات: {debt.Amount:N0} د.ع");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حفظ الدين: {ex.Message}");
            }
        }

        // إضافة دين معمل بدون مزامنة شبكة
        private void AddFactoryDebtWithoutNetworkSync(FactoryDebt debt)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseHelper.DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    INSERT INTO FactoryDebts (
                        PersonId, PersonName, Amount, DebtDate, InvoiceType, Description, IsPaid, AttachmentPath
                    ) VALUES (
                        @PersonId, @PersonName, @Amount, @DebtDate, @InvoiceType, @Description, @IsPaid, @AttachmentPath
                    )", connection);

                command.Parameters.AddWithValue("@PersonId", debt.PersonId);
                command.Parameters.AddWithValue("@PersonName", debt.PersonName);
                command.Parameters.AddWithValue("@Amount", debt.Amount);
                command.Parameters.AddWithValue("@DebtDate", debt.DebtDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@InvoiceType", debt.InvoiceType);
                command.Parameters.AddWithValue("@Description", debt.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsPaid", debt.IsPaid);
                command.Parameters.AddWithValue("@AttachmentPath", debt.AttachmentPath ?? (object)DBNull.Value);

                command.ExecuteNonQuery();
                OnLog($"✅ تم حفظ دين المعمل في قاعدة البيانات: {debt.PersonName} - {debt.Amount:N0} د.ع");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حفظ دين المعمل: {ex.Message}");
            }
        }

        // تحديث دين معمل بدون مزامنة شبكة
        private void UpdateFactoryDebtWithoutNetworkSync(FactoryDebt debt)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseHelper.DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    UPDATE FactoryDebts SET
                        PersonId = @PersonId, PersonName = @PersonName, Amount = @Amount,
                        DebtDate = @DebtDate, InvoiceType = @InvoiceType, Description = @Description,
                        IsPaid = @IsPaid, AttachmentPath = @AttachmentPath
                    WHERE Id = @Id", connection);

                command.Parameters.AddWithValue("@Id", debt.Id);
                command.Parameters.AddWithValue("@PersonId", debt.PersonId);
                command.Parameters.AddWithValue("@PersonName", debt.PersonName);
                command.Parameters.AddWithValue("@Amount", debt.Amount);
                command.Parameters.AddWithValue("@DebtDate", debt.DebtDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@InvoiceType", debt.InvoiceType);
                command.Parameters.AddWithValue("@Description", debt.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsPaid", debt.IsPaid);
                command.Parameters.AddWithValue("@AttachmentPath", debt.AttachmentPath ?? (object)DBNull.Value);

                command.ExecuteNonQuery();
                OnLog($"✅ تم تحديث دين المعمل في قاعدة البيانات: {debt.PersonName}");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث دين المعمل: {ex.Message}");
            }
        }

        // حذف دين معمل بدون مزامنة شبكة
        private void DeleteFactoryDebtWithoutNetworkSync(int debtId)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseHelper.DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand("DELETE FROM FactoryDebts WHERE Id = @Id", connection);
                command.Parameters.AddWithValue("@Id", debtId);
                command.ExecuteNonQuery();

                OnLog($"✅ تم حذف دين المعمل من قاعدة البيانات");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حذف دين المعمل: {ex.Message}");
            }
        }

        // إضافة راتب بدون مزامنة شبكة
        private void AddSalaryWithoutNetworkSync(Salary salary)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseHelper.DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    INSERT INTO Salaries (
                        WorkerId, WorkerName, PaymentDate, FromDate, ToDate, DailyWage, WorkDays, AbsenceDays,
                        OvertimeHours, OvertimeRate, Deductions, Bonuses, TotalAmount, Notes
                    ) VALUES (
                        @WorkerId, @WorkerName, @PaymentDate, @FromDate, @ToDate, @DailyWage, @WorkDays, @AbsenceDays,
                        @OvertimeHours, @OvertimeRate, @Deductions, @Bonuses, @TotalAmount, @Notes
                    )", connection);

                command.Parameters.AddWithValue("@WorkerId", salary.WorkerId);
                command.Parameters.AddWithValue("@WorkerName", salary.WorkerName);
                command.Parameters.AddWithValue("@PaymentDate", salary.PaymentDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@FromDate", salary.FromDate.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@ToDate", salary.ToDate.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@DailyWage", salary.DailyWage);
                command.Parameters.AddWithValue("@WorkDays", salary.WorkDays);
                command.Parameters.AddWithValue("@AbsenceDays", salary.AbsenceDays);
                command.Parameters.AddWithValue("@OvertimeHours", salary.OvertimeHours);
                command.Parameters.AddWithValue("@OvertimeRate", salary.OvertimeRate);
                command.Parameters.AddWithValue("@Deductions", salary.Deductions);
                command.Parameters.AddWithValue("@Bonuses", salary.Bonuses);
                command.Parameters.AddWithValue("@TotalAmount", salary.TotalAmount);
                command.Parameters.AddWithValue("@Notes", salary.Notes ?? (object)DBNull.Value);

                command.ExecuteNonQuery();
                OnLog($"✅ تم حفظ الراتب في قاعدة البيانات: {salary.WorkerName} - {salary.TotalAmount:N0} د.ع");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حفظ الراتب: {ex.Message}");
            }
        }

        // تحديث راتب بدون مزامنة شبكة
        private void UpdateSalaryWithoutNetworkSync(Salary salary)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseHelper.DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand(@"
                    UPDATE Salaries SET
                        WorkerId = @WorkerId, WorkerName = @WorkerName, PaymentDate = @PaymentDate,
                        FromDate = @FromDate, ToDate = @ToDate, DailyWage = @DailyWage,
                        WorkDays = @WorkDays, AbsenceDays = @AbsenceDays, OvertimeHours = @OvertimeHours,
                        OvertimeRate = @OvertimeRate, Deductions = @Deductions, Bonuses = @Bonuses,
                        TotalAmount = @TotalAmount, Notes = @Notes
                    WHERE Id = @Id", connection);

                command.Parameters.AddWithValue("@Id", salary.Id);
                command.Parameters.AddWithValue("@WorkerId", salary.WorkerId);
                command.Parameters.AddWithValue("@WorkerName", salary.WorkerName);
                command.Parameters.AddWithValue("@PaymentDate", salary.PaymentDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@FromDate", salary.FromDate.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@ToDate", salary.ToDate.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@DailyWage", salary.DailyWage);
                command.Parameters.AddWithValue("@WorkDays", salary.WorkDays);
                command.Parameters.AddWithValue("@AbsenceDays", salary.AbsenceDays);
                command.Parameters.AddWithValue("@OvertimeHours", salary.OvertimeHours);
                command.Parameters.AddWithValue("@OvertimeRate", salary.OvertimeRate);
                command.Parameters.AddWithValue("@Deductions", salary.Deductions);
                command.Parameters.AddWithValue("@Bonuses", salary.Bonuses);
                command.Parameters.AddWithValue("@TotalAmount", salary.TotalAmount);
                command.Parameters.AddWithValue("@Notes", salary.Notes ?? (object)DBNull.Value);

                command.ExecuteNonQuery();
                OnLog($"✅ تم تحديث الراتب في قاعدة البيانات: {salary.WorkerName}");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث الراتب: {ex.Message}");
            }
        }

        // حذف راتب بدون مزامنة شبكة
        private void DeleteSalaryWithoutNetworkSync(int salaryId)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabaseHelper.DatabaseFile};");
                connection.Open();

                using var command = new SqliteCommand("DELETE FROM Salaries WHERE Id = @Id", connection);
                command.Parameters.AddWithValue("@Id", salaryId);
                command.ExecuteNonQuery();

                OnLog($"✅ تم حذف الراتب من قاعدة البيانات");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حذف الراتب: {ex.Message}");
            }
        }

        // تنظيف الموارد
        public void Cleanup()
        {
            _networkService?.Disconnect();
        }
    }

    // نموذج معلومات الجهاز
    public class DeviceInfo
    {
        public string Name { get; set; } = "";
        public string IP { get; set; } = "";
        public DateTime ConnectedAt { get; set; }
    }
}
