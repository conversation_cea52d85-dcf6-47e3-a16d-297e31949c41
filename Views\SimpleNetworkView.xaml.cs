using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DebtManagementApp.Services;

namespace DebtManagementApp.Views
{
    public partial class SimpleNetworkView : UserControl
    {
        private readonly SimpleNetworkService _networkService;
        private bool _isScanning;

        public SimpleNetworkView()
        {
            InitializeComponent();
            _networkService = new SimpleNetworkService();
            
            InitializeEvents();
            RefreshLocalIP();
        }

        private void InitializeEvents()
        {
            _networkService.OnConnectionStatusChanged += OnConnectionStatusChanged;
            _networkService.OnLog += OnLog;
            _networkService.OnMessageReceived += OnMessageReceived;
        }

        private void RefreshLocalIP()
        {
            LocalIPTextBox.Text = SimpleNetworkService.GetLocalIPAddress();
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            Dispatcher.Invoke(() =>
            {
                if (isConnected)
                {
                    ConnectionStatusText.Text = "🟢 متصل";
                    ConnectionStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0x28, 0xA7, 0x45));
                    
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = _networkService.IsServer;
                    ConnectButton.IsEnabled = false;
                    DisconnectButton.IsEnabled = true;
                    TestButton.IsEnabled = true;
                }
                else
                {
                    ConnectionStatusText.Text = "🔴 غير متصل";
                    ConnectionStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0xDC, 0x35, 0x45));
                    
                    StartServerButton.IsEnabled = true;
                    StopServerButton.IsEnabled = false;
                    ConnectButton.IsEnabled = true;
                    DisconnectButton.IsEnabled = false;
                    TestButton.IsEnabled = false;
                }
            });
        }

        private void OnLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                LogTextBlock.Text += $"\n[{DateTime.Now:HH:mm:ss}] {message}";
                
                // التمرير لأسفل
                if (LogTextBlock.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToEnd();
                }
            });
        }

        private void OnMessageReceived(string message)
        {
            OnLog($"📥 استقبال رسالة: {message}");
            
            // يمكن إضافة معالجة للرسائل هنا
            // مثل تحديث البيانات أو إظهار إشعارات
        }

        private async void StartServer_Click(object sender, RoutedEventArgs e)
        {
            StartServerButton.IsEnabled = false;
            OnLog("🚀 بدء تشغيل الخادم...");
            
            bool success = await _networkService.StartServer();
            
            if (!success)
            {
                StartServerButton.IsEnabled = true;
            }
        }

        private void StopServer_Click(object sender, RoutedEventArgs e)
        {
            _networkService.Disconnect();
            OnLog("⏹️ تم إيقاف الخادم");
        }

        private async void ConnectToServer_Click(object sender, RoutedEventArgs e)
        {
            string serverIP = ServerIPTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(serverIP))
            {
                MessageBox.Show("يرجى إدخال عنوان IP الخادم", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            ConnectButton.IsEnabled = false;
            OnLog($"🔗 محاولة الاتصال بـ {serverIP}...");
            
            bool success = await _networkService.ConnectToServer(serverIP);
            
            if (!success)
            {
                ConnectButton.IsEnabled = true;
            }
        }

        private void Disconnect_Click(object sender, RoutedEventArgs e)
        {
            _networkService.Disconnect();
        }

        private async void ScanNetwork_Click(object sender, RoutedEventArgs e)
        {
            if (_isScanning) return;

            _isScanning = true;
            ScanStatusText.Text = "🔍 جاري البحث عن الأجهزة...";
            DevicesListBox.Items.Clear();

            try
            {
                var devices = await SimpleNetworkService.ScanNetwork();
                
                foreach (var device in devices)
                {
                    DevicesListBox.Items.Add(device);
                }

                ScanStatusText.Text = $"تم العثور على {devices.Count} جهاز";
                OnLog($"🔍 تم العثور على {devices.Count} جهاز في الشبكة");
            }
            catch (Exception ex)
            {
                ScanStatusText.Text = $"خطأ في البحث: {ex.Message}";
                OnLog($"❌ خطأ في البحث: {ex.Message}");
            }
            finally
            {
                _isScanning = false;
            }
        }

        private void DevicesListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DevicesListBox.SelectedItem is string selectedIP)
            {
                ServerIPTextBox.Text = selectedIP;
                OnLog($"📍 تم اختيار الجهاز: {selectedIP}");
            }
        }

        private async void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            if (_networkService.IsConnected)
            {
                await _networkService.SendSimpleData("TEST", "اختبار الاتصال");
                OnLog("🧪 تم إرسال رسالة اختبار");
            }
        }

        private void RefreshIP_Click(object sender, RoutedEventArgs e)
        {
            RefreshLocalIP();
            OnLog("🔄 تم تحديث عنوان IP المحلي");
        }

        private void ClearLog_Click(object sender, RoutedEventArgs e)
        {
            LogTextBlock.Text = "جاهز للاتصال...";
        }

        // تنظيف الموارد
        public void Cleanup()
        {
            _networkService?.Disconnect();
        }
    }
}
