using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Text.Json;
using DebtManagementApp.Services;
using DebtManagementApp.Models;
using DebtManagementApp.Helpers;

namespace DebtManagementApp.Views
{
    public partial class SimpleNetworkView : UserControl
    {
        private readonly SimpleNetworkService _networkService;
        private readonly object _databaseService;
        private bool _isScanning;
        private readonly List<string> _connectedDevices = new();

        public SimpleNetworkView()
        {
            InitializeComponent();
            _networkService = new SimpleNetworkService();
            _databaseService = null; // سيتم استخدام DatabaseHelper مباشرة

            InitializeEvents();
            RefreshLocalIP();
            UpdateConnectionStatus(false);
        }

        private void InitializeEvents()
        {
            _networkService.OnConnectionStatusChanged += OnConnectionStatusChanged;
            _networkService.OnLog += OnLog;
            _networkService.OnMessageReceived += OnMessageReceived;
        }

        private void RefreshLocalIP()
        {
            LocalIPTextBox.Text = SimpleNetworkService.GetLocalIPAddress();
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateConnectionStatus(isConnected);

                if (isConnected)
                {
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = _networkService.IsServer;
                    ConnectButton.IsEnabled = false;
                    DisconnectButton.IsEnabled = true;
                    TestButton.IsEnabled = true;

                    // إشعار بالاتصال
                    ShowConnectionNotification("✅ تم الاتصال بنجاح!", true);
                }
                else
                {
                    StartServerButton.IsEnabled = true;
                    StopServerButton.IsEnabled = false;
                    ConnectButton.IsEnabled = true;
                    DisconnectButton.IsEnabled = false;
                    TestButton.IsEnabled = false;

                    // إشعار بقطع الاتصال
                    ShowConnectionNotification("🔴 تم قطع الاتصال", false);
                    _connectedDevices.Clear();
                    UpdateDevicesList();
                }
            });
        }

        private void OnLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                LogTextBlock.Text += $"\n[{DateTime.Now:HH:mm:ss}] {message}";
                
                // التمرير لأسفل
                if (LogTextBlock.Parent is ScrollViewer scrollViewer)
                {
                    scrollViewer.ScrollToEnd();
                }
            });
        }

        private void OnMessageReceived(string message)
        {
            OnLog($"📥 استقبال رسالة: {message}");

            try
            {
                // معالجة الرسائل المختلفة
                var messageData = JsonSerializer.Deserialize<NetworkMessage>(message);

                Dispatcher.Invoke(async () =>
                {
                    switch (messageData?.Type)
                    {
                        case "PERSON_ADDED":
                            await HandlePersonAdded(messageData.Data);
                            break;
                        case "PERSON_UPDATED":
                            await HandlePersonUpdated(messageData.Data);
                            break;
                        case "PERSON_DELETED":
                            await HandlePersonDeleted(messageData.Data);
                            break;
                        case "DEBT_ADDED":
                            await HandleDebtAdded(messageData.Data);
                            break;
                        case "DEBT_UPDATED":
                            await HandleDebtUpdated(messageData.Data);
                            break;
                        case "DEBT_DELETED":
                            await HandleDebtDeleted(messageData.Data);
                            break;
                        case "DEVICE_INFO":
                            HandleDeviceInfo(messageData.Data);
                            break;
                        case "SYNC_REQUEST":
                            await HandleSyncRequest();
                            break;
                        default:
                            OnLog($"⚠️ نوع رسالة غير معروف: {messageData?.Type}");
                            break;
                    }
                });
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في معالجة الرسالة: {ex.Message}");
            }
        }

        // معالجة إضافة شخص
        private async Task HandlePersonAdded(string data)
        {
            try
            {
                var person = JsonSerializer.Deserialize<Person>(data);
                if (person != null)
                {
                    DatabaseHelper.AddPerson(person);
                    ShowDataSyncNotification($"تم إضافة شخص جديد: {person.Name}");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إضافة الشخص: {ex.Message}");
            }
        }

        // معالجة تحديث شخص
        private async Task HandlePersonUpdated(string data)
        {
            try
            {
                var person = JsonSerializer.Deserialize<Person>(data);
                if (person != null)
                {
                    DatabaseHelper.UpdatePerson(person);
                    ShowDataSyncNotification($"تم تحديث بيانات: {person.Name}");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث الشخص: {ex.Message}");
            }
        }

        // معالجة حذف شخص
        private async Task HandlePersonDeleted(string data)
        {
            try
            {
                var personId = JsonSerializer.Deserialize<int>(data);
                DatabaseHelper.DeletePerson(personId);
                ShowDataSyncNotification("تم حذف شخص");
                RefreshMainWindow();
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حذف الشخص: {ex.Message}");
            }
        }

        // معالجة إضافة دين
        private async Task HandleDebtAdded(string data)
        {
            try
            {
                var debt = JsonSerializer.Deserialize<Debt>(data);
                if (debt != null)
                {
                    DatabaseHelper.AddDebt(debt);
                    ShowDataSyncNotification($"تم إضافة دين جديد: {debt.Amount:N0} د.ع");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إضافة الدين: {ex.Message}");
            }
        }

        // معالجة تحديث دين
        private async Task HandleDebtUpdated(string data)
        {
            try
            {
                var debt = JsonSerializer.Deserialize<Debt>(data);
                if (debt != null)
                {
                    DatabaseHelper.UpdateDebt(debt);
                    ShowDataSyncNotification($"تم تحديث دين: {debt.Amount:N0} د.ع");
                    RefreshMainWindow();
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث الدين: {ex.Message}");
            }
        }

        // معالجة حذف دين
        private async Task HandleDebtDeleted(string data)
        {
            try
            {
                var debtId = JsonSerializer.Deserialize<int>(data);
                DatabaseHelper.DeleteDebt(debtId);
                ShowDataSyncNotification("تم حذف دين");
                RefreshMainWindow();
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في حذف الدين: {ex.Message}");
            }
        }

        // معالجة معلومات الجهاز
        private void HandleDeviceInfo(string data)
        {
            try
            {
                var deviceInfo = JsonSerializer.Deserialize<DeviceInfo>(data);
                if (deviceInfo != null && !_connectedDevices.Contains(deviceInfo.Name))
                {
                    _connectedDevices.Add(deviceInfo.Name);
                    UpdateDevicesList();
                    ShowConnectionNotification($"🔗 جهاز جديد متصل: {deviceInfo.Name}", true);
                }
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في معالجة معلومات الجهاز: {ex.Message}");
            }
        }

        // معالجة طلب المزامنة
        private async Task HandleSyncRequest()
        {
            try
            {
                // إرسال جميع البيانات للجهاز الجديد
                var persons = DatabaseHelper.GetAllPersons();
                var debts = DatabaseHelper.GetAllDebts();

                foreach (var person in persons)
                {
                    await SendNetworkMessage("PERSON_ADDED", JsonSerializer.Serialize(person));
                }

                foreach (var debt in debts)
                {
                    await SendNetworkMessage("DEBT_ADDED", JsonSerializer.Serialize(debt));
                }

                OnLog("📤 تم إرسال جميع البيانات للمزامنة");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في المزامنة: {ex.Message}");
            }
        }

        private async void StartServer_Click(object sender, RoutedEventArgs e)
        {
            StartServerButton.IsEnabled = false;
            OnLog("🚀 بدء تشغيل الخادم...");

            bool success = await _networkService.StartServer();

            if (success)
            {
                // إضافة الخادم إلى قائمة الأجهزة
                var serverName = $"{Environment.MachineName} (خادم)";
                if (!_connectedDevices.Contains(serverName))
                {
                    _connectedDevices.Add(serverName);
                    UpdateDevicesList();
                }
            }
            else
            {
                StartServerButton.IsEnabled = true;
            }
        }

        private void StopServer_Click(object sender, RoutedEventArgs e)
        {
            _networkService.Disconnect();
            OnLog("⏹️ تم إيقاف الخادم");
        }

        private async void ConnectToServer_Click(object sender, RoutedEventArgs e)
        {
            string serverIP = ServerIPTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(serverIP))
            {
                MessageBox.Show("يرجى إدخال عنوان IP الخادم", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            ConnectButton.IsEnabled = false;
            OnLog($"🔗 محاولة الاتصال بـ {serverIP}...");

            bool success = await _networkService.ConnectToServer(serverIP);

            if (success)
            {
                // إرسال معلومات الجهاز
                var deviceInfo = new DeviceInfo
                {
                    Name = Environment.MachineName,
                    IP = SimpleNetworkService.GetLocalIPAddress(),
                    ConnectedAt = DateTime.Now
                };

                await SendNetworkMessage("DEVICE_INFO", JsonSerializer.Serialize(deviceInfo));

                // طلب مزامنة البيانات
                await SendNetworkMessage("SYNC_REQUEST", "");
            }
            else
            {
                ConnectButton.IsEnabled = true;
            }
        }

        private void Disconnect_Click(object sender, RoutedEventArgs e)
        {
            _networkService.Disconnect();
        }

        private async void ScanNetwork_Click(object sender, RoutedEventArgs e)
        {
            if (_isScanning) return;

            _isScanning = true;
            ScanStatusText.Text = "🔍 جاري البحث عن الأجهزة...";
            DevicesListBox.Items.Clear();

            try
            {
                var devices = await SimpleNetworkService.ScanNetwork();
                
                foreach (var device in devices)
                {
                    DevicesListBox.Items.Add(device);
                }

                ScanStatusText.Text = $"تم العثور على {devices.Count} جهاز";
                OnLog($"🔍 تم العثور على {devices.Count} جهاز في الشبكة");
            }
            catch (Exception ex)
            {
                ScanStatusText.Text = $"خطأ في البحث: {ex.Message}";
                OnLog($"❌ خطأ في البحث: {ex.Message}");
            }
            finally
            {
                _isScanning = false;
            }
        }

        private void DevicesListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DevicesListBox.SelectedItem is string selectedIP)
            {
                ServerIPTextBox.Text = selectedIP;
                OnLog($"📍 تم اختيار الجهاز: {selectedIP}");
            }
        }

        private async void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            if (_networkService.IsConnected)
            {
                await _networkService.SendSimpleData("TEST", "اختبار الاتصال");
                OnLog("🧪 تم إرسال رسالة اختبار");
            }
        }

        private void RefreshIP_Click(object sender, RoutedEventArgs e)
        {
            RefreshLocalIP();
            OnLog("🔄 تم تحديث عنوان IP المحلي");
        }

        private void ClearLog_Click(object sender, RoutedEventArgs e)
        {
            LogTextBlock.Text = "جاهز للاتصال...";
        }

        // تحديث حالة الاتصال
        private void UpdateConnectionStatus(bool isConnected)
        {
            if (isConnected)
            {
                ConnectionStatusText.Text = "🟢 متصل";
                ConnectionStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0x28, 0xA7, 0x45));
            }
            else
            {
                ConnectionStatusText.Text = "🔴 غير متصل";
                ConnectionStatusText.Foreground = new SolidColorBrush(Color.FromRgb(0xDC, 0x35, 0x45));
            }
        }

        // إظهار تنبيه الاتصال
        private void ShowConnectionNotification(string message, bool isSuccess)
        {
            try
            {
                if (isSuccess)
                {
                    NotificationHelper.ShowSuccess(message);
                }
                else
                {
                    NotificationHelper.ShowWarning(message);
                }
                OnLog(message);
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إظهار التنبيه: {ex.Message}");
            }
        }

        // إظهار تنبيه مزامنة البيانات
        private void ShowDataSyncNotification(string message)
        {
            try
            {
                NotificationHelper.ShowInfo(message);
                OnLog($"🔄 {message}");
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إظهار تنبيه المزامنة: {ex.Message}");
            }
        }

        // تحديث قائمة الأجهزة
        private void UpdateDevicesList()
        {
            DevicesListBox.Items.Clear();
            foreach (var device in _connectedDevices)
            {
                DevicesListBox.Items.Add($"🖥️ {device}");
            }

            // تحديث عداد الأجهزة
            ConnectedDevicesCount.Text = $"{_connectedDevices.Count} جهاز متصل";
        }

        // تحديث النافذة الرئيسية
        private void RefreshMainWindow()
        {
            try
            {
                var mainWindow = Application.Current.MainWindow as MainWindow;
                mainWindow?.RefreshCurrentView();
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في تحديث النافذة الرئيسية: {ex.Message}");
            }
        }

        // إرسال رسالة شبكة
        private async Task SendNetworkMessage(string type, string data)
        {
            try
            {
                var message = new NetworkMessage
                {
                    Type = type,
                    Data = data,
                    Timestamp = DateTime.Now
                };

                var json = JsonSerializer.Serialize(message);
                await _networkService.SendSimpleData(type, json);
            }
            catch (Exception ex)
            {
                OnLog($"❌ خطأ في إرسال الرسالة: {ex.Message}");
            }
        }

        // تنظيف الموارد
        public void Cleanup()
        {
            _networkService?.Disconnect();
        }
    }

    // نموذج رسالة الشبكة
    public class NetworkMessage
    {
        public string Type { get; set; } = "";
        public string Data { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }

    // نموذج معلومات الجهاز
    public class DeviceInfo
    {
        public string Name { get; set; } = "";
        public string IP { get; set; } = "";
        public DateTime ConnectedAt { get; set; }
    }
}
