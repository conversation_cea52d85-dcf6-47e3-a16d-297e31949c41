﻿#pragma checksum "..\..\..\..\Views\NetworkSettingsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "78ED1C6DADCE7C3B90E9970591983E7F19EB42B4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// NetworkSettingsView
    /// </summary>
    public partial class NetworkSettingsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 46 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocalIPTextBox;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartServerButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopServerButton;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ServerIPTextBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectButton;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DisconnectButton;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SyncButton;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox DevicesListBox;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ScanStatusText;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\NetworkSettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LogTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/networksettingsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\NetworkSettingsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 15 "..\..\..\..\Views\NetworkSettingsView.xaml"
            ((System.Windows.Controls.ScrollViewer)(target)).PreviewMouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.ScrollViewer_PreviewMouseWheel);
            
            #line default
            #line hidden
            return;
            case 2:
            this.LocalIPTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            
            #line 50 "..\..\..\..\Views\NetworkSettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshIP_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.StartServerButton = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Views\NetworkSettingsView.xaml"
            this.StartServerButton.Click += new System.Windows.RoutedEventHandler(this.StartServer_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.StopServerButton = ((System.Windows.Controls.Button)(target));
            
            #line 75 "..\..\..\..\Views\NetworkSettingsView.xaml"
            this.StopServerButton.Click += new System.Windows.RoutedEventHandler(this.StopServer_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ServerIPTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            
            #line 106 "..\..\..\..\Views\NetworkSettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ScanNetwork_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ConnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\..\..\Views\NetworkSettingsView.xaml"
            this.ConnectButton.Click += new System.Windows.RoutedEventHandler(this.ConnectToServer_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.DisconnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\Views\NetworkSettingsView.xaml"
            this.DisconnectButton.Click += new System.Windows.RoutedEventHandler(this.Disconnect_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SyncButton = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Views\NetworkSettingsView.xaml"
            this.SyncButton.Click += new System.Windows.RoutedEventHandler(this.RequestSync_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DevicesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 131 "..\..\..\..\Views\NetworkSettingsView.xaml"
            this.DevicesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DevicesListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ScanStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            
            #line 154 "..\..\..\..\Views\NetworkSettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearLog_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.LogTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

