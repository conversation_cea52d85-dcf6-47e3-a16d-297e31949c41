using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using DebtManagementApp.Models;

namespace DebtManagementApp.Views
{
    public partial class CustomReportWindow : Window
    {
        private List<Person> _allPersons;
        private List<Debt> _allDebts;

        public CustomReportWindow(List<Person> persons, List<Debt> debts)
        {
            InitializeComponent();
            _allPersons = persons;
            _allDebts = debts;
            
            InitializeControls();
            LoadInitialData();
        }

        private void InitializeControls()
        {
            // تعيين التواريخ الافتراضية
            FromDatePicker.SelectedDate = DateTime.Now.AddMonths(-1);
            ToDatePicker.SelectedDate = DateTime.Now.AddMonths(1);
            
            // تحميل قائمة الأشخاص
            foreach (var person in _allPersons)
            {
                PersonComboBox.Items.Add(person);
            }
        }

        private void LoadInitialData()
        {
            // عرض جميع الديون في البداية
            ApplyFilter_Click(null, null);
        }

        private void ApplyFilter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filteredDebts = _allDebts.AsEnumerable();

                // فلترة بالتاريخ
                if (FromDatePicker.SelectedDate.HasValue)
                {
                    filteredDebts = filteredDebts.Where(d => d.DueDate >= FromDatePicker.SelectedDate.Value);
                }

                if (ToDatePicker.SelectedDate.HasValue)
                {
                    filteredDebts = filteredDebts.Where(d => d.DueDate <= ToDatePicker.SelectedDate.Value);
                }

                // فلترة بالحالة
                if (StatusComboBox.SelectedItem is ComboBoxItem statusItem)
                {
                    var status = statusItem.Content.ToString();
                    switch (status)
                    {
                        case "مدفوع":
                            filteredDebts = filteredDebts.Where(d => d.IsSettled);
                            break;
                        case "غير مدفوع":
                            filteredDebts = filteredDebts.Where(d => !d.IsSettled && d.DueDate >= DateTime.Now);
                            break;
                        case "متأخر":
                            filteredDebts = filteredDebts.Where(d => !d.IsSettled && d.DueDate < DateTime.Now);
                            break;
                    }
                }

                // فلترة بنطاق المبلغ
                if (decimal.TryParse(MinAmountTextBox.Text, out decimal minAmount))
                {
                    filteredDebts = filteredDebts.Where(d => d.Amount >= minAmount);
                }

                if (decimal.TryParse(MaxAmountTextBox.Text, out decimal maxAmount))
                {
                    filteredDebts = filteredDebts.Where(d => d.Amount <= maxAmount);
                }

                // فلترة بالشخص
                if (PersonComboBox.SelectedItem is Person selectedPerson)
                {
                    filteredDebts = filteredDebts.Where(d => d.PersonId == selectedPerson.Id);
                }

                // إعداد البيانات للعرض
                var reportData = filteredDebts.Select(d => new
                {
                    PersonName = d.PersonName,
                    Description = d.Description,
                    Amount = d.Amount,
                    DueDate = d.DueDate,
                    StatusText = d.IsSettled ? "مدفوع" : (d.DueDate < DateTime.Now ? "متأخر" : "مستحق")
                }).ToList();

                CustomReportDataGrid.ItemsSource = reportData;
                
                // تحديث عنوان النتائج
                ResultsTitle.Text = $"📊 نتائج التقرير المخصص ({reportData.Count} عنصر)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلترة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetFilter_Click(object sender, RoutedEventArgs e)
        {
            // إعادة تعيين جميع الفلاتر
            FromDatePicker.SelectedDate = DateTime.Now.AddMonths(-1);
            ToDatePicker.SelectedDate = DateTime.Now.AddMonths(1);
            StatusComboBox.SelectedIndex = 0;
            MinAmountTextBox.Text = "";
            MaxAmountTextBox.Text = "";
            PersonComboBox.SelectedIndex = 0;
            
            // تطبيق الفلترة المعاد تعيينها
            ApplyFilter_Click(sender, e);
        }

        private void ExportCustomReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة تصدير التقرير المخصص قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintCustomReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود بيانات للطباعة
                if (CustomReportDataGrid.Items.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء مستند للطباعة
                var printDocument = CreateCustomReportPrintDocument();

                // إظهار معاينة الطباعة
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    IDocumentPaginatorSource idpSource = printDocument;
                    printDialog.PrintDocument(idpSource.DocumentPaginator, "التقرير المخصص");
                    MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FlowDocument CreateCustomReportPrintDocument()
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(30);
            document.FontFamily = new FontFamily("Arial");
            document.FontSize = 11;
            document.FlowDirection = FlowDirection.RightToLeft;
            document.PageWidth = 793.7; // A4 width in pixels (210mm)
            document.PageHeight = 1122.5; // A4 height in pixels (297mm)
            document.ColumnWidth = double.PositiveInfinity;

            // عنوان التقرير
            var title = new Paragraph();
            title.FontSize = 18;
            title.FontWeight = FontWeights.Bold;
            title.TextAlignment = TextAlignment.Center;
            title.Margin = new Thickness(0, 0, 0, 15);
            title.Inlines.Add(new Run("التقرير المخصص"));
            document.Blocks.Add(title);

            // معلومات التقرير والفترة الزمنية
            var headerInfo = new Paragraph();
            headerInfo.FontSize = 11;
            headerInfo.Margin = new Thickness(0, 0, 0, 15);
            headerInfo.TextAlignment = TextAlignment.Center;

            var fromDate = FromDatePicker.SelectedDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
            var toDate = ToDatePicker.SelectedDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
            var selectedPerson = PersonComboBox.SelectedItem as Person;
            var personName = selectedPerson?.Name ?? "جميع الأشخاص";

            headerInfo.Inlines.Add(new Run($"الفترة: من {fromDate} إلى {toDate} | الشخص: {personName} | تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}"));
            document.Blocks.Add(headerInfo);

            // الحصول على البيانات المفلترة
            var filteredData = GetFilteredData();

            // إحصائيات التقرير المخصص
            AddCustomReportStatistics(document, filteredData);

            // جدول البيانات المخصص
            AddCustomReportTable(document, filteredData);

            // تذييل التقرير
            var footer = new Paragraph();
            footer.FontSize = 8;
            footer.TextAlignment = TextAlignment.Center;
            footer.Margin = new Thickness(0, 15, 0, 0);
            footer.Foreground = Brushes.Gray;
            footer.Inlines.Add(new Run($"نظام إدارة الديون - التقرير المخصص - {DateTime.Now:yyyy/MM/dd HH:mm}"));
            document.Blocks.Add(footer);

            return document;
        }

        private void AddCustomReportStatistics(FlowDocument document, List<Debt> filteredData)
        {
            // حساب الإحصائيات
            var totalAmount = filteredData.Sum(d => d.Amount);
            var settledAmount = filteredData.Where(d => d.IsSettled).Sum(d => d.Amount);
            var pendingAmount = totalAmount - settledAmount;
            var settledCount = filteredData.Count(d => d.IsSettled);
            var pendingCount = filteredData.Count(d => !d.IsSettled);
            var overdueDebts = filteredData.Where(d => !d.IsSettled && d.DueDate < DateTime.Now).ToList();
            var overdueAmount = overdueDebts.Sum(d => d.Amount);

            // إحصائيات التقرير المخصص
            var statistics = new Paragraph();
            statistics.FontSize = 11;
            statistics.Margin = new Thickness(0, 0, 0, 15);
            statistics.TextAlignment = TextAlignment.Center;
            statistics.Inlines.Add(new Run("إحصائيات التقرير المخصص:") { FontWeight = FontWeights.Bold });
            statistics.Inlines.Add(new LineBreak());
            statistics.Inlines.Add(new Run($"إجمالي: {filteredData.Count} دين ({totalAmount:N0} د.ع) | مسدد: {settledCount} ({settledAmount:N0} د.ع) | متبقي: {pendingCount} ({pendingAmount:N0} د.ع)"));

            if (overdueDebts.Any())
            {
                statistics.Inlines.Add(new LineBreak());
                statistics.Inlines.Add(new Run($"متأخر: {overdueDebts.Count} دين ({overdueAmount:N0} د.ع)") { Foreground = Brushes.Red, FontWeight = FontWeights.Bold });
            }

            document.Blocks.Add(statistics);
        }

        private void AddCustomReportTable(FlowDocument document, List<Debt> filteredData)
        {
            // عنوان الجدول
            var tableTitle = new Paragraph();
            tableTitle.FontSize = 12;
            tableTitle.FontWeight = FontWeights.Bold;
            tableTitle.Margin = new Thickness(0, 10, 0, 10);
            tableTitle.TextAlignment = TextAlignment.Center;
            tableTitle.Inlines.Add(new Run("تفاصيل الديون المفلترة"));
            document.Blocks.Add(tableTitle);

            // إنشاء الجدول
            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);
            table.FontSize = 9;
            table.Margin = new Thickness(0, 5, 0, 0);

            // تعريف الأعمدة بنسب مئوية
            table.Columns.Add(new TableColumn() { Width = new GridLength(20, GridUnitType.Star) }); // اسم الشخص
            table.Columns.Add(new TableColumn() { Width = new GridLength(15, GridUnitType.Star) }); // المبلغ
            table.Columns.Add(new TableColumn() { Width = new GridLength(12, GridUnitType.Star) }); // تاريخ الدين
            table.Columns.Add(new TableColumn() { Width = new GridLength(12, GridUnitType.Star) }); // تاريخ الاستحقاق
            table.Columns.Add(new TableColumn() { Width = new GridLength(15, GridUnitType.Star) }); // نوع العملية
            table.Columns.Add(new TableColumn() { Width = new GridLength(20, GridUnitType.Star) }); // الوصف
            table.Columns.Add(new TableColumn() { Width = new GridLength(6, GridUnitType.Star) });  // الحالة

            // رأس الجدول
            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;

            AddTableCell(headerRow, "اسم الشخص", true);
            AddTableCell(headerRow, "المبلغ (د.ع)", true);
            AddTableCell(headerRow, "تاريخ الدين", true);
            AddTableCell(headerRow, "تاريخ الاستحقاق", true);
            AddTableCell(headerRow, "نوع العملية", true);
            AddTableCell(headerRow, "الوصف", true);
            AddTableCell(headerRow, "الحالة", true);

            headerRowGroup.Rows.Add(headerRow);
            table.RowGroups.Add(headerRowGroup);

            // بيانات الجدول
            var dataRowGroup = new TableRowGroup();

            foreach (var debt in filteredData.OrderByDescending(d => d.Date))
            {
                var person = _allPersons.FirstOrDefault(p => p.Id == debt.PersonId);
                var row = new TableRow();

                // تلوين الصفوف المتأخرة
                if (!debt.IsSettled && debt.DueDate < DateTime.Now)
                {
                    row.Background = new SolidColorBrush(Color.FromRgb(255, 240, 240)); // خلفية حمراء فاتحة
                }

                AddTableCell(row, person?.Name ?? "غير معروف");
                AddTableCell(row, debt.Amount.ToString("N0"));
                AddTableCell(row, debt.Date.ToString("yyyy/MM/dd"));
                AddTableCell(row, debt.DueDate.ToString("yyyy/MM/dd"));
                AddTableCell(row, debt.OperationType ?? "غير محدد");
                AddTableCell(row, debt.Description ?? "");
                AddTableCell(row, debt.IsSettled ? "مسدد" : "غير مسدد");

                dataRowGroup.Rows.Add(row);
            }
            table.RowGroups.Add(dataRowGroup);

            document.Blocks.Add(table);
        }

        private void AddTableCell(TableRow row, string content, bool isHeader = false)
        {
            var cell = new TableCell();
            cell.BorderBrush = Brushes.Black;
            cell.BorderThickness = new Thickness(1);
            cell.Padding = new Thickness(3);

            var paragraph = new Paragraph();
            paragraph.TextAlignment = TextAlignment.Center;
            paragraph.Margin = new Thickness(0);
            paragraph.FontSize = isHeader ? 9 : 8;
            paragraph.LineHeight = 10;

            if (isHeader)
            {
                paragraph.FontWeight = FontWeights.Bold;
                cell.Background = Brushes.LightGray;
            }

            // تقصير النص الطويل
            var displayContent = content;
            if (!isHeader && content.Length > 30)
            {
                displayContent = content.Substring(0, 27) + "...";
            }

            paragraph.Inlines.Add(new Run(displayContent));
            cell.Blocks.Add(paragraph);
            row.Cells.Add(cell);
        }

        private List<Debt> GetFilteredData()
        {
            var filteredDebts = _allDebts.AsEnumerable();

            // فلترة حسب التاريخ
            if (FromDatePicker.SelectedDate.HasValue)
            {
                filteredDebts = filteredDebts.Where(d => d.Date >= FromDatePicker.SelectedDate.Value);
            }

            if (ToDatePicker.SelectedDate.HasValue)
            {
                filteredDebts = filteredDebts.Where(d => d.Date <= ToDatePicker.SelectedDate.Value);
            }

            // فلترة حسب الشخص
            if (PersonComboBox.SelectedItem is Person selectedPerson)
            {
                filteredDebts = filteredDebts.Where(d => d.PersonId == selectedPerson.Id);
            }

            // فلترة حسب الحالة
            if (StatusComboBox.SelectedIndex == 1) // مسدد
            {
                filteredDebts = filteredDebts.Where(d => d.IsSettled);
            }
            else if (StatusComboBox.SelectedIndex == 2) // غير مسدد
            {
                filteredDebts = filteredDebts.Where(d => !d.IsSettled);
            }

            return filteredDebts.ToList();
        }

        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
