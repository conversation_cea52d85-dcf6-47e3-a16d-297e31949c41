<Window x:Class="DebtManagementApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DebtManagementApp"
        mc:Ignorable="d"
        Title="نظام إدارة الديون الحديث" Height="900" Width="1400"
        Background="{StaticResource AppBackground}"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 🎨 شريط العنوان الحديث -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryGradient}" Padding="32,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- الشعار والعنوان -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="{StaticResource BackgroundPrimary}"
                            CornerRadius="20" Width="64" Height="64"
                            Margin="0,0,20,0" Effect="{StaticResource MediumShadow}">
                        <TextBlock Text="🏢" FontSize="36"
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="نظام إدارة الديون الحديث"
                                   Style="{StaticResource HeadingMedium}"
                                   Foreground="{StaticResource TextInverse}"/>
                        <TextBlock Text="إدارة شاملة لأعمال الحديد والديون والحسابات التجارية"
                                   Style="{StaticResource BodyMedium}"
                                   Foreground="{StaticResource TextInverse}"
                                   Opacity="0.9" Margin="0,4,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- معلومات الحالة -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#40FFFFFF" CornerRadius="12" Padding="16,8" Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🕐" FontSize="16" Foreground="{StaticResource TextInverse}"
                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock x:Name="TimeDisplay" Text="00:00:00"
                                       Style="{StaticResource BodyMedium}"
                                       Foreground="{StaticResource TextInverse}"
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Border>
                    <Border Background="#40FFFFFF" CornerRadius="12" Padding="16,8">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="8" Height="8" Fill="#4ADE80"
                                     Margin="0,0,8,0" VerticalAlignment="Center"/>
                            <TextBlock Text="النظام متصل"
                                       Style="{StaticResource BodyMedium}"
                                       Foreground="{StaticResource TextInverse}"
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 🎯 المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="24">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 📋 الشريط الجانبي للتنقل -->
            <Border Grid.Column="0" Style="{StaticResource ElevatedCard}" Margin="0,0,12,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              Style="{StaticResource ModernScrollViewer}"
                              PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                    <StackPanel>

                        <!-- عنوان التنقل -->
                        <TextBlock Text="📋 القائمة الرئيسية"
                                   Style="{StaticResource HeadingSmall}"
                                   Margin="0,0,0,24"/>

                        <!-- أزرار التنقل -->
                        <StackPanel x:Name="NavigationButtons">

                            <!-- العودة للوحة المعلومات -->
                            <Button x:Name="BtnDashboard" Content="🏠 الصفحة الرئيسية"
                                    Style="{StaticResource SecondaryButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="ReturnToDashboard_Click"/>

                            <!-- إدارة الأشخاص - أزرق -->
                            <Button x:Name="BtnPersons" Content="👥 إدارة الأشخاص"
                                    Style="{StaticResource PrimaryButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToPersons"/>

                            <!-- إدارة العمال - أخضر -->
                            <Button x:Name="BtnWorkers" Content="👷 إدارة العمال"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToWorkers">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                    <GradientStop Color="#10B981" Offset="0"/>
                                                    <GradientStop Color="#059669" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>

                            <!-- ديون الأشخاص - برتقالي -->
                            <Button x:Name="BtnDebts" Content="💰 ديون الأشخاص"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToDebts">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                    <GradientStop Color="#F59E0B" Offset="0"/>
                                                    <GradientStop Color="#D97706" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>

                            <!-- ديون المعمل - بنفسجي -->
                            <Button x:Name="BtnFactoryDebts" Content="🏭 ديون المعمل"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToFactoryDebts">
                                <Button.Style>
                                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                    <GradientStop Color="#8B5CF6" Offset="0"/>
                                                    <GradientStop Color="#7C3AED" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>

                            <!-- الديون المتأخرة -->
                            <Button x:Name="BtnOverdue" Content="⚠️ الديون المتأخرة"
                                    Style="{StaticResource AccentButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToOverdue"/>

                            <!-- التقارير -->
                            <Button x:Name="BtnReports" Content="📊 التقارير"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToReports"/>

                            <!-- النسخ الاحتياطي -->
                            <Button x:Name="BtnBackup" Content="💾 النسخ الاحتياطي"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToBackup"/>

                            <!-- الحاسبات -->
                            <TextBlock Text="🔧 الحاسبات"
                                       Style="{StaticResource HeadingSmall}"
                                       Margin="0,24,0,16"/>

                            <Button x:Name="BtnIronCalc" Content="⚙️ حاسبة الحديد"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToIronCalc"/>

                            <Button x:Name="BtnCuttingCalc" Content="🔪 حاسبة التقطيع"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToCuttingCalc"/>

                            <!-- أدوات أخرى -->
                            <TextBlock Text="🛠️ أدوات أخرى"
                                       Style="{StaticResource HeadingSmall}"
                                       Margin="0,24,0,16"/>

                            <Button x:Name="BtnReminders" Content="🔔 نظام التذكير"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToReminders"/>

                            <Button x:Name="BtnSearch" Content="🔍 البحث والفلترة"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToSearch"/>

                            <Button x:Name="BtnSettings" Content="⚙️ الإعدادات"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToSettings"/>

                            <Button x:Name="BtnNetwork" Content="🌐 إعدادات الشبكة"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToNetwork"/>

                            <Button x:Name="BtnOneDrive" Content="☁️ OneDrive"
                                    Style="{StaticResource OutlineButton}"
                                    Margin="0,0,0,12" HorizontalAlignment="Stretch"
                                    Click="NavigateToOneDrive"/>

                        </StackPanel>

                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- 🎨 منطقة المحتوى الرئيسي -->
            <Border Grid.Column="1" Style="{StaticResource ModernCard}" Margin="12,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- شريط العنوان للمحتوى -->
                    <Border Grid.Row="0" Background="{StaticResource BackgroundSecondary}"
                            CornerRadius="12,12,0,0" Padding="24,16" Margin="-24,-24,-24,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock x:Name="ContentTitle" Text="🏠 الصفحة الرئيسية"
                                       Style="{StaticResource HeadingMedium}"/>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button Content="🔄 تحديث" Style="{StaticResource OutlineButton}"
                                        Margin="0,0,12,0" Click="RefreshContent"/>
                                <Button Content="📤 تصدير" Style="{StaticResource OutlineButton}"
                                        Click="ExportContent"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- المحتوى الديناميكي -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,24,0,0"
                                  Style="{StaticResource ModernScrollViewer}"
                                  PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                        <ContentControl x:Name="MainContent">

                            <!-- المحتوى الافتراضي - لوحة المعلومات -->
                            <Grid x:Name="DashboardContent">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- بطاقات الإحصائيات -->
                                <UniformGrid Grid.Row="0" Columns="4" Margin="0,0,0,24">

                                    <Border Style="{StaticResource ModernCard}" Background="#F0F4FF" Margin="0,0,6,0">
                                        <StackPanel HorizontalAlignment="Center" Margin="12">
                                            <TextBlock Text="👥" FontSize="28" HorizontalAlignment="Center"/>
                                            <TextBlock Text="إجمالي الأشخاص" FontSize="12"
                                                       HorizontalAlignment="Center" Margin="0,6,0,0"/>
                                            <TextBlock x:Name="TotalPersonsCount" Text="0"
                                                       FontSize="20" FontWeight="SemiBold"
                                                       Foreground="{StaticResource PrimaryColor}"
                                                       HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Style="{StaticResource ModernCard}" Background="#F0FDF4" Margin="3,0">
                                        <StackPanel HorizontalAlignment="Center" Margin="12">
                                            <TextBlock Text="💰" FontSize="28" HorizontalAlignment="Center"/>
                                            <TextBlock Text="إجمالي الديون" FontSize="12"
                                                       HorizontalAlignment="Center" Margin="0,6,0,0"/>
                                            <TextBlock x:Name="TotalDebtsCount" Text="0"
                                                       FontSize="20" FontWeight="SemiBold"
                                                       Foreground="{StaticResource SecondaryColor}"
                                                       HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Style="{StaticResource ModernCard}" Background="#FFFBEB" Margin="3,0">
                                        <StackPanel HorizontalAlignment="Center" Margin="12">
                                            <TextBlock Text="⚠️" FontSize="28" HorizontalAlignment="Center"/>
                                            <TextBlock Text="ديون متأخرة" FontSize="12"
                                                       HorizontalAlignment="Center" Margin="0,6,0,0"/>
                                            <TextBlock x:Name="OverdueDebtsCount" Text="0"
                                                       FontSize="20" FontWeight="SemiBold"
                                                       Foreground="{StaticResource AccentColor}"
                                                       HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Style="{StaticResource ModernCard}" Background="#FEF2F2" Margin="6,0,0,0">
                                        <StackPanel HorizontalAlignment="Center" Margin="12">
                                            <TextBlock Text="💵" FontSize="28" HorizontalAlignment="Center"/>
                                            <TextBlock Text="إجمالي المبلغ" FontSize="12"
                                                       HorizontalAlignment="Center" Margin="0,6,0,0"/>
                                            <TextBlock x:Name="TotalAmountDisplay" Text="0 دينار"
                                                       FontSize="16" FontWeight="SemiBold"
                                                       Foreground="{StaticResource ErrorColor}"
                                                       HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                        </StackPanel>
                                    </Border>

                                </UniformGrid>

                                <!-- الأنشطة الأخيرة -->
                                <Border Grid.Row="1" Style="{StaticResource ModernCard}" Margin="0,0,0,24">
                                    <StackPanel>
                                        <TextBlock Text="📈 الأنشطة الأخيرة" Style="{StaticResource HeadingSmall}" Margin="0,0,0,16"/>
                                        <ListView x:Name="RecentActivitiesList" Height="200"
                                                  Background="Transparent" BorderThickness="0">
                                            <ListView.ContextMenu>
                                                <ContextMenu x:Name="ActivityContextMenu">
                                                    <MenuItem Header="🗑️ حذف النشاط" Click="DeleteActivity_Click">
                                                        <MenuItem.Style>
                                                            <Style TargetType="MenuItem">
                                                                <Setter Property="Foreground" Value="#DC3545"/>
                                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                            </Style>
                                                        </MenuItem.Style>
                                                    </MenuItem>
                                                    <MenuItem Header="↩️ التراجع عن النشاط" Click="UndoActivity_Click">
                                                        <MenuItem.Style>
                                                            <Style TargetType="MenuItem">
                                                                <Setter Property="Foreground" Value="#FD7E14"/>
                                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                            </Style>
                                                        </MenuItem.Style>
                                                    </MenuItem>
                                                </ContextMenu>
                                            </ListView.ContextMenu>
                                            <ListView.ItemTemplate>
                                                <DataTemplate>
                                                    <Border Background="{StaticResource BackgroundSecondary}"
                                                            CornerRadius="8" Padding="16,12" Margin="0,0,0,8">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0" Text="{Binding Icon}" FontSize="16"
                                                                       VerticalAlignment="Center" Margin="0,0,12,0"/>
                                                            <TextBlock Grid.Column="1" Text="{Binding Description}"
                                                                       Style="{StaticResource BodyMedium}"
                                                                       VerticalAlignment="Center"/>
                                                            <TextBlock Grid.Column="2" Text="{Binding Time}"
                                                                       Style="{StaticResource BodySmall}"
                                                                       VerticalAlignment="Center"/>
                                                        </Grid>
                                                    </Border>
                                                </DataTemplate>
                                            </ListView.ItemTemplate>
                                        </ListView>
                                    </StackPanel>
                                </Border>

                                <!-- الإجراءات السريعة -->
                                <Border Grid.Row="2" Style="{StaticResource ModernCard}">
                                    <StackPanel>
                                        <TextBlock Text="⚡ الإجراءات السريعة" Style="{StaticResource HeadingSmall}" Margin="0,0,0,16"/>
                                        <UniformGrid Columns="2" Rows="2">
                                            <!-- إضافة شخص جديد - أزرق -->
                                            <Button Content="➕ إضافة شخص جديد" Style="{StaticResource PrimaryButton}"
                                                    Margin="0,0,4,4" Click="QuickAddPerson"/>

                                            <!-- إضافة دين جديد - أخضر -->
                                            <Button Content="💰 إضافة دين جديد" Margin="4,0,0,4" Click="QuickAddDebt">
                                                <Button.Style>
                                                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                                    <GradientStop Color="#10B981" Offset="0"/>
                                                                    <GradientStop Color="#059669" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <!-- عرض تقرير سريع - برتقالي -->
                                            <Button Content="📊 عرض تقرير سريع" Margin="0,4,4,0" Click="QuickReport">
                                                <Button.Style>
                                                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                                    <GradientStop Color="#F59E0B" Offset="0"/>
                                                                    <GradientStop Color="#D97706" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <!-- اختبار التراجع - بنفسجي -->
                                            <Button Content="🧪 اختبار التراجع" Margin="4,4,0,0" Click="TestUndo">
                                                <Button.Style>
                                                    <Style TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
                                                        <Setter Property="Background">
                                                            <Setter.Value>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                                    <GradientStop Color="#8B5CF6" Offset="0"/>
                                                                    <GradientStop Color="#7C3AED" Offset="1"/>
                                                                </LinearGradientBrush>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>
                                        </UniformGrid>
                                    </StackPanel>
                                </Border>

                            </Grid>

                        </ContentControl>
                    </ScrollViewer>

                </Grid>
            </Border>

        </Grid>

    </Grid>
</Window>

