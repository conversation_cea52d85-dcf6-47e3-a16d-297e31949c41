using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using System.Windows;
using DebtManagementApp.Helpers;

namespace DebtManagementApp.ViewModels
{
    public class IronCalculatorViewModel : INotifyPropertyChanged
    {
        #region Properties

        private string _selectedIronType = "حديد عادي";
        private string _selectedDiameter = "8";
        private string _thickness = "5";
        private string _length = "1000";
        private string _width = "500";
        private string _quantity = "1";
        private string _pricePerTonUsd = "1200"; // سعر بالدولار
        private string _usdToIqdRate = "1500"; // سعر صرف الدولار
        private bool _includeTransport = false;
        private string _transportCost = "50000";
        private string _weightPerBar = "0 كغم";
        private string _totalWeight = "0 كغم";
        private string _ironCostUsd = "$0";
        private string _ironCostIqd = "0 د.ع";
        private string _totalCostUsd = "$0";
        private string _totalCostIqd = "0 د.ع";
        private string _calculationDetails = "أدخل البيانات وانقر على 'حساب التكلفة'";
        private decimal _finalPriceIqd = 0;

        public string SelectedIronType
        {
            get => _selectedIronType;
            set
            {
                _selectedIronType = value;
                OnPropertyChanged(nameof(SelectedIronType));
            }
        }

        public string SelectedDiameter
        {
            get => _selectedDiameter;
            set
            {
                _selectedDiameter = value;
                OnPropertyChanged(nameof(SelectedDiameter));
            }
        }

        public string Thickness
        {
            get => _thickness;
            set
            {
                _thickness = value;
                OnPropertyChanged(nameof(Thickness));
            }
        }

        public string Length
        {
            get => _length;
            set
            {
                _length = value;
                OnPropertyChanged(nameof(Length));
            }
        }

        public string Width
        {
            get => _width;
            set
            {
                _width = value;
                OnPropertyChanged(nameof(Width));
            }
        }

        public string Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged(nameof(Quantity));
            }
        }

        public string PricePerTonUsd
        {
            get => _pricePerTonUsd;
            set
            {
                _pricePerTonUsd = value;
                OnPropertyChanged(nameof(PricePerTonUsd));
            }
        }

        public string UsdToIqdRate
        {
            get => _usdToIqdRate;
            set
            {
                _usdToIqdRate = value;
                OnPropertyChanged(nameof(UsdToIqdRate));
            }
        }

        public bool IncludeTransport
        {
            get => _includeTransport;
            set
            {
                _includeTransport = value;
                OnPropertyChanged(nameof(IncludeTransport));
                OnPropertyChanged(nameof(TransportCostVisibility));
            }
        }

        public string TransportCost
        {
            get => _transportCost;
            set
            {
                _transportCost = value;
                OnPropertyChanged(nameof(TransportCost));
            }
        }

        // خصائص التكلفة بالدولار والدينار
        public string IronCostUsd
        {
            get => _ironCostUsd;
            set { _ironCostUsd = value; OnPropertyChanged(nameof(IronCostUsd)); }
        }
        public string IronCostIqd
        {
            get => _ironCostIqd;
            set { _ironCostIqd = value; OnPropertyChanged(nameof(IronCostIqd)); }
        }
        public string TotalCostUsd
        {
            get => _totalCostUsd;
            set { _totalCostUsd = value; OnPropertyChanged(nameof(TotalCostUsd)); }
        }

        public string TotalWeight
        {
            get => _totalWeight;
            set
            {
                _totalWeight = value;
                OnPropertyChanged(nameof(TotalWeight));
            }
        }

        public string IronCost
        {
            get => _ironCostIqd;
            set
            {
                _ironCostIqd = value;
                OnPropertyChanged(nameof(IronCost));
            }
        }

        public string TotalCost
        {
            get => _totalCostIqd;
            set
            {
                _totalCostIqd = value;
                OnPropertyChanged(nameof(TotalCost));
            }
        }

        public decimal FinalPriceIqd
        {
            get => _finalPriceIqd;
            set
            {
                _finalPriceIqd = value;
                OnPropertyChanged(nameof(FinalPriceIqd));
            }
        }

        public string CalculationDetails
        {
            get => _calculationDetails;
            set
            {
                _calculationDetails = value;
                OnPropertyChanged(nameof(CalculationDetails));
            }
        }

        public string FormattedTransportCost => CalculationHelper.FormatIqdCurrency(decimal.TryParse(TransportCost, out var cost) ? cost : 0);

        public Visibility TransportCostVisibility => IncludeTransport ? Visibility.Visible : Visibility.Collapsed;

        public ObservableCollection<string> IronTypes { get; set; }
        public ObservableCollection<string> Diameters { get; set; }

        #endregion

        #region Commands

        public ICommand CalculateCommand { get; }
        public ICommand SaveResultCommand { get; }
        public ICommand PrintReportCommand { get; }

        #endregion

        // تعديل الخصائص لتكون قابلة لأن تكون null
        // إزالة التكرار: SelectedDiameter معرف مسبقاً بشكل صحيح
        public string WeightPerBar
        {
            get => _weightPerBar;
            set
            {
                _weightPerBar = value;
                OnPropertyChanged(nameof(WeightPerBar));
            }
        }

        public IronCalculatorViewModel()
        {
            // تهيئة القوائم
            IronTypes = new ObservableCollection<string>
            {
                "حديد عادي",
                "حديد عالي المقاومة",
                "حديد مجلفن",
                "حديد مقاوم للصدأ"
            };

            Diameters = new ObservableCollection<string>
            {
                "6", "8", "10", "12", "14", "16", "18", "20", "22", "25", "28", "32"
            };

            // تهيئة الأوامر
            CalculateCommand = new RelayCommand(_ => CalculateCost());
            SaveResultCommand = new RelayCommand(_ => SaveResult());
            PrintReportCommand = new RelayCommand(_ => PrintReport());
        }

        #region Methods

        private void CalculateCost()
        {
            try
            {
                // التحقق من صحة البيانات
                if (!double.TryParse(SelectedDiameter, out double diameter) || diameter <= 0)
                {
                    MessageBox.Show("يرجى اختيار قطر صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!double.TryParse(Length, out double length) || length <= 0)
                {
                    MessageBox.Show("يرجى إدخال طول صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!int.TryParse(Quantity, out int quantity) || quantity <= 0)
                {
                    MessageBox.Show("يرجى إدخال عدد صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }


                if (!decimal.TryParse(PricePerTonUsd, out decimal pricePerTonUsd) || pricePerTonUsd <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر الطن بالدولار بشكل صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                if (!decimal.TryParse(UsdToIqdRate, out decimal usdToIqdRate) || usdToIqdRate <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صرف الدولار بشكل صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                decimal pricePerTon = pricePerTonUsd * usdToIqdRate;

                // حساب الوزن
                // الوزن = (القطر^2 × 0.00617 × الطول) كيلوغرام
                double weightPerBarKg = Math.Pow(diameter, 2) * 0.00617 * length;
                double totalWeightKg = weightPerBarKg * quantity;
                double totalWeightTon = totalWeightKg / 1000;

                // معامل نوع الحديد
                decimal ironTypeFactor = GetIronTypeFactor(SelectedIronType);

                // حساب التكلفة
                decimal ironCostValue = (decimal)totalWeightTon * pricePerTon * ironTypeFactor;
                decimal transportCostValue = 0;

                if (IncludeTransport)
                {
                    // تكلفة النقل = 7.5% من سعر الحديد بدون النقل
                    transportCostValue = ironCostValue * 0.075m;
                }

                decimal totalCostValue = ironCostValue + transportCostValue;
                FinalPriceIqd = totalCostValue;

                // تحديث النتائج
                WeightPerBar = $"{weightPerBarKg:F2} كغم";
                TotalWeight = $"{totalWeightKg:F2} كغم ({totalWeightTon:F3} طن)";
                IronCost = CalculationHelper.FormatIqdCurrency(ironCostValue);
                IronCostUsd = "$" + (pricePerTonUsd * (decimal)totalWeightTon * ironTypeFactor).ToString("F2");
                IronCostIqd = CalculationHelper.FormatIqdCurrency(ironCostValue);
                TotalCostUsd = "$" + ((pricePerTonUsd * (decimal)totalWeightTon * ironTypeFactor) + (IncludeTransport ? (transportCostValue / usdToIqdRate) : 0)).ToString("F2");
                TotalCost = CalculationHelper.FormatIqdCurrency(totalCostValue);
                FinalPriceIqd = totalCostValue;

                // تحديث التفاصيل
                CalculationDetails = $"نوع الحديد: {SelectedIronType} (معامل: {ironTypeFactor:F2})\n" +
                                   $"القطر: {diameter} مم\n" +
                                   $"الطول: {length} متر\n" +
                                   $"العدد: {quantity} قضيب\n" +
                                   $"سعر الطن (دولار): {pricePerTonUsd}$\n" +
                                   $"سعر الصرف: {usdToIqdRate} د.ع\n" +
                                   $"سعر الطن (دينار): {CalculationHelper.FormatIqdCurrency(pricePerTon)}\n" +
                                   $"الوزن الإجمالي: {totalWeightKg:F2} كغم\n" +
                                   $"تكلفة الحديد (دولار): {IronCostUsd}\n" +
                                   $"تكلفة الحديد (دينار): {IronCostIqd}";

                if (IncludeTransport)
                {
                    CalculationDetails += $"\nتكلفة النقل (7.5%): {CalculationHelper.FormatIqdCurrency(transportCostValue)}";
                    CalculationDetails += $"\nالتكلفة الإجمالية: {CalculationHelper.FormatIqdCurrency(totalCostValue)}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الحساب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private decimal GetIronTypeFactor(string ironType)
        {
            return ironType switch
            {
                "حديد عادي" => 1.0m,
                "حديد عالي المقاومة" => 1.15m,
                "حديد مجلفن" => 1.25m,
                "حديد مقاوم للصدأ" => 1.8m,
                _ => 1.0m
            };
        }

        private void SaveResult()
        {
            try
            {
                if (TotalCost == "0 د.ع")
                {
                    MessageBox.Show("يرجى حساب التكلفة أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // هنا يمكن حفظ النتيجة في قاعدة البيانات
                MessageBox.Show("تم حفظ النتيجة بنجاح", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الحفظ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintReport()
        {
            try
            {
                if (TotalCost == "0 د.ع")
                {
                    MessageBox.Show("يرجى حساب التكلفة أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // هنا يمكن إضافة وظيفة الطباعة
                MessageBox.Show("سيتم إضافة وظيفة الطباعة قريباً", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}