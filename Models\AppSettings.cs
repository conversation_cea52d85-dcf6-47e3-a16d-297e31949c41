using System;
using System.ComponentModel;
using System.IO;
using System.Text.Json;

namespace DebtManagementApp.Models
{
    /// <summary>
    /// نموذج إعدادات التطبيق
    /// </summary>
    public class AppSettings : INotifyPropertyChanged
    {
        private static AppSettings? _instance;
        private static readonly string SettingsFile = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DebtManagementApp",
            "settings.json");

        // إعدادات عامة
        private string _theme = "Light";
        private string _language = "العربية";
        private bool _autoSave = true;
        private int _autoSaveInterval = 5; // بالدقائق
        private bool _showNotifications = true;
        private bool _playNotificationSounds = true;

        // إعدادات التذكيرات
        private bool _enableReminders = true;
        private int _reminderDays = 7; // أيام قبل الاستحقاق
        private bool _showOverdueReminders = true;
        private string _reminderTime = "09:00"; // وقت التذكير اليومي

        // إعدادات النسخ الاحتياطي
        private bool _autoBackup = true;
        private int _backupInterval = 24; // بالساعات
        private string _backupLocation = "";
        private int _maxBackupFiles = 10;
        private bool _autoBackupEnabled = true;
        private int _backupFrequency = 0; // 0=يومياً، 1=أسبوعياً، 2=شهرياً
        private string _backupFolder = "";
        private int _backupRetentionDays = 30;
        private bool _deleteOldBackups = true;
        private bool _compressBackups = true;
        private int _compressionLevel = 1; // 0=منخفض، 1=متوسط، 2=عالي

        // إعدادات العرض
        private double _windowOpacity = 1.0;
        private bool _enableAnimations = true;
        private bool _showTooltips = true;
        private string _dateFormat = "yyyy/MM/dd";

        // إعدادات الشبكة
        private bool _autoStartServer = true;
        private bool _autoConnectToServer = true;
        private string _serverIP = "";
        private int _serverPort = 8080;
        private bool _showNetworkNotifications = true;

        // إعدادات OneDrive
        private OneDriveSettings _oneDriveSettings = new OneDriveSettings();
        private string _currencySymbol = "د.ع";

        // إعدادات الأمان
        private bool _requirePasswordOnStartup = false;
        private bool _lockAfterInactivity = false;
        private int _inactivityTimeout = 30; // بالدقائق

        #region Properties

        public static AppSettings Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = LoadSettings();
                }
                return _instance;
            }
        }

        // إعدادات عامة
        public string Theme
        {
            get => _theme;
            set { _theme = value; OnPropertyChanged(); }
        }

        public string Language
        {
            get => _language;
            set { _language = value; OnPropertyChanged(); }
        }

        public bool AutoSave
        {
            get => _autoSave;
            set { _autoSave = value; OnPropertyChanged(); }
        }

        public int AutoSaveInterval
        {
            get => _autoSaveInterval;
            set { _autoSaveInterval = value; OnPropertyChanged(); }
        }

        public bool ShowNotifications
        {
            get => _showNotifications;
            set { _showNotifications = value; OnPropertyChanged(); }
        }

        public bool PlayNotificationSounds
        {
            get => _playNotificationSounds;
            set { _playNotificationSounds = value; OnPropertyChanged(); }
        }

        // إعدادات التذكيرات
        public bool EnableReminders
        {
            get => _enableReminders;
            set { _enableReminders = value; OnPropertyChanged(); }
        }

        public int ReminderDays
        {
            get => _reminderDays;
            set { _reminderDays = value; OnPropertyChanged(); }
        }

        public bool ShowOverdueReminders
        {
            get => _showOverdueReminders;
            set { _showOverdueReminders = value; OnPropertyChanged(); }
        }

        public string ReminderTime
        {
            get => _reminderTime;
            set { _reminderTime = value; OnPropertyChanged(); }
        }

        // إعدادات النسخ الاحتياطي
        public bool AutoBackup
        {
            get => _autoBackup;
            set { _autoBackup = value; OnPropertyChanged(); }
        }

        public int BackupInterval
        {
            get => _backupInterval;
            set { _backupInterval = value; OnPropertyChanged(); }
        }

        public string BackupLocation
        {
            get => _backupLocation;
            set { _backupLocation = value; OnPropertyChanged(); }
        }

        public int MaxBackupFiles
        {
            get => _maxBackupFiles;
            set { _maxBackupFiles = value; OnPropertyChanged(); }
        }

        public bool AutoBackupEnabled
        {
            get => _autoBackupEnabled;
            set { _autoBackupEnabled = value; OnPropertyChanged(); }
        }

        public int BackupFrequency
        {
            get => _backupFrequency;
            set { _backupFrequency = value; OnPropertyChanged(); }
        }

        public string BackupFolder
        {
            get => _backupFolder;
            set { _backupFolder = value; OnPropertyChanged(); }
        }

        public int BackupRetentionDays
        {
            get => _backupRetentionDays;
            set { _backupRetentionDays = value; OnPropertyChanged(); }
        }

        public bool DeleteOldBackups
        {
            get => _deleteOldBackups;
            set { _deleteOldBackups = value; OnPropertyChanged(); }
        }

        public bool CompressBackups
        {
            get => _compressBackups;
            set { _compressBackups = value; OnPropertyChanged(); }
        }

        public int CompressionLevel
        {
            get => _compressionLevel;
            set { _compressionLevel = value; OnPropertyChanged(); }
        }

        // إعدادات العرض
        public double WindowOpacity
        {
            get => _windowOpacity;
            set { _windowOpacity = value; OnPropertyChanged(); }
        }

        public bool EnableAnimations
        {
            get => _enableAnimations;
            set { _enableAnimations = value; OnPropertyChanged(); }
        }

        public bool ShowTooltips
        {
            get => _showTooltips;
            set { _showTooltips = value; OnPropertyChanged(); }
        }

        public string DateFormat
        {
            get => _dateFormat;
            set { _dateFormat = value; OnPropertyChanged(); }
        }

        public string CurrencySymbol
        {
            get => _currencySymbol;
            set { _currencySymbol = value; OnPropertyChanged(); }
        }

        // إعدادات الأمان
        public bool RequirePasswordOnStartup
        {
            get => _requirePasswordOnStartup;
            set { _requirePasswordOnStartup = value; OnPropertyChanged(); }
        }

        public bool LockAfterInactivity
        {
            get => _lockAfterInactivity;
            set { _lockAfterInactivity = value; OnPropertyChanged(); }
        }

        public int InactivityTimeout
        {
            get => _inactivityTimeout;
            set { _inactivityTimeout = value; OnPropertyChanged(); }
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل الإعدادات من الملف
        /// </summary>
        private static AppSettings LoadSettings()
        {
            try
            {
                if (File.Exists(SettingsFile))
                {
                    var json = File.ReadAllText(SettingsFile);
                    var settings = JsonSerializer.Deserialize<AppSettings>(json);
                    return settings ?? new AppSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
            }

            return new AppSettings();
        }

        /// <summary>
        /// حفظ الإعدادات إلى الملف
        /// </summary>
        public void SaveSettings()
        {
            try
            {
                var directory = Path.GetDirectoryName(SettingsFile);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(this, options);
                File.WriteAllText(SettingsFile, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        public void ResetToDefaults()
        {
            Theme = "Light";
            Language = "العربية";
            AutoSave = true;
            AutoSaveInterval = 5;
            ShowNotifications = true;
            PlayNotificationSounds = true;
            EnableReminders = true;
            ReminderDays = 7;
            ShowOverdueReminders = true;
            ReminderTime = "09:00";
            AutoBackup = true;
            BackupInterval = 24;
            BackupLocation = "";
            MaxBackupFiles = 10;
            WindowOpacity = 1.0;
            EnableAnimations = true;
            ShowTooltips = true;
            DateFormat = "yyyy/MM/dd";
            CurrencySymbol = "د.ع";
            RequirePasswordOnStartup = false;
            LockAfterInactivity = false;
            InactivityTimeout = 30;
        }

        #endregion

        #region Network Settings Properties

        /// <summary>
        /// بدء الخادم تلقائياً عند فتح التطبيق
        /// </summary>
        public bool AutoStartServer
        {
            get => _autoStartServer;
            set
            {
                _autoStartServer = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// الاتصال بالخادم تلقائياً عند فتح التطبيق
        /// </summary>
        public bool AutoConnectToServer
        {
            get => _autoConnectToServer;
            set
            {
                _autoConnectToServer = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// عنوان IP للخادم
        /// </summary>
        public string ServerIP
        {
            get => _serverIP;
            set
            {
                _serverIP = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// منفذ الخادم
        /// </summary>
        public int ServerPort
        {
            get => _serverPort;
            set
            {
                _serverPort = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// إظهار إشعارات الشبكة
        /// </summary>
        public bool ShowNetworkNotifications
        {
            get => _showNetworkNotifications;
            set
            {
                _showNetworkNotifications = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// إعدادات OneDrive
        /// </summary>
        public OneDriveSettings OneDriveSettings
        {
            get => _oneDriveSettings;
            set
            {
                _oneDriveSettings = value ?? new OneDriveSettings();
                OnPropertyChanged();
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
