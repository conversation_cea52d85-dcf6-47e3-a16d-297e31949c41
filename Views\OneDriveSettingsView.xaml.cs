using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DebtManagementApp.Models;
using DebtManagementApp.Services;
using DebtManagementApp.Helpers;

namespace DebtManagementApp.Views
{
    /// <summary>
    /// صفحة إعدادات OneDrive
    /// </summary>
    public partial class OneDriveSettingsView : UserControl
    {
        private readonly OneDriveSettings _settings;
        private OneDriveSyncManager? _syncManager;
        private bool _isInitializing = true;

        public OneDriveSettingsView()
        {
            InitializeComponent();
            _settings = AppSettings.Instance.OneDriveSettings;
            InitializeSettings();
            InitializeSyncManager();
            _isInitializing = false;
        }

        /// <summary>
        /// تهيئة الإعدادات
        /// </summary>
        private void InitializeSettings()
        {
            try
            {
                EnableSyncToggle.IsChecked = _settings.IsEnabled;
                AutoSyncToggle.IsChecked = _settings.AutoSync;
                SyncIntervalTextBox.Text = _settings.SyncIntervalMinutes.ToString();
                FolderPathTextBox.Text = _settings.FolderPath;
                ShowNotificationsToggle.IsChecked = _settings.ShowSyncNotifications;
                AutoDownloadToggle.IsChecked = _settings.AutoDownloadUpdates;

                UpdateLastSyncInfo();
                UpdateConnectionStatus(false, null);

                // ربط الأحداث
                _settings.PropertyChanged += OnSettingsChanged;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تهيئة الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة مدير المزامنة
        /// </summary>
        private void InitializeSyncManager()
        {
            try
            {
                var databasePath = DatabaseHelper.DatabaseFile;
                _syncManager = new OneDriveSyncManager(_settings, databasePath);

                // ربط الأحداث
                _syncManager.StatusChanged += OnStatusChanged;
                _syncManager.SyncCompleted += OnSyncCompleted;
                _syncManager.ErrorOccurred += OnErrorOccurred;
                _syncManager.NewerVersionDetected += OnNewerVersionDetected;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تهيئة مدير المزامنة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث معلومات آخر مزامنة
        /// </summary>
        private void UpdateLastSyncInfo()
        {
            try
            {
                LastSyncTimeText.Text = _settings.LastSyncTimeText;
                LastSyncStatusText.Text = _settings.LastSyncStatus;

                // تلوين النص حسب الحالة
                if (_settings.LastSyncStatus.Contains("نجح") || _settings.LastSyncStatus.Contains("تم"))
                {
                    LastSyncStatusText.Foreground = new SolidColorBrush(Colors.Green);
                }
                else if (_settings.LastSyncStatus.Contains("فشل") || _settings.LastSyncStatus.Contains("خطأ"))
                {
                    LastSyncStatusText.Foreground = new SolidColorBrush(Colors.Red);
                }
                else
                {
                    LastSyncStatusText.Foreground = new SolidColorBrush(Colors.Gray);
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحديث معلومات المزامنة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث حالة الاتصال
        /// </summary>
        private void UpdateConnectionStatus(bool isConnected, string? userInfo)
        {
            try
            {
                if (isConnected)
                {
                    StatusIndicator.Background = new SolidColorBrush(Colors.Green);
                    ConnectionStatusText.Text = "متصل";
                    UserInfoText.Text = userInfo ?? "مستخدم OneDrive";
                    SignInButton.Content = "تسجيل الخروج";
                }
                else
                {
                    StatusIndicator.Background = new SolidColorBrush(Colors.Red);
                    ConnectionStatusText.Text = "غير متصل";
                    UserInfoText.Text = "يرجى تسجيل الدخول";
                    SignInButton.Content = "تسجيل الدخول";
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحديث حالة الاتصال: {ex.Message}");
            }
        }

        #region معالجات الأحداث

        private void EnableSyncToggle_Checked(object sender, RoutedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.IsEnabled = true;
                SaveSettings();
            }
        }

        private void EnableSyncToggle_Unchecked(object sender, RoutedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.IsEnabled = false;
                SaveSettings();
            }
        }

        private void AutoSyncToggle_Checked(object sender, RoutedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.AutoSync = true;
                SaveSettings();
            }
        }

        private void AutoSyncToggle_Unchecked(object sender, RoutedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.AutoSync = false;
                SaveSettings();
            }
        }

        private void SyncIntervalTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!_isInitializing && int.TryParse(SyncIntervalTextBox.Text, out int interval))
            {
                _settings.SyncIntervalMinutes = Math.Max(5, interval);
                SaveSettings();
            }
        }

        private void FolderPathTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.FolderPath = FolderPathTextBox.Text;
                SaveSettings();
            }
        }

        private void ShowNotificationsToggle_Checked(object sender, RoutedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.ShowSyncNotifications = true;
                SaveSettings();
            }
        }

        private void ShowNotificationsToggle_Unchecked(object sender, RoutedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.ShowSyncNotifications = false;
                SaveSettings();
            }
        }

        private void AutoDownloadToggle_Checked(object sender, RoutedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.AutoDownloadUpdates = true;
                SaveSettings();
            }
        }

        private void AutoDownloadToggle_Unchecked(object sender, RoutedEventArgs e)
        {
            if (!_isInitializing)
            {
                _settings.AutoDownloadUpdates = false;
                SaveSettings();
            }
        }

        private async void SignInButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_syncManager == null) return;

                SignInButton.IsEnabled = false;
                SignInButton.Content = "جاري المعالجة...";

                if (ConnectionStatusText.Text == "متصل")
                {
                    await _syncManager.StopAsync();
                    UpdateConnectionStatus(false, null);
                }
                else
                {
                    await _syncManager.StartAsync();
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                SignInButton.IsEnabled = true;
            }
        }

        private async void ManualSyncButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_syncManager == null) return;

                ManualSyncButton.IsEnabled = false;
                ManualSyncButton.Content = "جاري المزامنة...";

                await _syncManager.ManualSyncAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في المزامنة اليدوية: {ex.Message}");
            }
            finally
            {
                ManualSyncButton.IsEnabled = true;
                ManualSyncButton.Content = "مزامنة يدوية";
            }
        }

        private async void CheckUpdatesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_syncManager == null) return;

                CheckUpdatesButton.IsEnabled = false;
                CheckUpdatesButton.Content = "جاري الفحص...";

                await _syncManager.CheckForNewerVersionAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فحص التحديثات: {ex.Message}");
            }
            finally
            {
                CheckUpdatesButton.IsEnabled = true;
                CheckUpdatesButton.Content = "فحص التحديثات";
            }
        }

        private async void DownloadLatestButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_syncManager == null) return;

                var result = MessageBox.Show(
                    "هل أنت متأكد من تحميل النسخة الأحدث؟ سيتم استبدال قاعدة البيانات المحلية.",
                    "تأكيد التحميل",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    DownloadLatestButton.IsEnabled = false;
                    DownloadLatestButton.Content = "جاري التحميل...";

                    await _syncManager.DownloadLatestAsync();
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل النسخة الأحدث: {ex.Message}");
            }
            finally
            {
                DownloadLatestButton.IsEnabled = true;
                DownloadLatestButton.Content = "تحميل الأحدث";
            }
        }

        #endregion

        #region معالجات أحداث المزامنة

        private void OnStatusChanged(string status)
        {
            Dispatcher.Invoke(() =>
            {
                // يمكن إضافة شريط حالة هنا
            });
        }

        private void OnSyncCompleted(string message, bool success)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateLastSyncInfo();
                if (_settings.ShowSyncNotifications)
                {
                    ShowNotification(message, success);
                }
            });
        }

        private void OnErrorOccurred(string error)
        {
            Dispatcher.Invoke(() =>
            {
                ShowError(error);
            });
        }

        private void OnNewerVersionDetected(DateTime remoteModified, DateTime localModified)
        {
            Dispatcher.Invoke(() =>
            {
                if (_settings.AutoDownloadUpdates)
                {
                    // تحميل تلقائي
                    _ = _syncManager?.DownloadLatestAsync();
                }
                else
                {
                    // عرض نافذة التأكيد
                    var window = Window.GetWindow(this);
                    var (shouldDownload, cancelled) = OneDriveUpdateDialog.ShowDialog(
                        window, localModified, remoteModified);

                    if (shouldDownload && !cancelled)
                    {
                        _ = _syncManager?.DownloadLatestAsync();
                    }
                }
            });
        }

        private void OnSettingsChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateLastSyncInfo();
            });
        }

        #endregion

        #region دوال مساعدة

        private void SaveSettings()
        {
            try
            {
                AppSettings.Instance.SaveSettings();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }

        private void ShowError(string message)
        {
            // إضافة معلومات إضافية للمساعدة في حل المشكلة
            var fullMessage = message;

            if (message.Contains("فشل في تسجيل الدخول"))
            {
                fullMessage += "\n\nنصائح لحل المشكلة:\n" +
                              "• تأكد من اتصالك بالإنترنت\n" +
                              "• تأكد من صحة بيانات حساب Microsoft\n" +
                              "• جرب إعادة تشغيل التطبيق";
            }
            else if (message.Contains("معرف التطبيق"))
            {
                fullMessage += "\n\nيرجى التواصل مع مطور التطبيق لتحديث إعدادات Microsoft Graph";
            }

            MessageBox.Show(fullMessage, "خطأ OneDrive", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void ShowNotification(string message, bool success)
        {
            // يمكن استخدام NotificationHelper هنا
            MessageBox.Show(message, success ? "نجح" : "فشل", 
                MessageBoxButton.OK, success ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }

        #endregion
    }
}
