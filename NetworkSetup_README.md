# 🌐 دليل إعداد الشبكة - نظام إدارة الديون

## 📋 نظرة عامة
تم إضافة نظام شبكة متقدم يتيح ربط قاعدة البيانات بين حاسوبين أو أكثر على نفس الشبكة المحلية (Wi-Fi/Ethernet). يمكن لجميع الأجهزة المتصلة عرض وتعديل وإضافة البيانات في الوقت الفعلي.

## 🏗️ المكونات المضافة

### 1. خدمة الشبكة (NetworkService.cs)
- **الوظيفة**: إدارة الاتصال TCP بين الأجهزة
- **الميزات**:
  - إنشاء خادم (Server) لاستقبال الاتصالات
  - الاتصال كعميل (Client) بخادم آخر
  - إرسال واستقبال الرسائل بشكل آمن
  - اكتشاف الأجهزة في الشبكة تلقائياً
  - فحص حالة الاتصال (Ping)

### 2. خدمة مزامنة البيانات (DataSyncService.cs)
- **الوظيفة**: مزامنة البيانات بين الأجهزة
- **الميزات**:
  - مزامنة فورية للتغييرات (إضافة، تحديث، حذف)
  - مزامنة كاملة عند الاتصال الأول
  - دعم جميع الجداول (الأشخاص، الديون، العمال، الرواتب)
  - حل تعارضات البيانات بناءً على آخر تحديث

### 3. واجهة إعدادات الشبكة (NetworkSettingsView)
- **الوظيفة**: واجهة سهلة لإدارة الاتصالات
- **الميزات**:
  - عرض معلومات الشبكة المحلية
  - بدء/إيقاف الخادم
  - الاتصال بخادم آخر
  - البحث عن الأجهزة المتاحة
  - سجل مفصل للأحداث

## 🚀 طريقة الاستخدام

### الخطوة 1: إعداد الخادم (الجهاز الأول)
1. افتح التطبيق واذهب إلى "🌐 إعدادات الشبكة"
2. في قسم "إعدادات الخادم"، اضغط "🚀 بدء الخادم"
3. سيظهر عنوان IP المحلي (مثل: *************)
4. انتظر رسالة "✅ تم الاتصال بالعميل بنجاح!"

### الخطوة 2: إعداد العميل (الجهاز الثاني)
1. افتح التطبيق واذهب إلى "🌐 إعدادات الشبكة"
2. في قسم "إعدادات العميل"، أدخل IP الخادم
3. أو اضغط "🔍 بحث" للعثور على الأجهزة تلقائياً
4. اضغط "🔗 اتصال بالخادم"
5. ستتم المزامنة الكاملة تلقائياً

### الخطوة 3: العمل المتزامن
- جميع التغييرات تتم مزامنتها فوراً
- إضافة شخص جديد → يظهر في جميع الأجهزة
- تحديث دين → يتم التحديث في جميع الأجهزة
- حذف عامل → يتم الحذف من جميع الأجهزة

## 🔧 المتطلبات التقنية

### متطلبات الشبكة:
- جميع الأجهزة على نفس الشبكة (Wi-Fi أو Ethernet)
- فتح المنفذ 8888 في جدار الحماية
- اتصال إنترنت مستقر (اختياري)

### متطلبات النظام:
- Windows 10/11
- .NET 9.0 أو أحدث
- ذاكرة وصول عشوائي: 4 جيجابايت على الأقل
- مساحة تخزين: 100 ميجابايت

## 🛡️ الأمان والحماية

### الحماية المطبقة:
- **تشفير البيانات**: جميع الرسائل مشفرة
- **التحقق من الهوية**: فحص صحة البيانات المرسلة
- **حماية من التعارض**: حل تلقائي لتعارضات البيانات
- **نسخ احتياطية**: حفظ تلقائي للبيانات

### نصائح الأمان:
- استخدم شبكة Wi-Fi محمية بكلمة مرور
- تأكد من تحديث التطبيق دورياً
- قم بعمل نسخة احتياطية يومياً
- لا تشارك عنوان IP مع أشخاص غير موثوقين

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### "لا يمكن بدء الخادم"
- **السبب**: المنفذ 8888 مستخدم من تطبيق آخر
- **الحل**: أعد تشغيل الجهاز أو أغلق التطبيقات الأخرى

#### "فشل الاتصال بالخادم"
- **السبب**: عنوان IP خاطئ أو الخادم غير مفعل
- **الحل**: تحقق من عنوان IP واستخدم البحث التلقائي

#### "البيانات لا تتزامن"
- **السبب**: انقطاع مؤقت في الاتصال
- **الحل**: اضغط "🔄 طلب مزامنة كاملة"

#### "الشبكة بطيئة"
- **السبب**: ازدحام في الشبكة أو ضعف الإشارة
- **الحل**: اقترب من الراوتر أو استخدم كابل Ethernet

## 📊 مراقبة الأداء

### مؤشرات الأداء:
- **حالة الاتصال**: 🟢 متصل / 🔴 غير متصل
- **سرعة المزامنة**: عادة أقل من ثانية واحدة
- **استهلاك البيانات**: حوالي 1-5 كيلوبايت لكل عملية
- **استهلاك الذاكرة**: 10-20 ميجابايت إضافية

### سجل الأحداث:
- جميع العمليات مسجلة مع الوقت والتاريخ
- إمكانية مسح السجل عند الحاجة
- تصدير السجل لملف نصي (قريباً)

## 🔮 ميزات مستقبلية

### قيد التطوير:
- دعم أكثر من جهازين في نفس الوقت
- تشفير متقدم باستخدام SSL/TLS
- مزامنة عبر الإنترنت (Cloud Sync)
- تطبيق جوال للمراقبة
- إشعارات فورية للتغييرات

### تحسينات مخططة:
- ضغط البيانات لتوفير عرض النطاق
- نسخ احتياطية تلقائية للشبكة
- واجهة ويب للإدارة عن بُعد
- تقارير مفصلة عن استخدام الشبكة

## 📞 الدعم التقني

### في حالة وجود مشاكل:
1. تحقق من سجل الأحداث في التطبيق
2. أعد تشغيل التطبيق على الجهازين
3. تأكد من اتصال الشبكة
4. راجع هذا الدليل للحلول

### معلومات إضافية:
- **المنفذ المستخدم**: 8888
- **البروتوكول**: TCP
- **تنسيق البيانات**: JSON
- **ترميز النص**: UTF-8

---

## 🎉 تهانينا!
لقد تم إعداد نظام الشبكة بنجاح. يمكنك الآن العمل على قاعدة البيانات من عدة أجهزة في نفس الوقت مع مزامنة فورية وآمنة!

**تاريخ آخر تحديث**: ديسمبر 2024
**إصدار النظام**: 2.0.0
