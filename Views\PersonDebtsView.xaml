<UserControl x:Class="DebtManagementApp.Views.PersonDebtsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- المحتوى الرئيسي -->
        <Grid x:Name="MainContentGrid" Grid.RowSpan="4">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
        
        <!-- رأس الصفحة -->
        <Border Grid.Row="0" Background="White" CornerRadius="12"
                Padding="24" Margin="0,0,0,16"
                BorderBrush="#E5E7EB" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock x:Name="PersonNameTitle" Text="ديون الشخص"
                               FontSize="20" FontWeight="SemiBold"
                               Margin="0,0,0,8"/>
                    <TextBlock x:Name="PersonInfoText" Text="معلومات الشخص"
                               FontSize="14"
                               Foreground="Gray"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="➕ إضافة دين جديد"
                            Background="#3B82F6" Foreground="White"
                            BorderThickness="0" Padding="16,8"
                            FontWeight="SemiBold"
                            Margin="0,0,12,0"
                            Click="AddDebt_Click"/>
                    <Button Content="🔙 العودة"
                            Background="Transparent" Foreground="#3B82F6"
                            BorderBrush="#3B82F6" BorderThickness="1"
                            Padding="16,8"
                            Click="GoBack_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- إحصائيات الديون -->
        <Border Grid.Row="1" Background="White" CornerRadius="12"
                Padding="16" Margin="0,0,0,16"
                BorderBrush="#E5E7EB" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي الديون -->
                <Border Background="#EFF6FF" CornerRadius="8"
                        Padding="12" Margin="3,0"
                        BorderBrush="#E5E7EB" BorderThickness="1">
                    <StackPanel HorizontalAlignment="Center" Margin="8">
                        <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="إجمالي الديون" FontSize="11"
                                   HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock x:Name="TotalDebtsText" Text="0"
                                   FontSize="16" FontWeight="SemiBold"
                                   Foreground="#3B82F6"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- المبلغ الإجمالي -->
                <Border Background="#F0FDF4" CornerRadius="8"
                        Padding="12" Margin="3,0"
                        BorderBrush="#E5E7EB" BorderThickness="1">
                    <StackPanel HorizontalAlignment="Center" Margin="8">
                        <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="المبلغ الإجمالي" FontSize="11"
                                   HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock x:Name="TotalAmountText" Text="0 دينار"
                                   FontSize="14" FontWeight="SemiBold"
                                   Foreground="#10B981"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- المدفوع -->
                <Border Background="#DCFCE7" CornerRadius="8"
                        Padding="12" Margin="3,0"
                        BorderBrush="#E5E7EB" BorderThickness="1">
                    <StackPanel HorizontalAlignment="Center" Margin="8">
                        <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="مدفوع" FontSize="11"
                                   HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock x:Name="PaidAmountText" Text="0 دينار"
                                   FontSize="14" FontWeight="SemiBold"
                                   Foreground="#10B981"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- المتبقي -->
                <Border Background="#FEE2E2" CornerRadius="8"
                        Padding="12" Margin="3,0"
                        BorderBrush="#E5E7EB" BorderThickness="1">
                    <StackPanel HorizontalAlignment="Center" Margin="8">
                        <TextBlock Text="⏳" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="متبقي" FontSize="11"
                                   HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock x:Name="RemainingAmountText" Text="0 دينار"
                                   FontSize="14" FontWeight="SemiBold"
                                   Foreground="#EF4444"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
        
        <!-- قائمة الديون -->
        <Border Grid.Row="2" Style="{StaticResource ModernCard}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان القائمة -->
                <TextBlock Grid.Row="0" Text="📋 تفاصيل الديون"
                           Style="{StaticResource HeadingSmall}"
                           Margin="0,0,0,16"/>

                <!-- DataGrid للديون -->
                <DataGrid Grid.Row="1" x:Name="PersonDebtsDataGrid"
                          Style="{StaticResource ModernDataGrid}"
                          ItemsSource="{Binding PersonDebts}"
                          SelectedItem="{Binding SelectedDebt}"
                          MouseDoubleClick="EditDebt_Click"
                          RowHeight="60"
                          AlternatingRowBackground="#F8FAFC">

                    <!-- قائمة السياق (الزر الأيمن) -->
                    <DataGrid.ContextMenu>
                        <ContextMenu Background="White"
                                     BorderBrush="#E5E7EB"
                                     BorderThickness="1"
                                     Padding="4">
                            <MenuItem Header="✏️ تعديل الدين" Click="EditDebt_Click"
                                      Padding="12,8" FontSize="14" FontWeight="Medium"
                                      Foreground="#10B981"/>
                            <MenuItem Header="💳 تسديد الدين" Click="PayDebt_Click"
                                      Padding="12,8" FontSize="14" FontWeight="Medium"
                                      Foreground="#3B82F6"/>
                            <Separator Background="#E5E7EB" Margin="8,4" Height="1"/>
                            <MenuItem Header="🗑️ حذف الدين" Click="DeleteDebt_Click"
                                      Padding="12,8" FontSize="14" FontWeight="Medium"
                                      Foreground="#EF4444"/>
                        </ContextMenu>
                    </DataGrid.ContextMenu>
                    
                    <DataGrid.Columns>
                        <DataGridTemplateColumn Header="💰 المبلغ" Width="140"
                                                HeaderStyle="{StaticResource ModernDataGridColumnHeader}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="#F0F9FF" CornerRadius="6" Padding="8,4" Margin="2">
                                        <TextBlock Text="{Binding Amount, StringFormat='{}{0:N0} دينار'}"
                                                   FontWeight="SemiBold" Foreground="#0369A1"
                                                   HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="📅 تاريخ الدين"
                                            Binding="{Binding Date, StringFormat='yyyy/MM/dd'}"
                                            Width="120"
                                            HeaderStyle="{StaticResource ModernDataGridColumnHeader}"
                                            CellStyle="{StaticResource ModernDataGridCell}"/>

                        <DataGridTemplateColumn Header="⏰ تاريخ الاستحقاق" Width="140"
                                                HeaderStyle="{StaticResource ModernDataGridColumnHeader}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Padding="8,4" Margin="2" CornerRadius="6">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsOverdue}" Value="True">
                                                        <Setter Property="Background" Value="#FEF2F2"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsOverdue}" Value="False">
                                                        <Setter Property="Background" Value="#F0FDF4"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock HorizontalAlignment="Center" FontWeight="Medium">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsOverdue}" Value="True">
                                                            <Setter Property="Text" Value="{Binding DueDate, StringFormat='yyyy/MM/dd'}"/>
                                                            <Setter Property="Foreground" Value="#DC2626"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsOverdue}" Value="False">
                                                            <Setter Property="Text" Value="{Binding DueDate, StringFormat='yyyy/MM/dd'}"/>
                                                            <Setter Property="Foreground" Value="#16A34A"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="🔧 نوع العملية" Width="140"
                                                HeaderStyle="{StaticResource ModernDataGridColumnHeader}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="#F3F4F6" CornerRadius="8" Padding="10,6" Margin="2">
                                        <TextBlock Text="{Binding OperationType}"
                                                   FontWeight="Medium" Foreground="#374151"
                                                   HorizontalAlignment="Center"
                                                   TextTrimming="CharacterEllipsis"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="📝 الوصف"
                                            Binding="{Binding Description}"
                                            Width="200"
                                            HeaderStyle="{StaticResource ModernDataGridColumnHeader}"
                                            CellStyle="{StaticResource ModernDataGridCell}"/>

                        <DataGridTemplateColumn Header="📋 الملاحظات" Width="180"
                                                HeaderStyle="{StaticResource ModernDataGridColumnHeader}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Padding="8,4" Margin="2">
                                        <TextBlock Text="{Binding Notes}"
                                                   FontSize="13" Foreground="#6B7280"
                                                   TextWrapping="Wrap"
                                                   MaxHeight="40"
                                                   TextTrimming="CharacterEllipsis"
                                                   ToolTip="{Binding Notes}"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTemplateColumn Header="🎯 الحالة" Width="120"
                                                HeaderStyle="{StaticResource ModernDataGridColumnHeader}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="16" Padding="12,6" HorizontalAlignment="Center" Margin="4">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsSettled}" Value="True">
                                                        <Setter Property="Background" Value="#DCFCE7"/>
                                                        <Setter Property="BorderBrush" Value="#16A34A"/>
                                                        <Setter Property="BorderThickness" Value="1"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsSettled}" Value="False">
                                                        <Setter Property="Background" Value="#FEE2E2"/>
                                                        <Setter Property="BorderBrush" Value="#DC2626"/>
                                                        <Setter Property="BorderThickness" Value="1"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock HorizontalAlignment="Center" FontWeight="SemiBold" FontSize="12">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsSettled}" Value="True">
                                                            <Setter Property="Text" Value="✅ مدفوع"/>
                                                            <Setter Property="Foreground" Value="#16A34A"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsSettled}" Value="False">
                                                            <Setter Property="Text" Value="⏳ غير مدفوع"/>
                                                            <Setter Property="Foreground" Value="#DC2626"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
            </Border>

        </Grid>

        <!-- فقاعة إضافة دين جديد -->
        <Grid x:Name="AddDebtBubbleOverlay" Grid.RowSpan="4"
              Background="#CC000000"
              Visibility="Collapsed">

            <!-- الفقاعة -->
            <Border Style="{StaticResource ElevatedCard}"
                    Width="750"
                    MaxHeight="850"
                    Margin="15"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Background="{StaticResource BackgroundPrimary}"
                    BorderBrush="{StaticResource BorderLight}">
                <Border.RenderTransform>
                    <ScaleTransform x:Name="BubbleScaleTransform" ScaleX="0.8" ScaleY="0.8"/>
                </Border.RenderTransform>

                <Grid Margin="30">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- رأس الفقاعة -->
                    <Border Grid.Row="0" Background="{StaticResource PrimaryGradient}"
                            CornerRadius="16,16,0,0" Margin="-24,-24,-24,0" Padding="24,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <Border Background="{StaticResource TextInverse}"
                                        CornerRadius="16" Width="56" Height="56"
                                        Margin="0,0,20,0">
                                    <TextBlock Text="💰" FontSize="28"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="إضافة دين جديد"
                                               FontSize="22" FontWeight="Bold"
                                               Foreground="{StaticResource TextInverse}"/>
                                    <TextBlock x:Name="BubblePersonNameLabel" Text="للشخص: [اسم الشخص]"
                                               FontSize="16"
                                               Foreground="{StaticResource TextInverse}"
                                               Opacity="0.9" Margin="0,4,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <Button Grid.Column="1" Content="✕"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    FontSize="18"
                                    Foreground="{StaticResource TextInverse}"
                                    Width="36" Height="36"
                                    Click="CloseBubble_Click"
                                    Cursor="Hand"
                                    VerticalAlignment="Top">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="18"
                                                            Width="{TemplateBinding Width}"
                                                            Height="{TemplateBinding Height}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#40FFFFFF"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </Grid>
                    </Border>

                    <!-- محتوى الفقاعة -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>

                            <!-- معلومات الدين الأساسية -->
                            <Border Style="{StaticResource ModernCard}"
                                    Background="{StaticResource BackgroundSecondary}"
                                    Margin="0,0,0,20">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                                        <Border Background="{StaticResource PrimaryColor}"
                                                CornerRadius="10" Width="40" Height="40"
                                                Margin="0,0,16,0">
                                            <TextBlock Text="📋" FontSize="20"
                                                       Foreground="{StaticResource TextInverse}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>
                                        <TextBlock Text="معلومات الدين الأساسية"
                                                   FontSize="18" FontWeight="SemiBold"
                                                   Foreground="{StaticResource TextPrimary}"
                                                   VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="160"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- نوع العملية -->
                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع العملية:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Center" Margin="0,0,0,20"/>
                                        <ComboBox x:Name="BubbleOperationTypeComboBox" Grid.Row="0" Grid.Column="1"
                                                  IsEditable="True"
                                                  FontSize="15"
                                                  Height="45"
                                                  Padding="12,8"
                                                  Background="White"
                                                  BorderBrush="#D1D5DB"
                                                  BorderThickness="1"
                                                  Margin="0,0,0,20">
                                            <ComboBoxItem Content="قطع" IsSelected="True"/>
                                            <ComboBoxItem Content="لحام"/>
                                            <ComboBoxItem Content="ثني"/>
                                            <ComboBoxItem Content="تشكيل"/>
                                            <ComboBoxItem Content="تقطيع"/>
                                            <ComboBoxItem Content="تجميع"/>
                                            <ComboBoxItem Content="طلاء"/>
                                            <ComboBoxItem Content="تنظيف"/>
                                            <ComboBoxItem Content="أخرى"/>
                                        </ComboBox>

                                        <!-- المبلغ -->
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="المبلغ (د.ع):"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Center" Margin="0,0,0,20"/>
                                        <TextBox x:Name="BubbleAmountTextBox" Grid.Row="1" Grid.Column="1"
                                                 FontSize="15" Height="45"
                                                 Padding="12,8"
                                                 Background="White"
                                                 BorderBrush="#D1D5DB"
                                                 BorderThickness="1"
                                                 Margin="0,0,0,20" Text=""
                                                 TextChanged="BubbleAmountTextBox_TextChanged"/>

                                        <!-- تاريخ الاستحقاق -->
                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ الاستحقاق:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Center" Margin="0,0,0,20"/>
                                        <DatePicker x:Name="BubbleDueDatePicker" Grid.Row="2" Grid.Column="1"
                                                    FontSize="15" Height="45"
                                                    Background="White"
                                                    BorderBrush="#D1D5DB"
                                                    BorderThickness="1"
                                                    Margin="0,0,0,20"/>

                                        <!-- الوصف -->
                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="الوصف:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Top" Margin="0,10,0,0"/>
                                        <TextBox x:Name="BubbleDescriptionTextBox" Grid.Row="3" Grid.Column="1"
                                                 FontSize="15" Height="100" TextWrapping="Wrap"
                                                 AcceptsReturn="True"
                                                 Padding="12,8"
                                                 Background="White"
                                                 BorderBrush="#D1D5DB"
                                                 BorderThickness="1"
                                                 VerticalScrollBarVisibility="Auto"/>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- تفاصيل إضافية -->
                            <Border Style="{StaticResource ModernCard}"
                                    Background="{StaticResource BackgroundSecondary}">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                                        <Border Background="{StaticResource SecondaryColor}"
                                                CornerRadius="10" Width="40" Height="40"
                                                Margin="0,0,16,0">
                                            <TextBlock Text="📝" FontSize="20"
                                                       Foreground="{StaticResource TextInverse}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>
                                        <TextBlock Text="تفاصيل إضافية"
                                                   FontSize="18" FontWeight="SemiBold"
                                                   Foreground="{StaticResource TextPrimary}"
                                                   VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="160"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- الأولوية -->
                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="الأولوية:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Center" Margin="0,0,0,20"/>
                                        <ComboBox x:Name="BubblePriorityComboBox" Grid.Row="0" Grid.Column="1"
                                                  FontSize="15" Height="45"
                                                  Padding="12,8"
                                                  Background="White"
                                                  BorderBrush="#D1D5DB"
                                                  BorderThickness="1"
                                                  Margin="0,0,0,20" SelectedIndex="1">
                                            <ComboBoxItem Content="🔵 منخفضة"/>
                                            <ComboBoxItem Content="🟡 متوسطة"/>
                                            <ComboBoxItem Content="🟠 عالية"/>
                                            <ComboBoxItem Content="🔴 عاجلة"/>
                                        </ComboBox>

                                        <!-- حالة الدين -->
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="حالة الدين:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Center" Margin="0,0,0,20"/>
                                        <ComboBox x:Name="BubbleStatusComboBox" Grid.Row="1" Grid.Column="1"
                                                  FontSize="15" Height="45"
                                                  Padding="12,8"
                                                  Background="White"
                                                  BorderBrush="#D1D5DB"
                                                  BorderThickness="1"
                                                  Margin="0,0,0,20" SelectedIndex="0">
                                            <ComboBoxItem Content="⏳ غير مسدد"/>
                                            <ComboBoxItem Content="🔄 مسدد جزئياً"/>
                                            <ComboBoxItem Content="✅ مسدد"/>
                                        </ComboBox>

                                        <!-- ملاحظات -->
                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="ملاحظات:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Top" Margin="0,10,0,0"/>
                                        <TextBox x:Name="BubbleNotesTextBox" Grid.Row="2" Grid.Column="1"
                                                 FontSize="15" Height="80" TextWrapping="Wrap"
                                                 AcceptsReturn="True"
                                                 Padding="12,8"
                                                 Background="White"
                                                 BorderBrush="#D1D5DB"
                                                 BorderThickness="1"
                                                 VerticalScrollBarVisibility="Auto"/>
                                    </Grid>
                                </StackPanel>
                            </Border>

                        </StackPanel>
                    </ScrollViewer>

                    <!-- أزرار الإجراءات -->
                    <Border Grid.Row="2" Background="{StaticResource BackgroundTertiary}"
                            CornerRadius="0,0,16,16" Margin="-24,24,-24,-24" Padding="24,24">
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Center">
                            <Button x:Name="SaveDebtButton" Content="💾 حفظ الدين"
                                    Style="{StaticResource PrimaryButton}"
                                    FontSize="16" FontWeight="SemiBold"
                                    Padding="32,14"
                                    Margin="0,0,20,0"
                                    Click="SaveDebtBubble_Click"/>

                            <Button Content="❌ إلغاء"
                                    Style="{StaticResource OutlineButton}"
                                    FontSize="16" FontWeight="SemiBold"
                                    Padding="32,14"
                                    Click="CloseBubble_Click"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- فقاعة تعديل الدين -->
        <Grid x:Name="EditDebtBubbleOverlay" Grid.RowSpan="4"
              Background="#CC000000"
              Visibility="Collapsed">

            <!-- الفقاعة -->
            <Border Style="{StaticResource ElevatedCard}"
                    Width="750"
                    MaxHeight="850"
                    Margin="15"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Background="{StaticResource BackgroundPrimary}"
                    BorderBrush="{StaticResource BorderLight}">
                <Border.RenderTransform>
                    <ScaleTransform x:Name="EditBubbleScaleTransform" ScaleX="0.8" ScaleY="0.8"/>
                </Border.RenderTransform>

                <Grid Margin="30">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- رأس الفقاعة -->
                    <Border Grid.Row="0" Background="{StaticResource SecondaryColor}"
                            CornerRadius="16,16,0,0" Margin="-24,-24,-24,0" Padding="24,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <Border Background="{StaticResource TextInverse}"
                                        CornerRadius="16" Width="56" Height="56"
                                        Margin="0,0,20,0">
                                    <TextBlock Text="✏️" FontSize="28"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="تعديل الدين"
                                               FontSize="22" FontWeight="Bold"
                                               Foreground="{StaticResource TextInverse}"/>
                                    <TextBlock x:Name="EditBubblePersonNameLabel" Text="للشخص: [اسم الشخص]"
                                               FontSize="16"
                                               Foreground="{StaticResource TextInverse}"
                                               Opacity="0.9" Margin="0,4,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <Button Grid.Column="1" Content="✕"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    FontSize="18"
                                    Foreground="{StaticResource TextInverse}"
                                    Width="36" Height="36"
                                    Click="CloseEditBubble_Click"
                                    Cursor="Hand"
                                    VerticalAlignment="Top">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="18"
                                                            Width="{TemplateBinding Width}"
                                                            Height="{TemplateBinding Height}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#40FFFFFF"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </Grid>
                    </Border>

                    <!-- محتوى الفقاعة -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>

                            <!-- معلومات الدين الأساسية -->
                            <Border Style="{StaticResource ModernCard}"
                                    Background="{StaticResource BackgroundSecondary}"
                                    Margin="0,0,0,20">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                                        <Border Background="{StaticResource SecondaryColor}"
                                                CornerRadius="10" Width="40" Height="40"
                                                Margin="0,0,16,0">
                                            <TextBlock Text="📋" FontSize="20"
                                                       Foreground="{StaticResource TextInverse}"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>
                                        <TextBlock Text="معلومات الدين الأساسية"
                                                   FontSize="18" FontWeight="SemiBold"
                                                   Foreground="{StaticResource TextPrimary}"
                                                   VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="160"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- نوع العملية -->
                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع العملية:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Center" Margin="0,0,0,20"/>
                                        <ComboBox x:Name="EditOperationTypeComboBox" Grid.Row="0" Grid.Column="1"
                                                  IsEditable="True"
                                                  FontSize="15"
                                                  Height="45"
                                                  Padding="12,8"
                                                  Background="White"
                                                  BorderBrush="#D1D5DB"
                                                  BorderThickness="1"
                                                  Margin="0,0,0,20">
                                            <ComboBoxItem Content="قطع"/>
                                            <ComboBoxItem Content="لحام"/>
                                            <ComboBoxItem Content="ثني"/>
                                            <ComboBoxItem Content="تشكيل"/>
                                            <ComboBoxItem Content="تقطيع"/>
                                            <ComboBoxItem Content="تجميع"/>
                                            <ComboBoxItem Content="طلاء"/>
                                            <ComboBoxItem Content="تنظيف"/>
                                            <ComboBoxItem Content="أخرى"/>
                                        </ComboBox>

                                        <!-- المبلغ -->
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="المبلغ (د.ع):"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Center" Margin="0,0,0,20"/>
                                        <TextBox x:Name="EditAmountTextBox" Grid.Row="1" Grid.Column="1"
                                                 FontSize="15" Height="45"
                                                 Padding="12,8"
                                                 Background="White"
                                                 BorderBrush="#D1D5DB"
                                                 BorderThickness="1"
                                                 Margin="0,0,0,20"
                                                 TextChanged="EditAmountTextBox_TextChanged"/>

                                        <!-- تاريخ الاستحقاق -->
                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ الاستحقاق:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Center" Margin="0,0,0,20"/>
                                        <DatePicker x:Name="EditDueDatePicker" Grid.Row="2" Grid.Column="1"
                                                    FontSize="15" Height="45"
                                                    Background="White"
                                                    BorderBrush="#D1D5DB"
                                                    BorderThickness="1"
                                                    Margin="0,0,0,20"/>

                                        <!-- الوصف -->
                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="الوصف:"
                                                   FontSize="15" FontWeight="Medium"
                                                   Foreground="#2C3E50"
                                                   VerticalAlignment="Top" Margin="0,10,0,0"/>
                                        <TextBox x:Name="EditDescriptionTextBox" Grid.Row="3" Grid.Column="1"
                                                 FontSize="15" Height="100" TextWrapping="Wrap"
                                                 AcceptsReturn="True"
                                                 Padding="12,8"
                                                 Background="White"
                                                 BorderBrush="#D1D5DB"
                                                 BorderThickness="1"
                                                 VerticalScrollBarVisibility="Auto"/>
                                    </Grid>
                                </StackPanel>
                            </Border>

                        </StackPanel>
                    </ScrollViewer>

                    <!-- أزرار الإجراءات -->
                    <Border Grid.Row="2" Background="{StaticResource BackgroundTertiary}"
                            CornerRadius="0,0,16,16" Margin="-24,24,-24,-24" Padding="24,24">
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Center">
                            <Button x:Name="UpdateDebtButton" Content="💾 حفظ التعديلات"
                                    Style="{StaticResource PrimaryButton}"
                                    FontSize="16" FontWeight="SemiBold"
                                    Padding="32,14"
                                    Margin="0,0,20,0"
                                    Click="UpdateDebtBubble_Click"/>

                            <Button Content="❌ إلغاء"
                                    Style="{StaticResource OutlineButton}"
                                    FontSize="16" FontWeight="SemiBold"
                                    Padding="32,14"
                                    Click="CloseEditBubble_Click"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </Grid>

    </Grid>
</UserControl>
