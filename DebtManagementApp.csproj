﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.9.0" />
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.6" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="5.2.8" />
    <PackageReference Include="OxyPlot.Wpf" Version="2.2.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Services\DatabaseService.cs" />
    <Compile Remove="Services\ValidationService.cs" />
    <Compile Remove="Services\FileService.cs" />
    <Compile Remove="Services\LoggingService.cs" />
    <Compile Remove="Services\MessageService.cs" />
    <Compile Remove="Services\ResourceService.cs" />
  </ItemGroup>

</Project>
