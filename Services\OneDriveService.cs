using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Graph;
using Microsoft.Graph.Authentication;
using Microsoft.Identity.Client;
using DebtManagementApp.Models;
using System.Diagnostics;

namespace DebtManagementApp.Services
{
    /// <summary>
    /// خدمة مزامنة OneDrive
    /// </summary>
    public class OneDriveService
    {
        private readonly OneDriveSettings _settings;
        private IPublicClientApplication? _clientApp;
        private GraphServiceClient? _graphServiceClient;
        private bool _isAuthenticated = false;

        public event Action<string>? StatusChanged;
        public event Action<string, bool>? SyncCompleted;
        public event Action<string>? ErrorOccurred;

        public OneDriveService(OneDriveSettings settings)
        {
            _settings = settings;
            InitializeClient();
        }

        /// <summary>
        /// تهيئة عميل Microsoft Graph
        /// </summary>
        private void InitializeClient()
        {
            try
            {
                _clientApp = PublicClientApplicationBuilder
                    .Create(_settings.ClientId)
                    .WithAuthority("https://login.microsoftonline.com/common")
                    .WithRedirectUri("http://localhost")
                    .Build();

                // سيتم إنشاء GraphServiceClient عند تسجيل الدخول
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تهيئة OneDrive: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل الدخول إلى Microsoft
        /// </summary>
        public async Task<bool> SignInAsync()
        {
            try
            {
                if (_clientApp == null)
                {
                    ErrorOccurred?.Invoke("عميل Microsoft غير مهيأ");
                    return false;
                }

                StatusChanged?.Invoke("جاري تسجيل الدخول...");

                var accounts = await _clientApp.GetAccountsAsync();
                AuthenticationResult? result = null;

                try
                {
                    // محاولة تسجيل الدخول الصامت أولاً
                    if (accounts.Any())
                    {
                        result = await _clientApp.AcquireTokenSilent(_settings.Scopes, accounts.FirstOrDefault())
                            .ExecuteAsync();
                    }
                }
                catch (MsalUiRequiredException)
                {
                    // تسجيل الدخول التفاعلي مطلوب
                    result = await _clientApp.AcquireTokenInteractive(_settings.Scopes)
                        .ExecuteAsync();
                }

                if (result != null)
                {
                    // إنشاء GraphServiceClient مع التوكن
                    var authProvider = new BaseBearerTokenAuthenticationProvider(
                        new TokenProvider(result.AccessToken));
                    _graphServiceClient = new GraphServiceClient(authProvider);

                    _isAuthenticated = true;
                    StatusChanged?.Invoke($"تم تسجيل الدخول: {result.Account.Username}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تسجيل الدخول: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تسجيل الخروج
        /// </summary>
        public async Task SignOutAsync()
        {
            try
            {
                if (_clientApp != null)
                {
                    var accounts = await _clientApp.GetAccountsAsync();
                    foreach (var account in accounts)
                    {
                        await _clientApp.RemoveAsync(account);
                    }
                }

                _isAuthenticated = false;
                StatusChanged?.Invoke("تم تسجيل الخروج");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تسجيل الخروج: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من حالة تسجيل الدخول
        /// </summary>
        public bool IsAuthenticated => _isAuthenticated;

        /// <summary>
        /// رفع قاعدة البيانات إلى OneDrive
        /// </summary>
        public async Task<bool> UploadDatabaseAsync(string localFilePath)
        {
            try
            {
                if (!_isAuthenticated || _graphServiceClient == null)
                {
                    ErrorOccurred?.Invoke("يجب تسجيل الدخول أولاً");
                    return false;
                }

                if (!File.Exists(localFilePath))
                {
                    ErrorOccurred?.Invoke("ملف قاعدة البيانات غير موجود");
                    return false;
                }

                StatusChanged?.Invoke("جاري رفع قاعدة البيانات...");

                // إنشاء المجلد إذا لم يكن موجوداً
                await EnsureFolderExistsAsync();

                // رفع الملف
                var fileName = Path.GetFileName(localFilePath);
                var remotePath = $"/{_settings.FolderPath}/{fileName}";

                using var fileStream = File.OpenRead(localFilePath);

                var uploadedItem = await _graphServiceClient.Me.Drive.Root
                    .ItemWithPath(remotePath)
                    .Content
                    .PutAsync(fileStream);

                if (uploadedItem != null)
                {
                    _settings.LastSyncTime = DateTime.Now;
                    _settings.LastSyncStatus = "تم الرفع بنجاح";
                    
                    StatusChanged?.Invoke("تم رفع قاعدة البيانات بنجاح");
                    SyncCompleted?.Invoke("تم رفع قاعدة البيانات إلى OneDrive", true);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                var errorMsg = $"خطأ في رفع قاعدة البيانات: {ex.Message}";
                _settings.LastSyncStatus = "فشل الرفع";
                ErrorOccurred?.Invoke(errorMsg);
                SyncCompleted?.Invoke(errorMsg, false);
                return false;
            }
        }

        /// <summary>
        /// تحميل قاعدة البيانات من OneDrive
        /// </summary>
        public async Task<bool> DownloadDatabaseAsync(string localFilePath)
        {
            try
            {
                if (!_isAuthenticated || _graphServiceClient == null)
                {
                    ErrorOccurred?.Invoke("يجب تسجيل الدخول أولاً");
                    return false;
                }

                StatusChanged?.Invoke("جاري تحميل قاعدة البيانات...");

                var fileName = Path.GetFileName(localFilePath);
                var remotePath = $"/{_settings.FolderPath}/{fileName}";

                // الحصول على معلومات الملف
                var driveItem = await _graphServiceClient.Me.Drive.Root
                    .ItemWithPath(remotePath)
                    .GetAsync();

                if (driveItem == null)
                {
                    ErrorOccurred?.Invoke("ملف قاعدة البيانات غير موجود في OneDrive");
                    return false;
                }

                // تحميل محتوى الملف
                var contentStream = await _graphServiceClient.Me.Drive.Root
                    .ItemWithPath(remotePath)
                    .Content
                    .GetAsync();

                // حفظ الملف محلياً
                var directory = Path.GetDirectoryName(localFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var fileStream = File.Create(localFilePath);
                await contentStream.CopyToAsync(fileStream);

                _settings.LastSyncTime = DateTime.Now;
                _settings.LastSyncStatus = "تم التحميل بنجاح";

                StatusChanged?.Invoke("تم تحميل قاعدة البيانات بنجاح");
                SyncCompleted?.Invoke("تم تحميل قاعدة البيانات من OneDrive", true);
                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = $"خطأ في تحميل قاعدة البيانات: {ex.Message}";
                _settings.LastSyncStatus = "فشل التحميل";
                ErrorOccurred?.Invoke(errorMsg);
                SyncCompleted?.Invoke(errorMsg, false);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود نسخة أحدث في OneDrive
        /// </summary>
        public async Task<(bool hasNewer, DateTime remoteModified, DateTime localModified)> CheckForNewerVersionAsync(string localFilePath)
        {
            try
            {
                if (!_isAuthenticated || _graphServiceClient == null)
                {
                    return (false, DateTime.MinValue, DateTime.MinValue);
                }

                var fileName = Path.GetFileName(localFilePath);
                var remotePath = $"/{_settings.FolderPath}/{fileName}";

                // الحصول على معلومات الملف البعيد
                DriveItem? remoteItem = null;
                try
                {
                    remoteItem = await _graphServiceClient.Me.Drive.Root
                        .ItemWithPath(remotePath)
                        .GetAsync();
                }
                catch (Exception ex) when (ex.Message.Contains("itemNotFound") || ex.Message.Contains("NotFound"))
                {
                    // الملف غير موجود في OneDrive
                    return (false, DateTime.MinValue, DateTime.MinValue);
                }

                if (remoteItem?.LastModifiedDateTime == null)
                {
                    return (false, DateTime.MinValue, DateTime.MinValue);
                }

                var remoteModified = remoteItem.LastModifiedDateTime.Value.DateTime;
                var localModified = File.Exists(localFilePath) ? File.GetLastWriteTime(localFilePath) : DateTime.MinValue;

                var hasNewer = remoteModified > localModified;
                return (hasNewer, remoteModified, localModified);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في التحقق من النسخة: {ex.Message}");
                return (false, DateTime.MinValue, DateTime.MinValue);
            }
        }

        /// <summary>
        /// التأكد من وجود المجلد في OneDrive
        /// </summary>
        private async Task EnsureFolderExistsAsync()
        {
            try
            {
                if (_graphServiceClient == null) return;

                // محاولة الحصول على المجلد
                try
                {
                    await _graphServiceClient.Me.Drive.Root
                        .ItemWithPath(_settings.FolderPath)
                        .GetAsync();
                }
                catch (Exception ex) when (ex.Message.Contains("itemNotFound") || ex.Message.Contains("NotFound"))
                {
                    // إنشاء المجلد
                    var driveItem = new DriveItem
                    {
                        Name = _settings.FolderPath,
                        Folder = new Folder { }
                    };

                    await _graphServiceClient.Me.Drive.Root.Children
                        .PostAsync(driveItem);

                    StatusChanged?.Invoke($"تم إنشاء مجلد {_settings.FolderPath} في OneDrive");
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في إنشاء المجلد: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على معلومات المستخدم
        /// </summary>
        public async Task<string?> GetUserInfoAsync()
        {
            try
            {
                if (!_isAuthenticated || _graphServiceClient == null)
                    return null;

                var user = await _graphServiceClient.Me.GetAsync();
                return user?.DisplayName ?? user?.UserPrincipalName;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في الحصول على معلومات المستخدم: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على مساحة التخزين المتاحة
        /// </summary>
        public async Task<(long total, long used, long remaining)> GetStorageInfoAsync()
        {
            try
            {
                if (!_isAuthenticated || _graphServiceClient == null)
                    return (0, 0, 0);

                var drive = await _graphServiceClient.Me.Drive.GetAsync();
                var quota = drive?.Quota;

                if (quota != null)
                {
                    var total = quota.Total ?? 0;
                    var used = quota.Used ?? 0;
                    var remaining = quota.Remaining ?? 0;

                    return (total, used, remaining);
                }

                return (0, 0, 0);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في الحصول على معلومات التخزين: {ex.Message}");
                return (0, 0, 0);
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            _graphServiceClient?.Dispose();
        }
    }

    /// <summary>
    /// مزود التوكن للمصادقة
    /// </summary>
    public class TokenProvider
    {
        private readonly string _accessToken;

        public TokenProvider(string accessToken)
        {
            _accessToken = accessToken;
        }

        public string GetAccessToken()
        {
            return _accessToken;
        }
    }
}
