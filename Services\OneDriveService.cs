using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using Microsoft.Identity.Client;
using DebtManagementApp.Models;
using System.Diagnostics;

namespace DebtManagementApp.Services
{
    /// <summary>
    /// خدمة مزامنة OneDrive
    /// </summary>
    public class OneDriveService
    {
        private readonly OneDriveSettings _settings;
        private IPublicClientApplication? _clientApp;
        private readonly HttpClient _httpClient;
        private string? _accessToken;
        private bool _isAuthenticated = false;

        public event Action<string>? StatusChanged;
        public event Action<string, bool>? SyncCompleted;
        public event Action<string>? ErrorOccurred;

        public OneDriveService(OneDriveSettings settings)
        {
            _settings = settings;
            _httpClient = new HttpClient();
            InitializeClient();
        }

        /// <summary>
        /// تهيئة عميل Microsoft Graph
        /// </summary>
        private void InitializeClient()
        {
            try
            {
                _clientApp = PublicClientApplicationBuilder
                    .Create(_settings.ClientId)
                    .WithAuthority("https://login.microsoftonline.com/common")
                    .WithRedirectUri("http://localhost")
                    .Build();
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تهيئة OneDrive: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل الدخول إلى Microsoft
        /// </summary>
        public async Task<bool> SignInAsync()
        {
            try
            {
                if (_clientApp == null)
                {
                    ErrorOccurred?.Invoke("عميل Microsoft غير مهيأ. يرجى التحقق من إعدادات التطبيق.");
                    return false;
                }

                StatusChanged?.Invoke("جاري تسجيل الدخول...");

                var accounts = await _clientApp.GetAccountsAsync();
                AuthenticationResult? result = null;

                try
                {
                    // محاولة تسجيل الدخول الصامت أولاً
                    if (accounts.Any())
                    {
                        result = await _clientApp.AcquireTokenSilent(_settings.Scopes, accounts.FirstOrDefault())
                            .ExecuteAsync();
                    }
                }
                catch (MsalUiRequiredException)
                {
                    try
                    {
                        // تسجيل الدخول التفاعلي مطلوب
                        result = await _clientApp.AcquireTokenInteractive(_settings.Scopes)
                            .WithParentActivityOrWindow(System.Diagnostics.Process.GetCurrentProcess().MainWindowHandle)
                            .ExecuteAsync();
                    }
                    catch (MsalException msalEx)
                    {
                        ErrorOccurred?.Invoke($"خطأ في المصادقة: {GetFriendlyErrorMessage(msalEx)}");
                        return false;
                    }
                }
                catch (MsalException msalEx)
                {
                    ErrorOccurred?.Invoke($"خطأ في المصادقة: {GetFriendlyErrorMessage(msalEx)}");
                    return false;
                }

                if (result != null)
                {
                    _accessToken = result.AccessToken;
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);

                    _isAuthenticated = true;
                    StatusChanged?.Invoke($"تم تسجيل الدخول: {result.Account.Username}");
                    return true;
                }

                ErrorOccurred?.Invoke("فشل في الحصول على رمز الوصول");
                return false;
            }
            catch (HttpRequestException httpEx)
            {
                ErrorOccurred?.Invoke("فشل في الاتصال بخدمة Microsoft. يرجى التحقق من اتصالك بالإنترنت.");
                return false;
            }
            catch (TaskCanceledException)
            {
                ErrorOccurred?.Invoke("انتهت مهلة الاتصال. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.");
                return false;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تسجيل الدخول: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تسجيل الخروج
        /// </summary>
        public async Task SignOutAsync()
        {
            try
            {
                if (_clientApp != null)
                {
                    var accounts = await _clientApp.GetAccountsAsync();
                    foreach (var account in accounts)
                    {
                        await _clientApp.RemoveAsync(account);
                    }
                }

                _isAuthenticated = false;
                StatusChanged?.Invoke("تم تسجيل الخروج");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تسجيل الخروج: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من حالة تسجيل الدخول
        /// </summary>
        public bool IsAuthenticated => _isAuthenticated;

        /// <summary>
        /// رفع قاعدة البيانات إلى OneDrive
        /// </summary>
        public async Task<bool> UploadDatabaseAsync(string localFilePath)
        {
            try
            {
                if (!_isAuthenticated || string.IsNullOrEmpty(_accessToken))
                {
                    ErrorOccurred?.Invoke("يجب تسجيل الدخول أولاً");
                    return false;
                }

                if (!File.Exists(localFilePath))
                {
                    ErrorOccurred?.Invoke("ملف قاعدة البيانات غير موجود");
                    return false;
                }

                StatusChanged?.Invoke("جاري رفع قاعدة البيانات...");

                // إنشاء المجلد إذا لم يكن موجوداً
                await EnsureFolderExistsAsync();

                // رفع الملف
                var fileName = Path.GetFileName(localFilePath);
                var uploadUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}/{fileName}:/content";

                using var fileStream = File.OpenRead(localFilePath);
                using var content = new StreamContent(fileStream);

                var response = await _httpClient.PutAsync(uploadUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    _settings.LastSyncTime = DateTime.Now;
                    _settings.LastSyncStatus = "تم الرفع بنجاح";

                    StatusChanged?.Invoke("تم رفع قاعدة البيانات بنجاح");
                    SyncCompleted?.Invoke("تم رفع قاعدة البيانات إلى OneDrive", true);
                    return true;
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                ErrorOccurred?.Invoke($"فشل الرفع: {response.StatusCode} - {errorContent}");
                return false;
            }
            catch (Exception ex)
            {
                var errorMsg = $"خطأ في رفع قاعدة البيانات: {ex.Message}";
                _settings.LastSyncStatus = "فشل الرفع";
                ErrorOccurred?.Invoke(errorMsg);
                SyncCompleted?.Invoke(errorMsg, false);
                return false;
            }
        }

        /// <summary>
        /// تحميل قاعدة البيانات من OneDrive
        /// </summary>
        public async Task<bool> DownloadDatabaseAsync(string localFilePath)
        {
            try
            {
                if (!_isAuthenticated || string.IsNullOrEmpty(_accessToken))
                {
                    ErrorOccurred?.Invoke("يجب تسجيل الدخول أولاً");
                    return false;
                }

                StatusChanged?.Invoke("جاري تحميل قاعدة البيانات...");

                var fileName = Path.GetFileName(localFilePath);
                var downloadUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}/{fileName}:/content";

                var response = await _httpClient.GetAsync(downloadUrl);

                if (!response.IsSuccessStatusCode)
                {
                    if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        ErrorOccurred?.Invoke("ملف قاعدة البيانات غير موجود في OneDrive");
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        ErrorOccurred?.Invoke($"فشل التحميل: {response.StatusCode} - {errorContent}");
                    }
                    return false;
                }

                // حفظ الملف محلياً
                var directory = Path.GetDirectoryName(localFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var contentStream = await response.Content.ReadAsStreamAsync();
                using var fileStream = File.Create(localFilePath);
                await contentStream.CopyToAsync(fileStream);

                _settings.LastSyncTime = DateTime.Now;
                _settings.LastSyncStatus = "تم التحميل بنجاح";

                StatusChanged?.Invoke("تم تحميل قاعدة البيانات بنجاح");
                SyncCompleted?.Invoke("تم تحميل قاعدة البيانات من OneDrive", true);
                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = $"خطأ في تحميل قاعدة البيانات: {ex.Message}";
                _settings.LastSyncStatus = "فشل التحميل";
                ErrorOccurred?.Invoke(errorMsg);
                SyncCompleted?.Invoke(errorMsg, false);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود نسخة أحدث في OneDrive
        /// </summary>
        public async Task<(bool hasNewer, DateTime remoteModified, DateTime localModified)> CheckForNewerVersionAsync(string localFilePath)
        {
            try
            {
                if (!_isAuthenticated || string.IsNullOrEmpty(_accessToken))
                {
                    return (false, DateTime.MinValue, DateTime.MinValue);
                }

                var fileName = Path.GetFileName(localFilePath);
                var metadataUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}/{fileName}";

                var response = await _httpClient.GetAsync(metadataUrl);

                if (!response.IsSuccessStatusCode)
                {
                    // الملف غير موجود في OneDrive
                    return (false, DateTime.MinValue, DateTime.MinValue);
                }

                var jsonContent = await response.Content.ReadAsStringAsync();
                var fileInfo = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(jsonContent);

                if (fileInfo.TryGetProperty("lastModifiedDateTime", out var lastModifiedProp))
                {
                    if (DateTime.TryParse(lastModifiedProp.GetString(), out var remoteModified))
                    {
                        var localModified = File.Exists(localFilePath) ? File.GetLastWriteTime(localFilePath) : DateTime.MinValue;
                        var hasNewer = remoteModified > localModified;
                        return (hasNewer, remoteModified, localModified);
                    }
                }

                return (false, DateTime.MinValue, DateTime.MinValue);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في التحقق من النسخة: {ex.Message}");
                return (false, DateTime.MinValue, DateTime.MinValue);
            }
        }

        /// <summary>
        /// التأكد من وجود المجلد في OneDrive
        /// </summary>
        private async Task EnsureFolderExistsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_accessToken)) return;

                // محاولة الحصول على المجلد
                var folderUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}";
                var response = await _httpClient.GetAsync(folderUrl);

                if (!response.IsSuccessStatusCode)
                {
                    // إنشاء المجلد
                    var createFolderUrl = "https://graph.microsoft.com/v1.0/me/drive/root/children";
                    var folderData = new
                    {
                        name = _settings.FolderPath,
                        folder = new { }
                    };

                    var json = System.Text.Json.JsonSerializer.Serialize(folderData);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    var createResponse = await _httpClient.PostAsync(createFolderUrl, content);

                    if (createResponse.IsSuccessStatusCode)
                    {
                        StatusChanged?.Invoke($"تم إنشاء مجلد {_settings.FolderPath} في OneDrive");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في إنشاء المجلد: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على معلومات المستخدم
        /// </summary>
        public async Task<string?> GetUserInfoAsync()
        {
            try
            {
                if (!_isAuthenticated || string.IsNullOrEmpty(_accessToken))
                    return null;

                var userUrl = "https://graph.microsoft.com/v1.0/me";
                var response = await _httpClient.GetAsync(userUrl);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var userInfo = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(jsonContent);

                    if (userInfo.TryGetProperty("displayName", out var displayName))
                    {
                        return displayName.GetString();
                    }
                    else if (userInfo.TryGetProperty("userPrincipalName", out var userPrincipalName))
                    {
                        return userPrincipalName.GetString();
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في الحصول على معلومات المستخدم: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على مساحة التخزين المتاحة
        /// </summary>
        public async Task<(long total, long used, long remaining)> GetStorageInfoAsync()
        {
            try
            {
                if (!_isAuthenticated || string.IsNullOrEmpty(_accessToken))
                    return (0, 0, 0);

                var driveUrl = "https://graph.microsoft.com/v1.0/me/drive";
                var response = await _httpClient.GetAsync(driveUrl);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var driveInfo = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(jsonContent);

                    if (driveInfo.TryGetProperty("quota", out var quota))
                    {
                        var total = quota.TryGetProperty("total", out var totalProp) ? totalProp.GetInt64() : 0;
                        var used = quota.TryGetProperty("used", out var usedProp) ? usedProp.GetInt64() : 0;
                        var remaining = quota.TryGetProperty("remaining", out var remainingProp) ? remainingProp.GetInt64() : 0;

                        return (total, used, remaining);
                    }
                }

                return (0, 0, 0);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في الحصول على معلومات التخزين: {ex.Message}");
                return (0, 0, 0);
            }
        }

        /// <summary>
        /// ترجمة رسائل خطأ MSAL إلى العربية
        /// </summary>
        private string GetFriendlyErrorMessage(MsalException ex)
        {
            return ex.ErrorCode switch
            {
                "authentication_canceled" => "تم إلغاء عملية تسجيل الدخول من قبل المستخدم",
                "access_denied" => "تم رفض الوصول. يرجى التحقق من الأذونات",
                "invalid_client" => "معرف التطبيق غير صحيح. يرجى التحقق من إعدادات التطبيق",
                "invalid_grant" => "انتهت صلاحية رمز المصادقة. يرجى المحاولة مرة أخرى",
                "invalid_scope" => "الأذونات المطلوبة غير صحيحة",
                "temporarily_unavailable" => "الخدمة غير متاحة مؤقتاً. يرجى المحاولة لاحقاً",
                "service_not_available" => "خدمة Microsoft غير متاحة حالياً",
                "request_timeout" => "انتهت مهلة الطلب. يرجى التحقق من الاتصال بالإنترنت",
                "user_mismatch" => "المستخدم المحدد لا يطابق المستخدم المسجل",
                "consent_required" => "يتطلب موافقة المستخدم على الأذونات",
                _ => $"خطأ غير معروف: {ex.Message}"
            };
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
