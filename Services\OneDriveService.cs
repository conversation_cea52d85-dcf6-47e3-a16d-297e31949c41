using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using Microsoft.Identity.Client;
using DebtManagementApp.Models;
using System.Diagnostics;
using System.IO.Compression;

namespace DebtManagementApp.Services
{
    /// <summary>
    /// خدمة مزامنة OneDrive
    /// </summary>
    public class OneDriveService
    {
        private readonly OneDriveSettings _settings;
        private IPublicClientApplication? _clientApp;
        private readonly HttpClient _httpClient;
        private string? _accessToken;
        private bool _isAuthenticated = false;
        private string? _currentUserInfo = null;
        private bool _isLocalSync = false;


        public event Action<string>? StatusChanged;
        public event Action<string, bool>? SyncCompleted;
        public event Action<string>? ErrorOccurred;
        public event Action<int>? ProgressChanged;

        public OneDriveService(OneDriveSettings settings)
        {
            _settings = settings;
            _httpClient = new HttpClient();
            InitializeClient();

            // استعادة حالة تسجيل الدخول المحفوظة
            RestoreSavedSignInState();
        }

        /// <summary>
        /// تهيئة عميل Microsoft Graph
        /// </summary>
        private void InitializeClient()
        {
            try
            {
                _clientApp = PublicClientApplicationBuilder
                    .Create(_settings.ClientId)
                    .WithAuthority("https://login.microsoftonline.com/common")
                    .WithRedirectUri("http://localhost")
                    .Build();
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تهيئة OneDrive: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل الدخول إلى Microsoft
        /// </summary>
        public async Task<bool> SignInAsync()
        {
            try
            {
                // التحقق من وجود مجلد OneDrive المحلي كبديل
                var localOneDrivePath = GetLocalOneDrivePath();
                if (!string.IsNullOrEmpty(localOneDrivePath))
                {
                    StatusChanged?.Invoke("تم العثور على مجلد OneDrive المحلي");
                    _isAuthenticated = true;
                    StatusChanged?.Invoke("تم تفعيل المزامنة المحلية مع OneDrive");
                    return true;
                }

                if (_clientApp == null)
                {
                    ErrorOccurred?.Invoke("عميل Microsoft غير مهيأ. سيتم استخدام المزامنة المحلية إذا كان OneDrive مثبتاً.");
                    return await TryLocalSync();
                }

                StatusChanged?.Invoke("جاري تسجيل الدخول...");

                var accounts = await _clientApp.GetAccountsAsync();
                AuthenticationResult? result = null;

                try
                {
                    // محاولة تسجيل الدخول الصامت أولاً
                    if (accounts.Any())
                    {
                        result = await _clientApp.AcquireTokenSilent(_settings.Scopes, accounts.FirstOrDefault())
                            .ExecuteAsync();
                    }
                }
                catch (MsalUiRequiredException)
                {
                    try
                    {
                        // تسجيل الدخول التفاعلي مطلوب
                        result = await _clientApp.AcquireTokenInteractive(_settings.Scopes)
                            .WithParentActivityOrWindow(System.Diagnostics.Process.GetCurrentProcess().MainWindowHandle)
                            .ExecuteAsync();
                    }
                    catch (MsalException msalEx)
                    {
                        ErrorOccurred?.Invoke($"خطأ في المصادقة: {GetFriendlyErrorMessage(msalEx)}");
                        // محاولة استخدام المزامنة المحلية كبديل
                        return await TryLocalSync();
                    }
                }
                catch (MsalException msalEx)
                {
                    ErrorOccurred?.Invoke($"خطأ في المصادقة: {GetFriendlyErrorMessage(msalEx)}");
                    // محاولة استخدام المزامنة المحلية كبديل
                    return await TryLocalSync();
                }

                if (result != null)
                {
                    _accessToken = result.AccessToken;
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);

                    _isAuthenticated = true;
                    _isLocalSync = false;
                    _currentUserInfo = result.Account.Username;

                    // حفظ حالة تسجيل الدخول
                    SaveSignInState("GraphAPI", result.Account.Username);

                    StatusChanged?.Invoke($"تم تسجيل الدخول: {result.Account.Username}");
                    return true;
                }

                ErrorOccurred?.Invoke("فشل في الحصول على رمز الوصول");
                // محاولة استخدام المزامنة المحلية كبديل
                return await TryLocalSync();
            }
            catch (HttpRequestException httpEx)
            {
                ErrorOccurred?.Invoke("فشل في الاتصال بخدمة Microsoft. يرجى التحقق من اتصالك بالإنترنت.");
                return false;
            }
            catch (TaskCanceledException)
            {
                ErrorOccurred?.Invoke("انتهت مهلة الاتصال. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.");
                return false;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تسجيل الدخول: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تسجيل الخروج
        /// </summary>
        public async Task SignOutAsync()
        {
            try
            {
                if (_clientApp != null)
                {
                    var accounts = await _clientApp.GetAccountsAsync();
                    foreach (var account in accounts)
                    {
                        await _clientApp.RemoveAsync(account);
                    }
                }

                _isAuthenticated = false;
                StatusChanged?.Invoke("تم تسجيل الخروج");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في تسجيل الخروج: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من حالة تسجيل الدخول
        /// </summary>
        public bool IsAuthenticated => _isAuthenticated;

        /// <summary>
        /// رفع قاعدة البيانات إلى OneDrive
        /// </summary>
        public async Task<bool> UploadDatabaseAsync(string localFilePath)
        {
            try
            {
                if (!File.Exists(localFilePath))
                {
                    ErrorOccurred?.Invoke("ملف قاعدة البيانات غير موجود");
                    return false;
                }

                StatusChanged?.Invoke("جاري رفع قاعدة البيانات...");

                // إذا كان لدينا مصادقة Graph API، استخدمها
                if (_isAuthenticated && !string.IsNullOrEmpty(_accessToken))
                {
                    return await UploadViaGraphAPI(localFilePath);
                }
                // وإلا استخدم المزامنة المحلية
                else if (_isAuthenticated)
                {
                    return await UploadViaLocalSync(localFilePath);
                }
                else
                {
                    ErrorOccurred?.Invoke("يجب تسجيل الدخول أولاً");
                    return false;
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"خطأ في رفع قاعدة البيانات: {ex.Message}";
                _settings.LastSyncStatus = "فشل الرفع";
                ErrorOccurred?.Invoke(errorMsg);
                SyncCompleted?.Invoke(errorMsg, false);
                return false;
            }
        }

        /// <summary>
        /// رفع الملف عبر Graph API
        /// </summary>
        private async Task<bool> UploadViaGraphAPI(string localFilePath)
        {
            // إنشاء المجلد إذا لم يكن موجوداً
            await EnsureFolderExistsAsync();

            // إنشاء نسخة احتياطية (مضغوطة أو عادية حسب الإعدادات)
            var fileToUpload = _settings.EnableCompression ?
                await CreateCompressedBackupAsync(localFilePath) : localFilePath;

            try
            {
                var fileName = Path.GetFileName(fileToUpload);
                var uploadUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}/{fileName}:/content";

                var fileInfo = new FileInfo(fileToUpload);
                var fileSize = fileInfo.Length;

                StatusChanged?.Invoke($"جاري رفع الملف ({fileSize / 1024:N0} KB)...");
                ProgressChanged?.Invoke(0);

                using var fileStream = File.OpenRead(fileToUpload);
                using var content = new ProgressStreamContent(fileStream, (progress) =>
                {
                    ProgressChanged?.Invoke(progress);
                });

                var response = await _httpClient.PutAsync(uploadUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    _settings.LastSyncTime = DateTime.Now;
                    _settings.LastSyncStatus = "تم الرفع بنجاح";

                    StatusChanged?.Invoke("تم رفع قاعدة البيانات بنجاح");
                    SyncCompleted?.Invoke("تم رفع قاعدة البيانات إلى OneDrive", true);
                    ProgressChanged?.Invoke(100);

                    // تنظيف النسخ القديمة إذا كان نظام الإصدارات مفعلاً
                    if (_settings.EnableVersioning)
                    {
                        await CleanupOldBackupsAsync();
                    }

                    return true;
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                ErrorOccurred?.Invoke($"فشل الرفع: {response.StatusCode} - {errorContent}");
                return false;
            }
            finally
            {
                // حذف الملف المضغوط المؤقت إذا كان مختلفاً عن الملف الأصلي
                if (_settings.EnableCompression && File.Exists(fileToUpload) && fileToUpload != localFilePath)
                {
                    File.Delete(fileToUpload);
                }
            }
        }

        /// <summary>
        /// رفع الملف عبر المزامنة المحلية
        /// </summary>
        private async Task<bool> UploadViaLocalSync(string localFilePath)
        {
            try
            {
                var oneDrivePath = GetLocalOneDrivePath();
                if (string.IsNullOrEmpty(oneDrivePath))
                {
                    ErrorOccurred?.Invoke("لم يتم العثور على مجلد OneDrive المحلي");
                    return false;
                }

                var targetFolder = Path.Combine(oneDrivePath, _settings.FolderPath);
                if (!Directory.Exists(targetFolder))
                {
                    Directory.CreateDirectory(targetFolder);
                }

                var fileName = Path.GetFileName(localFilePath);
                var targetPath = Path.Combine(targetFolder, fileName);

                // نسخ الملف إلى مجلد OneDrive
                File.Copy(localFilePath, targetPath, true);

                _settings.LastSyncTime = DateTime.Now;
                _settings.LastSyncStatus = "تم الرفع بنجاح (مزامنة محلية)";

                StatusChanged?.Invoke("تم نسخ قاعدة البيانات إلى مجلد OneDrive المحلي");
                SyncCompleted?.Invoke("تم رفع قاعدة البيانات إلى OneDrive (مزامنة محلية)", true);
                return true;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في المزامنة المحلية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحميل قاعدة البيانات من OneDrive
        /// </summary>
        public async Task<bool> DownloadDatabaseAsync(string localFilePath)
        {
            try
            {
                if (!_isAuthenticated)
                {
                    ErrorOccurred?.Invoke("يجب تسجيل الدخول أولاً");
                    return false;
                }

                StatusChanged?.Invoke("جاري تحميل قاعدة البيانات...");

                // محاولة Graph API أولاً إذا كان متاحاً
                if (!string.IsNullOrEmpty(_accessToken))
                {
                    return await DownloadViaGraphAPI(localFilePath);
                }
                // وإلا محاولة المزامنة المحلية
                else
                {
                    return await DownloadViaLocalSync(localFilePath);
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"خطأ في تحميل قاعدة البيانات: {ex.Message}";
                ErrorOccurred?.Invoke(errorMsg);
                return false;
            }
        }

        /// <summary>
        /// تحميل الملف عبر Graph API
        /// </summary>
        private async Task<bool> DownloadViaGraphAPI(string localFilePath)
        {
            var fileName = Path.GetFileName(localFilePath);
            var downloadUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}/{fileName}:/content";

            var response = await _httpClient.GetAsync(downloadUrl);

            if (!response.IsSuccessStatusCode)
            {
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    ErrorOccurred?.Invoke("ملف قاعدة البيانات غير موجود في OneDrive");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    ErrorOccurred?.Invoke($"فشل التحميل: {response.StatusCode} - {errorContent}");
                }
                return false;
            }

            // حفظ الملف محلياً
            var directory = Path.GetDirectoryName(localFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var contentStream = await response.Content.ReadAsStreamAsync();
            using var fileStream = File.Create(localFilePath);
            await contentStream.CopyToAsync(fileStream);

            _settings.LastSyncTime = DateTime.Now;
            _settings.LastSyncStatus = "تم التحميل بنجاح";

            StatusChanged?.Invoke("تم تحميل قاعدة البيانات بنجاح");
            SyncCompleted?.Invoke("تم تحميل قاعدة البيانات من OneDrive", true);
            return true;
        }

        /// <summary>
        /// تحميل الملف عبر المزامنة المحلية
        /// </summary>
        private async Task<bool> DownloadViaLocalSync(string localFilePath)
        {
            try
            {
                var oneDrivePath = GetLocalOneDrivePath();
                if (string.IsNullOrEmpty(oneDrivePath))
                {
                    ErrorOccurred?.Invoke("لم يتم العثور على مجلد OneDrive المحلي");
                    return false;
                }

                var sourceFolder = Path.Combine(oneDrivePath, _settings.FolderPath);
                if (!Directory.Exists(sourceFolder))
                {
                    ErrorOccurred?.Invoke($"مجلد {_settings.FolderPath} غير موجود في OneDrive المحلي");
                    return false;
                }

                var fileName = Path.GetFileName(localFilePath);
                var sourcePath = Path.Combine(sourceFolder, fileName);

                if (!File.Exists(sourcePath))
                {
                    ErrorOccurred?.Invoke("ملف قاعدة البيانات غير موجود في مجلد OneDrive المحلي");
                    return false;
                }

                // التحقق من أن الملف المصدر أحدث
                var sourceInfo = new FileInfo(sourcePath);
                var localInfo = new FileInfo(localFilePath);

                if (localInfo.Exists && sourceInfo.LastWriteTime <= localInfo.LastWriteTime)
                {
                    StatusChanged?.Invoke("النسخة المحلية هي الأحدث بالفعل");
                    return true;
                }

                // نسخ الملف من مجلد OneDrive المحلي
                File.Copy(sourcePath, localFilePath, true);

                _settings.LastSyncTime = DateTime.Now;
                _settings.LastSyncStatus = "تم التحميل بنجاح (مزامنة محلية)";

                StatusChanged?.Invoke("تم تحميل قاعدة البيانات من مجلد OneDrive المحلي");
                SyncCompleted?.Invoke("تم تحميل قاعدة البيانات من OneDrive (مزامنة محلية)", true);
                return true;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في المزامنة المحلية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود نسخة أحدث في OneDrive
        /// </summary>
        public async Task<(bool hasNewer, DateTime remoteModified, DateTime localModified)> CheckForNewerVersionAsync(string localFilePath)
        {
            try
            {
                if (!_isAuthenticated || string.IsNullOrEmpty(_accessToken))
                {
                    return (false, DateTime.MinValue, DateTime.MinValue);
                }

                var fileName = Path.GetFileName(localFilePath);
                var metadataUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}/{fileName}";

                var response = await _httpClient.GetAsync(metadataUrl);

                if (!response.IsSuccessStatusCode)
                {
                    // الملف غير موجود في OneDrive
                    return (false, DateTime.MinValue, DateTime.MinValue);
                }

                var jsonContent = await response.Content.ReadAsStringAsync();
                var fileInfo = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(jsonContent);

                if (fileInfo.TryGetProperty("lastModifiedDateTime", out var lastModifiedProp))
                {
                    if (DateTime.TryParse(lastModifiedProp.GetString(), out var remoteModified))
                    {
                        var localModified = File.Exists(localFilePath) ? File.GetLastWriteTime(localFilePath) : DateTime.MinValue;
                        var hasNewer = remoteModified > localModified;
                        return (hasNewer, remoteModified, localModified);
                    }
                }

                return (false, DateTime.MinValue, DateTime.MinValue);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في التحقق من النسخة: {ex.Message}");
                return (false, DateTime.MinValue, DateTime.MinValue);
            }
        }

        /// <summary>
        /// التأكد من وجود المجلد في OneDrive
        /// </summary>
        private async Task EnsureFolderExistsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_accessToken)) return;

                // محاولة الحصول على المجلد
                var folderUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}";
                var response = await _httpClient.GetAsync(folderUrl);

                if (!response.IsSuccessStatusCode)
                {
                    // إنشاء المجلد
                    var createFolderUrl = "https://graph.microsoft.com/v1.0/me/drive/root/children";
                    var folderData = new
                    {
                        name = _settings.FolderPath,
                        folder = new { }
                    };

                    var json = System.Text.Json.JsonSerializer.Serialize(folderData);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    var createResponse = await _httpClient.PostAsync(createFolderUrl, content);

                    if (createResponse.IsSuccessStatusCode)
                    {
                        StatusChanged?.Invoke($"تم إنشاء مجلد {_settings.FolderPath} في OneDrive");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في إنشاء المجلد: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على معلومات المستخدم
        /// </summary>
        public async Task<string?> GetUserInfoAsync()
        {
            try
            {
                if (!_isAuthenticated)
                    return null;

                // إذا كانت لدينا معلومات محفوظة، أرجعها
                if (!string.IsNullOrEmpty(_currentUserInfo))
                    return _currentUserInfo;

                // إذا كانت مزامنة محلية، أرجع معلومات المستخدم المحلي
                if (_isLocalSync)
                {
                    return $"مزامنة محلية - {Environment.UserName}";
                }

                // إذا كان لدينا Graph API، احصل على معلومات المستخدم
                if (!string.IsNullOrEmpty(_accessToken))
                {
                    var userUrl = "https://graph.microsoft.com/v1.0/me";
                    var response = await _httpClient.GetAsync(userUrl);

                    if (response.IsSuccessStatusCode)
                    {
                        var jsonContent = await response.Content.ReadAsStringAsync();
                        var userInfo = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(jsonContent);

                        if (userInfo.TryGetProperty("displayName", out var displayName))
                        {
                            _currentUserInfo = displayName.GetString();
                            return _currentUserInfo;
                        }
                        else if (userInfo.TryGetProperty("userPrincipalName", out var userPrincipalName))
                        {
                            _currentUserInfo = userPrincipalName.GetString();
                            return _currentUserInfo;
                        }
                    }
                }

                return "مستخدم OneDrive";
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في الحصول على معلومات المستخدم: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على مساحة التخزين المتاحة
        /// </summary>
        public async Task<(long total, long used, long remaining)> GetStorageInfoAsync()
        {
            try
            {
                if (!_isAuthenticated)
                    return (0, 0, 0);

                // إذا كانت مزامنة محلية، احصل على معلومات القرص المحلي
                if (_isLocalSync)
                {
                    var oneDrivePath = GetLocalOneDrivePath();
                    if (!string.IsNullOrEmpty(oneDrivePath))
                    {
                        var driveInfo = new DriveInfo(Path.GetPathRoot(oneDrivePath) ?? "C:");
                        return (driveInfo.TotalSize, driveInfo.TotalSize - driveInfo.AvailableFreeSpace, driveInfo.AvailableFreeSpace);
                    }
                    return (0, 0, 0);
                }

                // إذا كان لدينا Graph API، احصل على معلومات OneDrive
                if (string.IsNullOrEmpty(_accessToken))
                    return (0, 0, 0);

                var driveUrl = "https://graph.microsoft.com/v1.0/me/drive";
                var response = await _httpClient.GetAsync(driveUrl);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var driveInfo = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(jsonContent);

                    if (driveInfo.TryGetProperty("quota", out var quota))
                    {
                        var total = quota.TryGetProperty("total", out var totalProp) ? totalProp.GetInt64() : 0;
                        var used = quota.TryGetProperty("used", out var usedProp) ? usedProp.GetInt64() : 0;
                        var remaining = quota.TryGetProperty("remaining", out var remainingProp) ? remainingProp.GetInt64() : 0;

                        return (total, used, remaining);
                    }
                }

                return (0, 0, 0);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في الحصول على معلومات التخزين: {ex.Message}");
                return (0, 0, 0);
            }
        }

        /// <summary>
        /// محاولة استخدام المزامنة المحلية
        /// </summary>
        private async Task<bool> TryLocalSync()
        {
            try
            {
                var localPath = GetLocalOneDrivePath();
                if (!string.IsNullOrEmpty(localPath))
                {
                    _isAuthenticated = true;
                    _isLocalSync = true;
                    _currentUserInfo = $"مزامنة محلية - {Environment.UserName}";

                    // حفظ حالة تسجيل الدخول
                    SaveSignInState("LocalSync", _currentUserInfo);

                    StatusChanged?.Invoke("تم تفعيل المزامنة المحلية مع OneDrive");
                    return true;
                }
                else
                {
                    ErrorOccurred?.Invoke("لم يتم العثور على مجلد OneDrive المحلي. يرجى تثبيت تطبيق OneDrive أو إعداد معرف تطبيق صحيح.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"خطأ في المزامنة المحلية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// البحث عن مجلد OneDrive المحلي
        /// </summary>
        private string GetLocalOneDrivePath()
        {
            try
            {
                // البحث في المسارات الشائعة لـ OneDrive
                var userProfile = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
                var possiblePaths = new[]
                {
                    Path.Combine(userProfile, "OneDrive"),
                    Path.Combine(userProfile, "OneDrive - Personal"),
                    Path.Combine(userProfile, "OneDrive - Microsoft"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "OneDrive")
                };

                foreach (var path in possiblePaths)
                {
                    if (Directory.Exists(path))
                    {
                        return path;
                    }
                }

                // البحث في Registry لمسار OneDrive
                try
                {
                    using (var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\OneDrive"))
                    {
                        if (key != null)
                        {
                            var userFolder = key.GetValue("UserFolder") as string;
                            if (!string.IsNullOrEmpty(userFolder) && Directory.Exists(userFolder))
                            {
                                return userFolder;
                            }
                        }
                    }
                }
                catch
                {
                    // تجاهل أخطاء Registry
                }

                return string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// ترجمة رسائل خطأ MSAL إلى العربية
        /// </summary>
        private string GetFriendlyErrorMessage(MsalException ex)
        {
            return ex.ErrorCode switch
            {
                "authentication_canceled" => "تم إلغاء عملية تسجيل الدخول من قبل المستخدم",
                "access_denied" => "تم رفض الوصول. يرجى التحقق من الأذونات",
                "invalid_client" => "معرف التطبيق غير صحيح. يرجى التحقق من إعدادات التطبيق",
                "invalid_grant" => "انتهت صلاحية رمز المصادقة. يرجى المحاولة مرة أخرى",
                "invalid_scope" => "الأذونات المطلوبة غير صحيحة",
                "temporarily_unavailable" => "الخدمة غير متاحة مؤقتاً. يرجى المحاولة لاحقاً",
                "service_not_available" => "خدمة Microsoft غير متاحة حالياً",
                "request_timeout" => "انتهت مهلة الطلب. يرجى التحقق من الاتصال بالإنترنت",
                "user_mismatch" => "المستخدم المحدد لا يطابق المستخدم المسجل",
                "consent_required" => "يتطلب موافقة المستخدم على الأذونات",
                _ => $"خطأ غير معروف: {ex.Message}"
            };
        }

        /// <summary>
        /// حفظ حالة تسجيل الدخول
        /// </summary>
        private void SaveSignInState(string syncType, string userInfo)
        {
            if (_settings.RememberSignIn)
            {
                _settings.SavedSyncType = syncType;
                _settings.SavedUserInfo = userInfo;
            }
        }

        /// <summary>
        /// استعادة حالة تسجيل الدخول المحفوظة
        /// </summary>
        private void RestoreSavedSignInState()
        {
            try
            {
                if (!_settings.RememberSignIn || string.IsNullOrEmpty(_settings.SavedSyncType))
                    return;

                if (_settings.SavedSyncType == "LocalSync")
                {
                    var localPath = GetLocalOneDrivePath();
                    if (!string.IsNullOrEmpty(localPath))
                    {
                        _isAuthenticated = true;
                        _isLocalSync = true;
                        _currentUserInfo = _settings.SavedUserInfo;
                        StatusChanged?.Invoke("تم استعادة المزامنة المحلية");
                    }
                }
                else if (_settings.SavedSyncType == "GraphAPI")
                {
                    // سيتم محاولة تسجيل الدخول الصامت عند الحاجة
                    StatusChanged?.Invoke("تم حفظ معلومات تسجيل الدخول");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استعادة حالة تسجيل الدخول: {ex.Message}");
            }
        }

        /// <summary>
        /// مسح حالة تسجيل الدخول المحفوظة
        /// </summary>
        public void ClearSavedSignInState()
        {
            _settings.SavedSyncType = "";
            _settings.SavedUserInfo = "";
            _isAuthenticated = false;
            _isLocalSync = false;
            _currentUserInfo = null;
            _accessToken = null;
        }

        /// <summary>
        /// إنشاء نسخة احتياطية مضغوطة
        /// </summary>
        private async Task<string> CreateCompressedBackupAsync(string originalFilePath)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var fileName = Path.GetFileNameWithoutExtension(originalFilePath);
            var tempPath = Path.GetTempPath();
            var compressedFileName = $"{fileName}_{timestamp}.zip";
            var compressedFilePath = Path.Combine(tempPath, compressedFileName);

            using (var archive = ZipFile.Open(compressedFilePath, ZipArchiveMode.Create))
            {
                archive.CreateEntryFromFile(originalFilePath, Path.GetFileName(originalFilePath));
            }

            return compressedFilePath;
        }

        /// <summary>
        /// تنظيف النسخ الاحتياطية القديمة
        /// </summary>
        private async Task CleanupOldBackupsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_accessToken)) return;

                var folderUrl = $"https://graph.microsoft.com/v1.0/me/drive/root:/{_settings.FolderPath}:/children";
                var response = await _httpClient.GetAsync(folderUrl);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var folderData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(jsonContent);

                    if (folderData.TryGetProperty("value", out var files))
                    {
                        var backupFiles = new List<(string id, string name, DateTime created)>();

                        foreach (var file in files.EnumerateArray())
                        {
                            if (file.TryGetProperty("name", out var nameElement) &&
                                file.TryGetProperty("id", out var idElement) &&
                                file.TryGetProperty("createdDateTime", out var createdElement))
                            {
                                var name = nameElement.GetString() ?? "";
                                if (name.EndsWith(".zip") && name.Contains("_"))
                                {
                                    if (DateTime.TryParse(createdElement.GetString(), out var created))
                                    {
                                        backupFiles.Add((idElement.GetString() ?? "", name, created));
                                    }
                                }
                            }
                        }

                        // ترتيب حسب التاريخ والاحتفاظ بأحدث نسخ حسب الإعدادات
                        var filesToDelete = backupFiles
                            .OrderByDescending(f => f.created)
                            .Skip(_settings.MaxBackupVersions)
                            .ToList();

                        foreach (var fileToDelete in filesToDelete)
                        {
                            var deleteUrl = $"https://graph.microsoft.com/v1.0/me/drive/items/{fileToDelete.id}";
                            await _httpClient.DeleteAsync(deleteUrl);
                        }

                        if (filesToDelete.Any())
                        {
                            StatusChanged?.Invoke($"تم حذف {filesToDelete.Count} نسخة احتياطية قديمة");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء التنظيف
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف النسخ القديمة: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// محتوى تدفق مع تتبع التقدم
    /// </summary>
    public class ProgressStreamContent : HttpContent
    {
        private readonly Stream _content;
        private readonly Action<int> _progressCallback;
        private readonly int _bufferSize;

        public ProgressStreamContent(Stream content, Action<int> progressCallback, int bufferSize = 8192)
        {
            _content = content ?? throw new ArgumentNullException(nameof(content));
            _progressCallback = progressCallback ?? throw new ArgumentNullException(nameof(progressCallback));
            _bufferSize = bufferSize;
        }

        protected override Task SerializeToStreamAsync(Stream stream, System.Net.TransportContext? context)
        {
            return CopyToAsync(stream);
        }

        private new async Task CopyToAsync(Stream destination)
        {
            var buffer = new byte[_bufferSize];
            var totalBytes = _content.Length;
            var totalBytesRead = 0L;

            int bytesRead;
            while ((bytesRead = await _content.ReadAsync(buffer, 0, buffer.Length)) > 0)
            {
                await destination.WriteAsync(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;

                var progress = (int)((totalBytesRead * 100) / totalBytes);
                _progressCallback(progress);
            }
        }

        protected override bool TryComputeLength(out long length)
        {
            length = _content.Length;
            return true;
        }
    }
}
