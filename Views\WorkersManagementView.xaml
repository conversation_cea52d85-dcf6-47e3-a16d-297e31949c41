<UserControl x:Class="DebtManagementApp.Views.WorkersManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Loaded="WorkersManagementView_Loaded">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات العلوي -->
        <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1"
                Padding="16" Margin="0,0,0,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- شريط البحث -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox x:Name="SearchTextBox"
                             Width="300" Margin="0,0,12,0"
                             Padding="8" FontSize="14"
                             TextChanged="SearchTextBox_TextChanged"
                             Tag="🔍 البحث في العمال..."/>

                    <ComboBox x:Name="StatusFilterComboBox"
                              Width="150" Margin="0,0,12,0"
                              Padding="8" FontSize="14"
                              SelectionChanged="StatusFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع العمال" IsSelected="True"/>
                        <ComboBoxItem Content="العمال النشطين"/>
                        <ComboBoxItem Content="العمال غير النشطين"/>
                    </ComboBox>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="👷 إضافة عامل جديد"
                            Margin="0,0,12,0" Padding="16,8"
                            Background="#007BFF" Foreground="White"
                            BorderThickness="0"
                            Click="AddWorker_Click"/>

                    <Button x:Name="PaySalariesButton" Content="💰 صرف الرواتب"
                            Margin="0,0,12,0" Padding="16,8"
                            Background="#28A745" Foreground="White"
                            BorderThickness="0"
                            Click="PaySalaries_Click"/>

                    <Button x:Name="ViewPaidSalariesButton" Content="📄 الرواتب المصروفة"
                            Margin="0,0,12,0" Padding="16,8"
                            Background="#17A2B8" Foreground="White"
                            BorderThickness="0"
                            Click="ViewPaidSalaries_Click"/>

                    <Button Content="🔄 تحديث"
                            Padding="12,8"
                            Background="#6C757D" Foreground="White"
                            BorderThickness="0"
                            Click="RefreshData_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <TabControl Grid.Row="1" x:Name="MainTabControl" FontSize="14"
                    Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1">
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Padding" Value="16,8"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                    <Setter Property="Background" Value="#F8F9FA"/>
                    <Setter Property="BorderBrush" Value="#E9ECEF"/>
                    <Setter Property="BorderThickness" Value="1,1,1,0"/>
                    <Setter Property="Margin" Value="0,0,2,0"/>
                    <Style.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="Foreground" Value="#007BFF"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </TabControl.Resources>

            <!-- تبويب إدارة العمال -->
            <TabItem Header="👷 إدارة العمال" x:Name="WorkersManagementTab">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

            <!-- جدول العمال -->
            <Border Grid.Column="0" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1"
                    Padding="16" Margin="0,0,12,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان الجدول -->
                    <TextBlock Grid.Row="0" Text="📋 قائمة العمال"
                               FontSize="18" FontWeight="SemiBold"
                               Margin="0,0,0,16"/>

                    <!-- الجدول -->
                    <DataGrid Grid.Row="1" x:Name="WorkersDataGrid"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              IsReadOnly="True"
                              SelectionMode="Single"
                              GridLinesVisibility="All"
                              HeadersVisibility="Column"
                              RowHeaderWidth="0"
                              FontSize="14"
                              FontFamily="Segoe UI"
                              Background="White"
                              BorderBrush="#E9ECEF"
                              BorderThickness="1"
                              AlternatingRowBackground="#F8F9FA"
                              RowBackground="White"
                              ColumnWidth="*"
                              HorizontalScrollBarVisibility="Auto"
                              VerticalScrollBarVisibility="Auto"
                              EnableColumnVirtualization="False"
                              EnableRowVirtualization="False"
                              SelectionChanged="WorkersDataGrid_SelectionChanged"
                              MouseDoubleClick="WorkersDataGrid_MouseDoubleClick">
                        <DataGrid.Resources>
                            <!-- ستايل الهيدر -->
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="#6C757D"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Height" Value="50"/>
                                <Setter Property="Padding" Value="12,8"/>
                                <Setter Property="BorderBrush" Value="#495057"/>
                                <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                <Setter Property="VerticalContentAlignment" Value="Center"/>
                            </Style>

                            <!-- ستايل الخلايا -->
                            <Style TargetType="DataGridCell">
                                <Setter Property="Padding" Value="12,8"/>
                                <Setter Property="BorderBrush" Value="#E9ECEF"/>
                                <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                <Setter Property="VerticalContentAlignment" Value="Center"/>
                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#E2E6EA"/>
                                        <Setter Property="Foreground" Value="#495057"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>

                            <!-- ستايل الصفوف -->
                            <Style TargetType="DataGridRow">
                                <Setter Property="MinHeight" Value="50"/>
                                <Setter Property="Height" Value="50"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F1F3F4"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#E2E6EA"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.Resources>
                        <DataGrid.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="✏️ تعديل العامل" Click="EditWorker_Click"/>
                                <MenuItem Header="👁️ عرض التفاصيل" Click="ViewWorkerDetails_Click"/>
                                <Separator/>
                                <MenuItem Header="✅ تفعيل العامل" Click="ActivateWorker_Click"/>
                                <MenuItem Header="❌ إلغاء تفعيل العامل" Click="DeactivateWorker_Click"/>
                                <Separator/>
                                <MenuItem Header="🗑️ حذف العامل" Click="DeleteWorker_Click"
                                          Foreground="#DC3545"/>
                            </ContextMenu>
                        </DataGrid.ContextMenu>
                        
                        <DataGrid.Columns>
                            <!-- عمود الاسم -->
                            <DataGridTextColumn Header="👤 الاسم" Binding="{Binding Name}" Width="200" MinWidth="150">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#2C3E50"/>
                                        <Setter Property="Padding" Value="12,8"/>
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                        <Setter Property="FontSize" Value="14"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- عمود المنصب -->
                            <DataGridTextColumn Header="💼 المنصب" Binding="{Binding JobTitle}" Width="180" MinWidth="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#495057"/>
                                        <Setter Property="Padding" Value="12,8"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="13"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- عمود الأجر اليومي -->
                            <DataGridTextColumn Header="💰 الأجر اليومي" Binding="{Binding DailyWageFormatted}" Width="160" MinWidth="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#28A745"/>
                                        <Setter Property="Padding" Value="12,8"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontSize" Value="13"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- عمود الهاتف -->
                            <DataGridTextColumn Header="📞 الهاتف" Binding="{Binding Phone}" Width="160" MinWidth="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#007BFF"/>
                                        <Setter Property="Padding" Value="12,8"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="13"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- عمود الحالة -->
                            <DataGridTemplateColumn Header="⚡ الحالة" Width="120" MinWidth="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="{Binding StatusColor}"
                                                CornerRadius="15" Padding="12,8"
                                                BorderThickness="2" BorderBrush="White"
                                                Margin="4">
                                            <TextBlock Text="{Binding StatusText}"
                                                       Foreground="White"
                                                       FontSize="12" FontWeight="Bold"
                                                       HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- عمود تاريخ التوظيف -->
                            <DataGridTextColumn Header="📅 تاريخ التوظيف" Binding="{Binding HireDateFormatted}" Width="160" MinWidth="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="Padding" Value="12,8"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="13"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- معلومات الإحصائيات -->
                    <Border Grid.Row="2" Background="#E9ECEF" BorderBrush="#DEE2E6" BorderThickness="1"
                            CornerRadius="8" Padding="16,8" Margin="0,16,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Border Background="#007BFF" CornerRadius="20" Padding="12,6" Margin="0,0,12,0">
                                <TextBlock x:Name="TotalWorkersText" Text="إجمالي العمال: 0"
                                           FontSize="14" FontWeight="Bold" Foreground="White"/>
                            </Border>
                            <Border Background="#28A745" CornerRadius="20" Padding="12,6" Margin="0,0,12,0">
                                <TextBlock x:Name="ActiveWorkersText" Text="العمال النشطين: 0"
                                           FontSize="14" FontWeight="Bold" Foreground="White"/>
                            </Border>
                            <Border Background="#DC3545" CornerRadius="20" Padding="12,6">
                                <TextBlock x:Name="InactiveWorkersText" Text="العمال غير النشطين: 0"
                                           FontSize="14" FontWeight="Bold" Foreground="White"/>
                            </Border>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- لوحة التفاصيل -->
            <Border Grid.Column="1" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1"
                    Padding="16">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              Style="{StaticResource ModernScrollViewer}"
                              PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                    <StackPanel x:Name="WorkerDetailsPanel">

                        <!-- عنوان التفاصيل -->
                        <TextBlock Text="📋 تفاصيل العامل"
                                   FontSize="18" FontWeight="SemiBold"
                                   Margin="0,0,0,16"/>

                        <!-- رسالة عدم الاختيار -->
                        <TextBlock x:Name="NoSelectionMessage"
                                   Text="اختر عاملاً من القائمة لعرض تفاصيله"
                                   FontSize="14"
                                   Foreground="#6C757D"
                                   HorizontalAlignment="Center"
                                   Margin="0,40,0,0"/>

                        <!-- تفاصيل العامل - فقاعة حديثة -->
                        <Border x:Name="WorkerDetailsContent" Visibility="Collapsed"
                                Background="White"
                                BorderBrush="#E3F2FD"
                                BorderThickness="2"
                                CornerRadius="16"
                                Margin="0,8,0,0"
                                Padding="0">
                            <Border.Effect>
                                <DropShadowEffect Color="#1976D2"
                                                  BlurRadius="15"
                                                  ShadowDepth="3"
                                                  Opacity="0.2"/>
                            </Border.Effect>

                            <!-- تأثير الحركة -->
                            <Border.RenderTransform>
                                <ScaleTransform x:Name="DetailsScaleTransform" ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>

                            <StackPanel>
                                <!-- رأس الفقاعة -->
                                <Border Background="{StaticResource PrimaryGradient}"
                                        CornerRadius="14,14,0,0"
                                        Padding="20,16">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="👤" FontSize="24" Margin="0,0,12,0"/>
                                        <StackPanel>
                                            <TextBlock Text="تفاصيل العامل"
                                                       FontSize="18" FontWeight="Bold"
                                                       Foreground="White"/>
                                            <TextBlock x:Name="WorkerNameTitle"
                                                       FontSize="14"
                                                       Foreground="#E3F2FD"
                                                       Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>

                                <!-- محتوى التفاصيل -->
                                <StackPanel Margin="20">
                                    <!-- المعلومات الأساسية -->
                                    <Border Background="#F8F9FA"
                                            BorderBrush="#E9ECEF"
                                            BorderThickness="1"
                                            CornerRadius="12"
                                            Padding="16"
                                            Margin="0,0,0,16">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0"/>
                                                <TextBlock Text="المعلومات الأساسية"
                                                           FontSize="14" FontWeight="SemiBold"
                                                           Foreground="#2C3E50"/>
                                            </StackPanel>

                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Row="0" Grid.Column="0" Text="الاسم:"
                                                           FontWeight="SemiBold" Margin="0,0,12,8"
                                                           Foreground="#495057"/>
                                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="WorkerNameText"
                                                           Margin="0,0,0,8" Foreground="#212529"/>

                                                <TextBlock Grid.Row="1" Grid.Column="0" Text="المنصب:"
                                                           FontWeight="SemiBold" Margin="0,0,12,8"
                                                           Foreground="#495057"/>
                                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="WorkerJobTitleText"
                                                           Margin="0,0,0,8" Foreground="#212529"/>

                                                <TextBlock Grid.Row="2" Grid.Column="0" Text="الهاتف:"
                                                           FontWeight="SemiBold" Margin="0,0,12,8"
                                                           Foreground="#495057"/>
                                                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="WorkerPhoneText"
                                                           Margin="0,0,0,8" Foreground="#212529"/>

                                                <TextBlock Grid.Row="3" Grid.Column="0" Text="الهوية:"
                                                           FontWeight="SemiBold" Margin="0,0,12,0"
                                                           Foreground="#495057"/>
                                                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="WorkerNationalIdText"
                                                           Foreground="#212529"/>
                                            </Grid>
                                        </StackPanel>
                                    </Border>

                                    <!-- معلومات الراتب -->
                                    <Border Background="#E8F5E8"
                                            BorderBrush="#C8E6C9"
                                            BorderThickness="1"
                                            CornerRadius="12"
                                            Padding="16"
                                            Margin="0,0,0,16">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                <TextBlock Text="💰" FontSize="16" Margin="0,0,8,0"/>
                                                <TextBlock Text="معلومات الراتب"
                                                           FontSize="14" FontWeight="SemiBold"
                                                           Foreground="#2E7D32"/>
                                            </StackPanel>

                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Row="0" Grid.Column="0" Text="الراتب اليومي:"
                                                           FontWeight="SemiBold" Margin="0,0,12,8"
                                                           Foreground="#388E3C"/>
                                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="WorkerDailyWageText"
                                                           Margin="0,0,0,8" Foreground="#1B5E20"/>

                                                <TextBlock Grid.Row="1" Grid.Column="0" Text="الراتب الشهري:"
                                                           FontWeight="SemiBold" Margin="0,0,12,0"
                                                           Foreground="#388E3C"/>
                                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="WorkerMonthlyWageText"
                                                           Foreground="#1B5E20"/>
                                            </Grid>
                                        </StackPanel>
                                    </Border>

                                    <!-- معلومات العمل -->
                                    <Border Background="#FFF3E0"
                                            BorderBrush="#FFE0B2"
                                            BorderThickness="1"
                                            CornerRadius="12"
                                            Padding="16"
                                            Margin="0,0,0,16">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                <TextBlock Text="🏢" FontSize="16" Margin="0,0,8,0"/>
                                                <TextBlock Text="معلومات العمل"
                                                           FontSize="14" FontWeight="SemiBold"
                                                           Foreground="#F57C00"/>
                                            </StackPanel>

                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Row="0" Grid.Column="0" Text="تاريخ التوظيف:"
                                                           FontWeight="SemiBold" Margin="0,0,12,8"
                                                           Foreground="#FF8F00"/>
                                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="WorkerHireDateText"
                                                           Margin="0,0,0,8" Foreground="#E65100"/>

                                                <TextBlock Grid.Row="1" Grid.Column="0" Text="مدة العمل:"
                                                           FontWeight="SemiBold" Margin="0,0,12,8"
                                                           Foreground="#FF8F00"/>
                                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="WorkerWorkDurationText"
                                                           Margin="0,0,0,8" Foreground="#E65100"/>

                                                <TextBlock Grid.Row="2" Grid.Column="0" Text="الخبرة:"
                                                           FontWeight="SemiBold" Margin="0,0,12,8"
                                                           Foreground="#FF8F00"/>
                                                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="WorkerExperienceText"
                                                           Margin="0,0,0,8" Foreground="#E65100"/>

                                                <TextBlock Grid.Row="3" Grid.Column="0" Text="آخر يوم عمل:"
                                                           FontWeight="SemiBold" Margin="0,0,12,0"
                                                           Foreground="#FF8F00"/>
                                                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="WorkerLastWorkDateText"
                                                           Foreground="#E65100"/>
                                            </Grid>
                                        </StackPanel>
                                    </Border>

                                    <!-- أزرار الإجراءات -->
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,8,0,0">
                                        <Button Content="✏️ تعديل"
                                                Style="{StaticResource PrimaryButton}"
                                                Margin="0,0,12,0"
                                                Padding="16,8"
                                                Click="EditSelectedWorker_Click"/>

                                        <Button Content="🗑️ حذف"
                                                Style="{StaticResource DangerButton}"
                                                Margin="0,0,12,0"
                                                Padding="16,8"
                                                Click="DeleteSelectedWorker_Click"/>

                                        <Button Content="❌ إغلاق"
                                                Style="{StaticResource OutlineButton}"
                                                Padding="16,8"
                                                Click="CloseWorkerDetails_Click"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- فقاعة إضافة/تعديل العامل -->
            <Border x:Name="WorkerEditBubble" Visibility="Collapsed"
                    Background="White"
                    BorderBrush="#E3F2FD"
                    BorderThickness="2"
                    CornerRadius="16"
                    Margin="16"
                    Padding="0"
                    Panel.ZIndex="1000">
                <Border.Effect>
                    <DropShadowEffect Color="#1976D2"
                                      BlurRadius="20"
                                      ShadowDepth="5"
                                      Opacity="0.3"/>
                </Border.Effect>

                <!-- تأثير الحركة -->
                <Border.RenderTransform>
                    <ScaleTransform x:Name="EditBubbleScaleTransform" ScaleX="1" ScaleY="1"/>
                </Border.RenderTransform>

                <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="600"
                              Style="{StaticResource ModernScrollViewer}"
                              PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                    <StackPanel>
                        <!-- رأس الفقاعة -->
                        <Border Background="{StaticResource PrimaryGradient}"
                                CornerRadius="14,14,0,0"
                                Padding="20,16">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock x:Name="EditBubbleIcon" Text="👤" FontSize="24" Margin="0,0,12,0"/>
                                <StackPanel>
                                    <TextBlock x:Name="EditBubbleTitle" Text="إضافة عامل جديد"
                                               FontSize="18" FontWeight="Bold"
                                               Foreground="White"/>
                                    <TextBlock Text="املأ البيانات المطلوبة"
                                               FontSize="14"
                                               Foreground="#E3F2FD"
                                               Margin="0,2,0,0"/>
                                </StackPanel>
                                <Button Content="❌"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Foreground="White"
                                        FontSize="16"
                                        Padding="8"
                                        Margin="0,0,0,0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Top"
                                        Click="CloseWorkerEditBubble_Click"/>
                            </StackPanel>
                        </Border>

                        <!-- محتوى النموذج -->
                        <StackPanel Margin="20">
                            <!-- المعلومات الأساسية -->
                            <Border Background="#F8F9FA"
                                    BorderBrush="#E9ECEF"
                                    BorderThickness="1"
                                    CornerRadius="12"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="المعلومات الأساسية"
                                                   FontSize="14" FontWeight="SemiBold"
                                                   Foreground="#2C3E50"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- الاسم -->
                                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,12">
                                            <TextBlock Text="الاسم الكامل *" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#495057" Margin="0,0,0,4"/>
                                            <TextBox x:Name="BubbleNameTextBox"
                                                     Padding="8" Height="35"
                                                     BorderBrush="#E9ECEF" BorderThickness="1"
                                                     Background="White"/>
                                        </StackPanel>

                                        <!-- المنصب -->
                                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="8,0,0,12">
                                            <TextBlock Text="المنصب *" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#495057" Margin="0,0,0,4"/>
                                            <ComboBox x:Name="BubbleJobTitleComboBox"
                                                      Padding="8" Height="35"
                                                      BorderBrush="#E9ECEF" BorderThickness="1"
                                                      Background="White" IsEditable="True">
                                                <ComboBoxItem Content="عامل حديد"/>
                                                <ComboBoxItem Content="عامل تقطيع"/>
                                                <ComboBoxItem Content="عامل لحام"/>
                                                <ComboBoxItem Content="عامل نقل"/>
                                                <ComboBoxItem Content="مشرف ورشة"/>
                                                <ComboBoxItem Content="محاسب"/>
                                                <ComboBoxItem Content="مهندس"/>
                                                <ComboBoxItem Content="فني"/>
                                            </ComboBox>
                                        </StackPanel>

                                        <!-- الهاتف -->
                                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="رقم الهاتف" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#495057" Margin="0,0,0,4"/>
                                            <TextBox x:Name="BubblePhoneTextBox"
                                                     Padding="8" Height="35"
                                                     BorderBrush="#E9ECEF" BorderThickness="1"
                                                     Background="White"/>
                                        </StackPanel>

                                        <!-- العنوان -->
                                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="8,0,0,0">
                                            <TextBlock Text="العنوان" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#495057" Margin="0,0,0,4"/>
                                            <TextBox x:Name="BubbleAddressTextBox"
                                                     Padding="8" Height="35"
                                                     BorderBrush="#E9ECEF" BorderThickness="1"
                                                     Background="White"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- معلومات الراتب -->
                            <Border Background="#E8F5E8"
                                    BorderBrush="#C8E6C9"
                                    BorderThickness="1"
                                    CornerRadius="12"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <TextBlock Text="💰" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="معلومات الراتب"
                                                   FontSize="14" FontWeight="SemiBold"
                                                   Foreground="#2E7D32"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- الأجر اليومي -->
                                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="الأجر اليومي (دينار)" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#388E3C" Margin="0,0,0,4"/>
                                            <TextBox x:Name="BubbleDailyWageTextBox"
                                                     Padding="8" Height="35"
                                                     BorderBrush="#C8E6C9" BorderThickness="1"
                                                     Background="White"
                                                     TextChanged="BubbleDailyWage_TextChanged"/>
                                        </StackPanel>

                                        <!-- الراتب الأسبوعي -->
                                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="8,0,0,0">
                                            <TextBlock Text="الراتب الأسبوعي (دينار)" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#388E3C" Margin="0,0,0,4"/>
                                            <TextBox x:Name="BubbleWeeklyWageTextBox"
                                                     Padding="8" Height="35"
                                                     BorderBrush="#C8E6C9" BorderThickness="1"
                                                     Background="White"
                                                     TextChanged="BubbleWeeklyWage_TextChanged"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- معلومات العمل -->
                            <Border Background="#FFF3E0"
                                    BorderBrush="#FFE0B2"
                                    BorderThickness="1"
                                    CornerRadius="12"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <TextBlock Text="🏢" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="معلومات العمل"
                                                   FontSize="14" FontWeight="SemiBold"
                                                   Foreground="#F57C00"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- تاريخ التوظيف -->
                                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,12">
                                            <TextBlock Text="تاريخ التوظيف" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#FF8F00" Margin="0,0,0,4"/>
                                            <DatePicker x:Name="BubbleHireDatePicker"
                                                        Height="35"
                                                        BorderBrush="#FFE0B2" BorderThickness="1"
                                                        Background="White"/>
                                        </StackPanel>

                                        <!-- سنوات الخبرة -->
                                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="8,0,0,12">
                                            <TextBlock Text="سنوات الخبرة" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#FF8F00" Margin="0,0,0,4"/>
                                            <TextBox x:Name="BubbleExperienceYearsTextBox"
                                                     Padding="8" Height="35"
                                                     BorderBrush="#FFE0B2" BorderThickness="1"
                                                     Background="White"/>
                                        </StackPanel>

                                        <!-- الحالة -->
                                        <StackPanel Grid.Row="1" Grid.ColumnSpan="2" Margin="0,0,0,0">
                                            <CheckBox x:Name="BubbleIsActiveCheckBox"
                                                      Content="العامل نشط"
                                                      FontSize="12" FontWeight="SemiBold"
                                                      Foreground="#FF8F00"
                                                      IsChecked="True"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- أزرار الإجراءات -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,8,0,0">
                                <Button x:Name="BubbleSaveButton" Content="💾 حفظ"
                                        Style="{StaticResource PrimaryButton}"
                                        Margin="0,0,12,0"
                                        Padding="20,10"
                                        Click="SaveWorkerFromBubble_Click"/>

                                <Button Content="❌ إلغاء"
                                        Style="{StaticResource OutlineButton}"
                                        Padding="20,10"
                                        Click="CloseWorkerEditBubble_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
                </Grid>
            </TabItem>

            <!-- تبويب صرف الرواتب -->
            <TabItem Header="💰 صرف الرواتب" x:Name="PaySalariesTab">
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان القسم -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                        <TextBlock Text="💰 صرف الرواتب الأسبوعية"
                                   FontSize="22" FontWeight="Bold" Margin="0,0,20,0"
                                   Foreground="#2C3E50" VerticalAlignment="Center"/>
                        <Button Content="📊 صرف رواتب جماعية"
                                Background="#28A745" Foreground="White"
                                BorderThickness="0" Padding="16,8"
                                FontWeight="Bold" FontSize="14"
                                Click="PayAllSalaries_Click"
                                ToolTip="صرف جميع الرواتب المعروضة في الجدول">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="#28A745"/>
                                    <Setter Property="Cursor" Value="Hand"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#218838"/>
                                            <Setter Property="RenderTransform">
                                                <Setter.Value>
                                                    <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                                </Setter.Value>
                                            </Setter>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>

                    <!-- جدول صرف الرواتب -->
                    <Border Grid.Row="1" BorderBrush="#E9ECEF" BorderThickness="2" CornerRadius="8" Margin="0,8,0,0">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto"
                                      Style="{StaticResource ModernScrollViewer}"
                                      PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                            <DataGrid x:Name="PaySalariesDataGrid"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="False"
                                  CanUserResizeColumns="True"
                                  CanUserSortColumns="False"
                                  GridLinesVisibility="All"
                                  HeadersVisibility="Column"
                                  RowHeaderWidth="0"
                                  FontSize="12"
                                  FontFamily="Segoe UI"
                                  Background="White"
                                  BorderBrush="#E9ECEF"
                                  BorderThickness="1"
                                  AlternatingRowBackground="#F8F9FA"
                                  RowBackground="White"
                                  SelectionMode="Single"
                                  SelectionUnit="FullRow">
                            <DataGrid.Resources>
                                <!-- ستايل الهيدر -->
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#007BFF"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="Padding" Value="8,6"/>
                                    <Setter Property="BorderBrush" Value="#0056B3"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                </Style>

                                <!-- ستايل الخلايا -->
                                <Style TargetType="DataGridCell">
                                    <Setter Property="Padding" Value="8,4"/>
                                    <Setter Property="BorderBrush" Value="#E9ECEF"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                            <Setter Property="Foreground" Value="#1976D2"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>

                                <!-- ستايل الصفوف -->
                                <Style TargetType="DataGridRow">
                                    <Setter Property="MinHeight" Value="35"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F0F8FF"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.Resources>
                            <DataGrid.Columns>
                                <!-- عمود الاسم -->
                                <DataGridTextColumn Header="👤 الاسم الكامل" Binding="{Binding Name}" Width="160" IsReadOnly="True">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                            <Setter Property="Padding" Value="8,4"/>
                                            <Setter Property="HorizontalAlignment" Value="Right"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- عمود الراتب اليومي -->
                                <DataGridTextColumn Header="💰 الراتب اليومي" Binding="{Binding DailyWageFormatted}" Width="120" IsReadOnly="True">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="Foreground" Value="#27AE60"/>
                                            <Setter Property="Padding" Value="8,4"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- عمود أيام العمل -->
                                <DataGridTemplateColumn Header="📅 أيام العمل" Width="90">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding WorkDays, UpdateSourceTrigger=PropertyChanged}"
                                                     TextAlignment="Center" BorderThickness="1"
                                                     BorderBrush="#BDC3C7" Background="#ECF0F1"
                                                     Padding="4" FontWeight="Bold"
                                                     Foreground="#2980B9"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- عمود أيام الغياب -->
                                <DataGridTemplateColumn Header="❌ أيام الغياب" Width="90">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding AbsenceDays, UpdateSourceTrigger=PropertyChanged}"
                                                     TextAlignment="Center" BorderThickness="1"
                                                     BorderBrush="#BDC3C7" Background="#FADBD8"
                                                     Padding="4" FontWeight="Bold"
                                                     Foreground="#E74C3C"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- عمود الراتب الأسبوعي -->
                                <DataGridTextColumn Header="💵 الراتب الأسبوعي" Binding="{Binding WeeklySalaryFormatted}" Width="130" IsReadOnly="True">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="Foreground" Value="#16A085"/>
                                            <Setter Property="Padding" Value="8,4"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="Background" Value="#E8F8F5"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- عمود الساعات الإضافية -->
                                <DataGridTemplateColumn Header="⏰ ساعات إضافية" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding OvertimeHours, UpdateSourceTrigger=PropertyChanged}"
                                                     TextAlignment="Center" BorderThickness="1"
                                                     BorderBrush="#BDC3C7" Background="#FEF9E7"
                                                     Padding="4" FontWeight="Bold"
                                                     Foreground="#F39C12"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- عمود سعر الساعة الإضافية -->
                                <DataGridTemplateColumn Header="💲 سعر الساعة" Width="110">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding OvertimeRate, UpdateSourceTrigger=PropertyChanged}"
                                                     TextAlignment="Center" BorderThickness="1"
                                                     BorderBrush="#BDC3C7" Background="#FEF9E7"
                                                     Padding="4" FontWeight="Bold"
                                                     Foreground="#F39C12"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- عمود الخصومات -->
                                <DataGridTemplateColumn Header="➖ الخصومات" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding Deductions, UpdateSourceTrigger=PropertyChanged}"
                                                     TextAlignment="Center" BorderThickness="1"
                                                     BorderBrush="#BDC3C7" Background="#FADBD8"
                                                     Padding="4" FontWeight="Bold"
                                                     Foreground="#E74C3C"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- عمود المكافآت -->
                                <DataGridTemplateColumn Header="🎁 المكافآت" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding Bonuses, UpdateSourceTrigger=PropertyChanged}"
                                                     TextAlignment="Center" BorderThickness="1"
                                                     BorderBrush="#BDC3C7" Background="#D5EDDA"
                                                     Padding="4" FontWeight="Bold"
                                                     Foreground="#28A745"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- عمود إجمالي الراتب -->
                                <DataGridTextColumn Header="💎 إجمالي الراتب" Binding="{Binding TotalAmountFormatted}" Width="140" IsReadOnly="True">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="14"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="Background" Value="#007BFF"/>
                                            <Setter Property="Padding" Value="8,6"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- عمود الإجراءات -->
                                <DataGridTemplateColumn Header="⚡ الإجراءات" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Content="💰 صرف الراتب"
                                                    Background="#28A745" Foreground="White"
                                                    BorderThickness="0" Padding="8,6"
                                                    FontWeight="Bold" FontSize="11"
                                                    Click="PayWorkerSalary_Click"
                                                    Tag="{Binding}"
                                                    ToolTip="اضغط لصرف راتب هذا العامل">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Background" Value="#28A745"/>
                                                        <Setter Property="Cursor" Value="Hand"/>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#218838"/>
                                                                <Setter Property="RenderTransform">
                                                                    <Setter.Value>
                                                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                                                    </Setter.Value>
                                                                </Setter>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                            </DataGrid>
                        </ScrollViewer>
                    </Border>
                </Grid>
            </TabItem>

            <!-- تبويب الرواتب المصروفة -->
            <TabItem Header="📄 الرواتب المصروفة" x:Name="PaidSalariesTab">
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان القسم -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                        <TextBlock Text="📄 سجل الرواتب المصروفة"
                                   FontSize="22" FontWeight="Bold" Margin="0,0,20,0"
                                   Foreground="#2C3E50" VerticalAlignment="Center"/>
                        <Button Content="📊 تصدير جميع الرواتب إلى Excel"
                                Background="#17A2B8" Foreground="White"
                                BorderThickness="0" Padding="16,8"
                                FontWeight="Bold" FontSize="14"
                                Click="ExportSalaries_Click"
                                ToolTip="تصدير جميع سجلات الرواتب إلى ملف Excel">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="#17A2B8"/>
                                    <Setter Property="Cursor" Value="Hand"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#138496"/>
                                            <Setter Property="RenderTransform">
                                                <Setter.Value>
                                                    <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                                </Setter.Value>
                                            </Setter>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>

                    <!-- جدول الرواتب المصروفة -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- قائمة العمال -->
                        <Border Grid.Column="0" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1"
                                Padding="16" Margin="0,0,12,0">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="👷 العمال الذين تم صرف رواتب لهم"
                                           FontSize="14" FontWeight="Bold" Margin="0,0,0,12"
                                           Foreground="#2C3E50"/>

                                <StackPanel Grid.Row="1">
                                    <ListBox x:Name="WorkersWithSalariesListBox"
                                             FontSize="14" Background="White"
                                             BorderBrush="#E9ECEF" BorderThickness="1"
                                             Height="300"
                                             SelectionChanged="WorkersWithSalariesListBox_SelectionChanged"
                                             MouseDoubleClick="WorkersWithSalariesListBox_MouseDoubleClick">
                                    <ListBox.ItemContainerStyle>
                                        <Style TargetType="ListBoxItem">
                                            <Setter Property="Padding" Value="12,8"/>
                                            <Setter Property="Margin" Value="2"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                            <Setter Property="BorderBrush" Value="#E9ECEF"/>
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="Foreground" Value="#2C3E50"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#E3F2FD"/>
                                                    <Setter Property="BorderBrush" Value="#2196F3"/>
                                                    <Setter Property="Foreground" Value="#1976D2"/>
                                                </Trigger>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="#007BFF"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                    <Setter Property="BorderBrush" Value="#0056B3"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ListBox.ItemContainerStyle>
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="👤" FontSize="16" Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}" FontWeight="SemiBold"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>

                                <!-- زر عرض الرواتب -->
                                <Button x:Name="ViewWorkerSalariesButton" Content="📄 عرض رواتب العامل المحدد"
                                        Background="#007BFF" Foreground="White"
                                        BorderThickness="0" Padding="12,8" Margin="0,8,0,0"
                                        FontWeight="Bold" FontSize="13"
                                        Click="ViewWorkerSalariesButton_Click"
                                        IsEnabled="False">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Background" Value="#007BFF"/>
                                            <Setter Property="Cursor" Value="Hand"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#0056B3"/>
                                                </Trigger>
                                                <Trigger Property="IsEnabled" Value="False">
                                                    <Setter Property="Background" Value="#6C757D"/>
                                                    <Setter Property="Cursor" Value="Arrow"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </StackPanel>
                            </Grid>
                        </Border>

                        <!-- تفاصيل الرواتب -->
                        <Border Grid.Column="1" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="2"
                                CornerRadius="8" Padding="16">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" x:Name="SelectedWorkerSalariesTitle"
                                           Text="اختر عاملاً لعرض سجل رواتبه"
                                           FontSize="16" FontWeight="Bold" Margin="0,0,0,12"
                                           Foreground="#2C3E50" HorizontalAlignment="Center"/>

                                <DataGrid Grid.Row="1" x:Name="WorkerSalariesDataGrid"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          CanUserDeleteRows="False"
                                          CanUserReorderColumns="False"
                                          CanUserResizeColumns="True"
                                          CanUserSortColumns="True"
                                          IsReadOnly="True"
                                          GridLinesVisibility="All"
                                          HeadersVisibility="Column"
                                          RowHeaderWidth="0"
                                          FontSize="12"
                                          FontFamily="Segoe UI"
                                          Background="White"
                                          BorderBrush="#E9ECEF"
                                          BorderThickness="1"
                                          AlternatingRowBackground="#F8F9FA"
                                          RowBackground="White"
                                          SelectionMode="Single"
                                          SelectionUnit="FullRow">
                                    <DataGrid.Resources>
                                        <!-- ستايل الهيدر -->
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#17A2B8"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="Padding" Value="8,6"/>
                                            <Setter Property="BorderBrush" Value="#138496"/>
                                            <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        </Style>

                                        <!-- ستايل الخلايا -->
                                        <Style TargetType="DataGridCell">
                                            <Setter Property="Padding" Value="8,4"/>
                                            <Setter Property="BorderBrush" Value="#E9ECEF"/>
                                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="#D1ECF1"/>
                                                    <Setter Property="Foreground" Value="#0C5460"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>

                                        <!-- ستايل الصفوف -->
                                        <Style TargetType="DataGridRow">
                                            <Setter Property="MinHeight" Value="35"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#E8F4F8"/>
                                                </Trigger>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="#D1ECF1"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGrid.Resources>
                                    <DataGrid.Columns>
                                        <!-- عمود تاريخ الصرف -->
                                        <DataGridTextColumn Header="📅 تاريخ الصرف" Binding="{Binding PaymentDateFormatted}" Width="100">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="Foreground" Value="#6C757D"/>
                                                    <Setter Property="Padding" Value="8,4"/>
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- عمود فترة الراتب -->
                                        <DataGridTextColumn Header="📊 فترة الراتب" Binding="{Binding PeriodFormatted}" Width="140">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                    <Setter Property="Foreground" Value="#495057"/>
                                                    <Setter Property="Padding" Value="8,4"/>
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="Background" Value="#F8F9FA"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- عمود المبلغ المصروف -->
                                        <DataGridTextColumn Header="💰 المبلغ المصروف" Binding="{Binding TotalAmountFormatted}" Width="130">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                    <Setter Property="FontSize" Value="13"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                    <Setter Property="Background" Value="#28A745"/>
                                                    <Setter Property="Padding" Value="8,4"/>
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- عمود أيام العمل -->
                                        <DataGridTextColumn Header="📊 أيام العمل" Binding="{Binding WorkDays}" Width="90">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                    <Setter Property="Foreground" Value="#007BFF"/>
                                                    <Setter Property="Padding" Value="8,4"/>
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="Background" Value="#E3F2FD"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- عمود أيام الغياب -->
                                        <DataGridTextColumn Header="❌ أيام الغياب" Binding="{Binding AbsenceDays}" Width="90">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                    <Setter Property="Foreground" Value="#DC3545"/>
                                                    <Setter Property="Padding" Value="8,4"/>
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="Background" Value="#F8D7DA"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- عمود الساعات الإضافية -->
                                        <DataGridTextColumn Header="⏰ ساعات إضافية" Binding="{Binding OvertimeHours}" Width="100">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                    <Setter Property="Foreground" Value="#FD7E14"/>
                                                    <Setter Property="Padding" Value="8,4"/>
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="Background" Value="#FFF3CD"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- عمود الخصومات -->
                                        <DataGridTextColumn Header="➖ الخصومات" Binding="{Binding DeductionsFormatted}" Width="110">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="Foreground" Value="#DC3545"/>
                                                    <Setter Property="Padding" Value="8,4"/>
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- عمود المكافآت -->
                                        <DataGridTextColumn Header="🎁 المكافآت" Binding="{Binding BonusesFormatted}" Width="110">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="Foreground" Value="#28A745"/>
                                                    <Setter Property="Padding" Value="8,4"/>
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>
                                    </DataGrid.Columns>
                                </DataGrid>

                                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                                    <Button Content="📄 تصدير إلى PDF"
                                            Background="#DC3545" Foreground="White"
                                            BorderThickness="0" Padding="16,8" Margin="0,0,12,0"
                                            FontWeight="Bold" FontSize="13"
                                            Click="ExportWorkerSalariesToPdf_Click"
                                            ToolTip="تصدير سجل رواتب العامل المحدد إلى ملف PDF">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Background" Value="#DC3545"/>
                                                <Setter Property="Cursor" Value="Hand"/>
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#C82333"/>
                                                        <Setter Property="RenderTransform">
                                                            <Setter.Value>
                                                                <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>

                                    <Button Content="📊 تصدير إلى Excel"
                                            Background="#28A745" Foreground="White"
                                            BorderThickness="0" Padding="16,8"
                                            FontWeight="Bold" FontSize="13"
                                            Click="ExportWorkerSalariesToExcel_Click"
                                            ToolTip="تصدير سجل رواتب العامل المحدد إلى ملف Excel">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Background" Value="#28A745"/>
                                                <Setter Property="Cursor" Value="Hand"/>
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#218838"/>
                                                        <Setter Property="RenderTransform">
                                                            <Setter.Value>
                                                                <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </Grid>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
